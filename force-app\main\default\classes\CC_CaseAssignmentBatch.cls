/*
 * Batch driver for Case assignment.
 *
 * Usage examples (Execute Anonymous):
 *   // Run immediately with default selection criteria
 *   Database.executeBatch(new CC_CaseAssignmentBatch(), 200);
 *
 *   // Run on a specific set of Case Ids
 *   Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ case ids  }), 100);
 *
 * Notes:
 * - Known Contact Centers: CC1, CC2, CC3 (enumerated via constant TARGET_PERCENTAGES; values not used as fallback).
 * - Uses org counters (CC_CaseUtils.getCCCounters) as baseline counts.
 * - Resolves owner queues via Custom Metadata CC_QueueMapping__mdt (CC_CaseUtils.getBaseQueueDeveloperName).
 * - This class prepares the batch infrastructure (start/execute/finish).
 * - Business selection/assignment rules can be evolved safely within execute().
 */
public with sharing class CC_CaseAssignmentBatch implements Database.Batchable<SObject>, Database.Stateful {
    private final List<Id> caseIdentifiers;
    private Map<String, Integer> countersByCenter;
    private Map<String, Decimal> percentagesByCenter;
    private Map<String, Id> queueIdByDeveloperName;

    // Fixed target distribution for Contact Centers (normalized by engine)
    private static final Map<String, Decimal> TARGET_PERCENTAGES = new Map<String, Decimal>{
        'CC1' => 20.0,
        'CC2' => 30.0,
        'CC3' => 50.0
    };

    public CC_CaseAssignmentBatch() {
        this.caseIdentifiers = null;
    }

    public CC_CaseAssignmentBatch(List<Id> caseIdentifiers) {
        this.caseIdentifiers = caseIdentifiers == null ? null : new List<Id>(caseIdentifiers);
    }

    public Database.QueryLocator start(Database.BatchableContext context) {
        // Prepare engine baseline from org counters; use fixed target shares for CC1/CC2/CC3
        countersByCenter = CC_CaseUtils.getCCCounters();
        percentagesByCenter = new Map<String, Decimal>(TARGET_PERCENTAGES);
        System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.start — countersByCenter=' + countersByCenter);
        System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.start — targetPercentages=' + percentagesByCenter);

        // Resolve all potential CC queues once (e.g., CC_Queue_*_*), cache by DeveloperName
        queueIdByDeveloperName = new Map<String, Id>();
        for (Group queueGroup : [
            SELECT Id, DeveloperName FROM Group
            WHERE Type = 'Queue' AND DeveloperName LIKE 'CC_%'
        ]) {
            queueIdByDeveloperName.put(queueGroup.DeveloperName, queueGroup.Id);
        }
        System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.start — queuesLoaded=' + queueIdByDeveloperName.size());
        // If explicit Ids are provided, limit the scope to them
        if (caseIdentifiers != null && !caseIdentifiers.isEmpty()) {
            System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.start — mode=ExplicitIds size=' + caseIdentifiers.size());
            return Database.getQueryLocator([
                SELECT Id,
                       Type,
                       SLA_Start_Date__c,
                       SLA_Expiration_Date__c,
                       AreaOfNeed__c,
                       CommercialAreasOfNeed__c,
                       Opportunity__c
                FROM Case
                WHERE Id IN :caseIdentifiers
            ]);
        }

        // Default selection: Cases parked in the contact center holding queue (if present)
        Group contactCenterParkQueueGroup = CC_CaseUtils.getCCParkQueue();
        if (contactCenterParkQueueGroup == null) {
            // Safe empty scope
            System.debug(LoggingLevel.WARN, 'CC_CaseAssignmentBatch.start — park queue not found, returning empty scope');
            return Database.getQueryLocator([SELECT Id FROM Case WHERE Id = null]);
        }
        System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.start — mode=ParkQueue groupId=' + contactCenterParkQueueGroup.Id);
        return Database.getQueryLocator([
            SELECT Id,
                   Type,
                   SLA_Start_Date__c,
                   SLA_Expiration_Date__c,
                   AreaOfNeed__c,
                   CommercialAreasOfNeed__c,
                   Opportunity__c
            FROM Case
            WHERE OwnerId = :contactCenterParkQueueGroup.Id
              AND IsClosed = false
        ]);
    }

    public void execute(Database.BatchableContext context, List<Case> caseScope) {
        if (caseScope == null || caseScope.isEmpty()) {
            return;
        }

        System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.execute — scopeSize=' + caseScope.size());
        // Step 1) Build the list of Contact Centers (developer codes)
        // Note: TARGET_PERCENTAGES is used here only to enumerate the set of centers; no fallback distribution is applied.
        List<String> contactCenterCodes = new List<String>(percentagesByCenter.keySet());
        contactCenterCodes.sort();
        System.debug(LoggingLevel.DEBUG, '[Assign] Centers=' + contactCenterCodes + ' baselineCounters=' + countersByCenter);

        // Step 2) Collect inputs for routing rules (only for derived activity 'Trattativa')
        Set<String> caseTypesForRouting = new Set<String>();
        Set<String> needScopesForRouting = new Set<String>();
        for (Case caseRecord : caseScope) {
            String derivedActivity = deriveActivity(caseRecord);
            if (derivedActivity == 'Trattativa') {
                if (!String.isBlank(caseRecord.Type)) caseTypesForRouting.add(caseRecord.Type);
                // Collect all candidate need scopes present in scope
                for (String needScopeValueCandidate : parseNeedScopes(caseRecord.CommercialAreasOfNeed__c)) {
                    needScopesForRouting.add(needScopeValueCandidate);
                }
            }
        }
        // Ensure 'Tutti' is always present to retrieve the General rule
        needScopesForRouting.add('Tutti');
        System.debug(LoggingLevel.DEBUG, '[Assign] Routing inputs — typesForRouting=' + caseTypesForRouting + ' needScopesForRouting=' + needScopesForRouting);

        // Step 3) Fetch routing rules for 'Trattativa' (if any type was collected)
        List<CC_CaseRoutingRules.RoutingRuleResponseItem> routingItems = new List<CC_CaseRoutingRules.RoutingRuleResponseItem>();
        if (!caseTypesForRouting.isEmpty()) {
            try {
                routingItems = CC_CaseRoutingRules.fetchRoutingRules(
                    'Trattativa',
                    new List<String>(caseTypesForRouting),
                    contactCenterCodes,
                    new List<String>(needScopesForRouting)
                );
            } catch (Exception routingFetchException) {
                System.debug(LoggingLevel.WARN, 'Routing rules fetch failed; proceeding without routing percentages. No fallback to fixed distribution. error=' + routingFetchException.getMessage());
                routingItems = new List<CC_CaseRoutingRules.RoutingRuleResponseItem>();
            }
        } else {
            System.debug(LoggingLevel.DEBUG, '[Assign] No Trattativa types found in scope; skipping routing fetch.');
        }
        System.debug(LoggingLevel.DEBUG, '[Assign] Routing items returned=' + routingItems.size());

        // Step 3.b) Fetch SLA rules to compute SLA days per (Type|Need Scope)
        // Gather all Types and Need Scopes present in scope (independent from Trattativa) + ensure 'Tutti'
        Set<String> caseTypesForSla = new Set<String>();
        Set<String> needScopesForSla = new Set<String>();
        for (Case caseRecord : caseScope) {
            if (!String.isBlank(caseRecord.Type)) caseTypesForSla.add(caseRecord.Type);
            for (String scopeCandidate : parseNeedScopes(caseRecord.CommercialAreasOfNeed__c)) {
                needScopesForSla.add(scopeCandidate);
            }
        }
        needScopesForSla.add('Tutti');
        Map<String, Integer> slaDaysByTypeAndScope = new Map<String, Integer>();
        if (!caseTypesForSla.isEmpty()) {
            try {
                List<CC_CaseSlaRules.SlaRuleResponseItem> slaRuleItems = CC_CaseSlaRules.fetchSlaRules(
                    'CaseSLA',
                    new List<String>(caseTypesForSla),
                    new List<String>(needScopesForSla),
                    new List<String>{ 'Generale', 'Eccezione' }
                );
                for (CC_CaseSlaRules.SlaRuleResponseItem slaItem : slaRuleItems) {
                    if (slaItem == null) continue;
                    if (String.isBlank(slaItem.type) || String.isBlank(slaItem.need_scope)) continue;
                    if (slaItem.sla_days == null) continue; // defer fallback at usage time
                    String typeAndScopeKeyForSla = slaItem.type + '|' + slaItem.need_scope;
                    // Prefer the lowest SLA days if multiple entries appear for safety
                    Integer currentSlaDays = slaDaysByTypeAndScope.get(typeAndScopeKeyForSla);
                    slaDaysByTypeAndScope.put(
                        typeAndScopeKeyForSla,
                        (currentSlaDays == null || slaItem.sla_days < currentSlaDays) ? slaItem.sla_days : currentSlaDays
                    );
                }
                System.debug(LoggingLevel.DEBUG, '[Assign] SLA rules items returned=' + slaRuleItems.size() + ' mappedKeys=' + slaDaysByTypeAndScope.keySet().size());
            } catch (Exception slaFetchException) {
                System.debug(LoggingLevel.WARN, '[Assign] SLA rules fetch failed; will use default SLA days. error=' + slaFetchException.getMessage());
            }
        } else {
            System.debug(LoggingLevel.DEBUG, '[Assign] No Types found in scope; skipping SLA rules fetch.');
        }

        // Step 4) Build indexes from routing results:
        // - assignmentPercentageByTypeAndNeedScopeAndCenter: percentages per (type|scope)/center
        // - explicitPriorityByTypeAndNeedScope: explicit priority per (type|scope)
        // - minExplicitGeneralPriorityByCaseType: min explicit priority among GENERAL rules for each type
        Map<String, Map<String, Integer>> assignmentPercentageByTypeAndNeedScopeAndCenter = new Map<String, Map<String, Integer>>();
        Map<String, Integer> explicitPriorityByTypeAndNeedScope = new Map<String, Integer>();
        Map<String, Integer> minExplicitGeneralPriorityByCaseType = new Map<String, Integer>();
        for (CC_CaseRoutingRules.RoutingRuleResponseItem routingItem : routingItems) {
            if (routingItem == null) continue;
            String typeValue = routingItem.type;
            String needScopeValue = routingItem.need_scope;
            String contactCenterCode = routingItem.contact_center;
            if (String.isBlank(typeValue) || String.isBlank(needScopeValue) || String.isBlank(contactCenterCode)) continue;
            String typeAndScopeKey = typeValue + '|' + needScopeValue;
            // Percentages aggregation per (type|scope)/center
            if (!assignmentPercentageByTypeAndNeedScopeAndCenter.containsKey(typeAndScopeKey)) {
                assignmentPercentageByTypeAndNeedScopeAndCenter.put(typeAndScopeKey, new Map<String, Integer>());
            }
            if (routingItem.assignment_percentage != null) {
                assignmentPercentageByTypeAndNeedScopeAndCenter.get(typeAndScopeKey).put(contactCenterCode, routingItem.assignment_percentage);
            }
            // Explicit priority (min across centers for the same (type|scope))
            if (routingItem.priority != null) {
                Integer currentMinForPair = explicitPriorityByTypeAndNeedScope.get(typeAndScopeKey);
                explicitPriorityByTypeAndNeedScope.put(
                    typeAndScopeKey,
                    (currentMinForPair == null || routingItem.priority < currentMinForPair) ? routingItem.priority : currentMinForPair
                );
                // Track min explicit priority only among GENERAL rules for the Type
                Boolean isGeneralRule = (needScopeValue == 'Tutti') || (routingItem.rule_type == 'Generale');
                if (isGeneralRule) {
                    Integer currentMinForTypeGeneral = minExplicitGeneralPriorityByCaseType.get(typeValue);
                    minExplicitGeneralPriorityByCaseType.put(
                        typeValue,
                        (currentMinForTypeGeneral == null || routingItem.priority < currentMinForTypeGeneral) ? routingItem.priority : currentMinForTypeGeneral
                    );
                }
            }
        }
        // Step 5) Compute effective priority per (type|scope):
        // explicit priority if present; otherwise min(General by type) + 1; otherwise fallback to base type ranking.
        // Important: iterate on the union of pairs to include couples with only explicit priority and no percentages.
        Map<String, Integer> effectivePriorityByTypeAndNeedScope = new Map<String, Integer>();
        Set<String> allTypeAndScopeKeys = new Set<String>();
        allTypeAndScopeKeys.addAll(assignmentPercentageByTypeAndNeedScopeAndCenter.keySet());
        allTypeAndScopeKeys.addAll(explicitPriorityByTypeAndNeedScope.keySet());
        for (String typeAndScopeKey : allTypeAndScopeKeys) {
            Integer explicitPriority = explicitPriorityByTypeAndNeedScope.get(typeAndScopeKey);
            if (explicitPriority != null) {
                effectivePriorityByTypeAndNeedScope.put(typeAndScopeKey, explicitPriority);
            } else {
                // Extract type from composite key
                List<String> keyParts = typeAndScopeKey.split('\\|');
                String typeFromKey = (keyParts.size() > 0) ? keyParts[0] : null;
                Integer minGeneralExplicitPriorityForType = (typeFromKey == null) ? null : minExplicitGeneralPriorityByCaseType.get(typeFromKey);
                if (minGeneralExplicitPriorityForType != null) {
                    effectivePriorityByTypeAndNeedScope.put(typeAndScopeKey, minGeneralExplicitPriorityForType + 1);
                } else {
                    Integer baseRank = getBaseTypeRank(typeFromKey);
                    if (baseRank != null) {
                        // Use a coarse fallback to preserve Type-driven precedence; larger gaps keep room for future refinements
                        effectivePriorityByTypeAndNeedScope.put(typeAndScopeKey, baseRank * 1000 + 1);
                    }
                }
            }
        }
        System.debug(LoggingLevel.DEBUG, '[Assign] Pairs with percentages=' + assignmentPercentageByTypeAndNeedScopeAndCenter.keySet().size() +
            ' pairsWithExplicitPriority=' + explicitPriorityByTypeAndNeedScope.keySet().size() +
            ' effectivePairs=' + effectivePriorityByTypeAndNeedScope.keySet().size());

        // Group cases by profile (type + chosen Need Scope) and assign by bucket with dynamic percentages
        // Step 6) Group cases by profile (type + chosen Need Scope) and store chosen scope per Case
        Map<String, List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem>> assignmentInputItemsByProfileKey = new Map<String, List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem>>();
        Map<String, String> chosenNeedScopeByCaseId = new Map<String, String>();
        Integer fallbackChosenScopeToGeneralCount = 0;
        for (Case caseRecord : caseScope) {
            String typeValue = caseRecord.Type;
            if (String.isBlank(typeValue)) {
                // Group under default profile with General scope when Type is missing
                String profileKeyWithoutType = '|' + 'Tutti';
                if (!assignmentInputItemsByProfileKey.containsKey(profileKeyWithoutType)) {
                    assignmentInputItemsByProfileKey.put(profileKeyWithoutType, new List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem>());
                }
                assignmentInputItemsByProfileKey.get(profileKeyWithoutType).add(new CC_CaseAssignmentWrapper.CaseAssignmentInputItem(caseRecord.Id));
                chosenNeedScopeByCaseId.put(caseRecord.Id, 'Tutti');
                continue;
            }
            Boolean isTrattativa = (deriveActivity(caseRecord) == 'Trattativa');
            String chosenNeedScope = 'Tutti';
            if (isTrattativa) {
                // Choose the best scope among candidates using effective priority
                List<String> candidateNeedScopes = parseNeedScopes(caseRecord.CommercialAreasOfNeed__c);
                if (candidateNeedScopes.isEmpty()) candidateNeedScopes.add('Tutti');
                Integer bestPriority = null;
                String bestScope = null;
                for (String candidateScope : candidateNeedScopes) {
                    String typeAndScopeKeyCandidate = typeValue + '|' + candidateScope;
                    Integer effectivePriorityValue = effectivePriorityByTypeAndNeedScope.get(typeAndScopeKeyCandidate);
                    if (effectivePriorityValue == null) {
                        // No priority known for this pair; consider as lower precedence than any numeric
                        // but still allow tie-break later
                    }
                    if (bestScope == null ||
                        (effectivePriorityValue != null && (bestPriority == null || effectivePriorityValue < bestPriority)) ||
                        (effectivePriorityValue == bestPriority && tieBreakScope(candidateScope, bestScope))
                    ) {
                        bestScope = candidateScope;
                        bestPriority = effectivePriorityValue;
                    }
                }
                if (bestScope != null) chosenNeedScope = bestScope;
                // If the chosen exception scope has no routing percentages, fall back to General ('Tutti') if available for this Type
                if (!hasRoutingPercentagesFor(assignmentPercentageByTypeAndNeedScopeAndCenter, percentagesByCenter, typeValue, chosenNeedScope) &&
                    hasRoutingPercentagesFor(assignmentPercentageByTypeAndNeedScopeAndCenter, percentagesByCenter, typeValue, 'Tutti')) {
                    fallbackChosenScopeToGeneralCount++;
                    System.debug(LoggingLevel.DEBUG, '[Assign] Fallback caseId=' + caseRecord.Id + ' type=' + typeValue + ' chosenScope=' + chosenNeedScope + ' -> Tutti (no percentages for exception).');
                    chosenNeedScope = 'Tutti';
                }
            } else {
                chosenNeedScope = 'Tutti';
            }
            String profileKey = typeValue + '|' + chosenNeedScope;
            if (!assignmentInputItemsByProfileKey.containsKey(profileKey)) {
                assignmentInputItemsByProfileKey.put(profileKey, new List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem>());
            }
            assignmentInputItemsByProfileKey.get(profileKey).add(new CC_CaseAssignmentWrapper.CaseAssignmentInputItem(caseRecord.Id));
            chosenNeedScopeByCaseId.put(caseRecord.Id, chosenNeedScope);
        }
        System.debug(LoggingLevel.DEBUG, '[Assign] Profiles to process=' + assignmentInputItemsByProfileKey.keySet().size() + ' => ' + assignmentInputItemsByProfileKey.keySet());
        if (fallbackChosenScopeToGeneralCount > 0) {
            System.debug(LoggingLevel.DEBUG, '[Assign] Scope selection fallbacks to General (Tutti) applied for cases=' + fallbackChosenScopeToGeneralCount);
        }

        // Execute per-bucket assignment with dynamic percentages and carry counters forward
        CC_CaseAssignmentWrapper.CaseAssignmentResult assignmentResult = new CC_CaseAssignmentWrapper.CaseAssignmentResult();
        // Step 7) Execute per-bucket assignment with dynamic percentages and carry counters forward
        Integer processedBuckets = 0;
        Integer skippedBuckets = 0;
        for (String profileKey : assignmentInputItemsByProfileKey.keySet()) {
            List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem> profileInputItems = assignmentInputItemsByProfileKey.get(profileKey);
            if (profileInputItems == null || profileInputItems.isEmpty()) continue;
            List<String> profileKeyParts = profileKey.split('\\|');
            String typeValue = (profileKeyParts.size() > 0) ? profileKeyParts[0] : null;
            String needScopeValue = (profileKeyParts.size() > 1) ? profileKeyParts[1] : 'Tutti';
            // Build per-profile percentages, one entry per CC (missing CC default to 0)
            Map<String, Decimal> bucketPercentages = new Map<String, Decimal>();
            Map<String, Integer> rawPercentagesByCenter = assignmentPercentageByTypeAndNeedScopeAndCenter.get(typeValue + '|' + needScopeValue);
            Decimal sumOfPercentages = 0;
            for (String contactCenterCode : percentagesByCenter.keySet()) {
                Integer percentageValue = (rawPercentagesByCenter == null) ? null : rawPercentagesByCenter.get(contactCenterCode);
                Decimal percentageAsDecimal = (percentageValue == null) ? 0 : Decimal.valueOf(percentageValue);
                bucketPercentages.put(contactCenterCode, percentageAsDecimal);
                sumOfPercentages += percentageAsDecimal;
            }
            if (sumOfPercentages <= 0) {
                // Attempt fallback to General percentages for the same Type
                Map<String, Integer> rawGeneralPercentagesByCenter = assignmentPercentageByTypeAndNeedScopeAndCenter.get(typeValue + '|' + 'Tutti');
                Decimal sumOfGeneralPercentages = 0;
                Map<String, Decimal> generalPercentages = new Map<String, Decimal>();
                if (rawGeneralPercentagesByCenter != null) {
                    for (String contactCenterCode : percentagesByCenter.keySet()) {
                        Integer percentageValue = rawGeneralPercentagesByCenter.get(contactCenterCode);
                        Decimal percentageAsDecimal = (percentageValue == null) ? 0 : Decimal.valueOf(percentageValue);
                        generalPercentages.put(contactCenterCode, percentageAsDecimal);
                        sumOfGeneralPercentages += percentageAsDecimal;
                    }
                }
                if (sumOfGeneralPercentages > 0) {
                    System.debug(LoggingLevel.WARN, '[Assign] Fallback to General for profile=' + profileKey + ' size=' + profileInputItems.size() + ' — using percentages from ' + typeValue + '|Tutti');
                    bucketPercentages = generalPercentages;
                } else {
                    skippedBuckets++;
                    System.debug(LoggingLevel.WARN, '[Assign] Skipping bucket profile=' + profileKey + ' size=' + profileInputItems.size() + ' — no routing percentages available (neither exception nor general).');
                    continue; // do not assign this bucket without routing config
                }
            }
            // Snapshot counters before assignment for delta logging
            Map<String, Integer> countersBeforeBucket = new Map<String, Integer>(countersByCenter);
            System.debug(LoggingLevel.DEBUG, '[Assign] Bucket start profile=' + profileKey + ' size=' + profileInputItems.size() + ' percentages=' + bucketPercentages + ' countersBefore=' + countersBeforeBucket);
            CC_CaseAssignmentEngine.State assignmentEngineStateForBucket = CC_CaseAssignmentEngine.buildState(
                bucketPercentages,
                countersByCenter,
                0
            );
            CC_CaseAssignmentEngine assignmentEngineForBucket = new CC_CaseAssignmentEngine(assignmentEngineStateForBucket);
            CC_CaseAssignmentWrapper.CaseAssignmentResult assignmentResultForBucket = assignmentEngineForBucket.assign(profileInputItems);
            // Merge decisions into global result
            for (CC_CaseAssignmentWrapper.CaseAssignmentDecision decisionFromBucket : assignmentResultForBucket.decisions) {
                assignmentResult.decisions.add(decisionFromBucket);
            }
            // Update counters for next bucket
            if (assignmentResultForBucket.finalAssignedCounts != null) {
                countersByCenter = new Map<String, Integer>(assignmentResultForBucket.finalAssignedCounts);
            }
            processedBuckets++;
            // Log delta
            Map<String, Integer> countersAfterBucket = new Map<String, Integer>(countersByCenter);
            Map<String, Integer> deltaByCenter = new Map<String, Integer>();
            for (String centerCode : countersAfterBucket.keySet()) {
                Integer before = countersBeforeBucket.get(centerCode);
                Integer after = countersAfterBucket.get(centerCode);
                deltaByCenter.put(centerCode, (after == null ? 0 : after) - (before == null ? 0 : before));
            }
            System.debug(LoggingLevel.DEBUG, '[Assign] Bucket end profile=' + profileKey + ' decisions=' + assignmentResultForBucket.decisions.size() + ' countersAfter=' + countersAfterBucket + ' delta=' + deltaByCenter);
        }
        // expose final counters for potential carry-over logging
        assignmentResult.finalAssignedCounts = new Map<String, Integer>(countersByCenter);
        // Build decisions per center summary
        Map<String, Integer> decisionsPerCenter = new Map<String, Integer>();
        for (CC_CaseAssignmentWrapper.CaseAssignmentDecision decisionItem : assignmentResult.decisions) {
            String centerCode = decisionItem.targetCenterCode;
            decisionsPerCenter.put(centerCode, (decisionsPerCenter.containsKey(centerCode) ? decisionsPerCenter.get(centerCode) : 0) + 1);
        }
        System.debug(LoggingLevel.DEBUG, '[Assign] Execute summary — processedBuckets=' + processedBuckets + ' skippedBuckets=' + skippedBuckets + ' decisions=' + assignmentResult.decisions.size() + ' byCenter=' + decisionsPerCenter + ' finalCounters=' + countersByCenter);

        // Map scope Cases by Id to access Type in Owner queue resolution
        Map<Id, Case> scopeCasesById = new Map<Id, Case>();
        for (Case caseRecord : caseScope) {
            scopeCasesById.put(caseRecord.Id, caseRecord);
        }

        // Derive activity (temporary business rule):
        // if Opportunity__c != null => Activity = 'Trattativa' (only expected activity for now)
        Map<Id, String> derivedActivityByCaseId = new Map<Id, String>();
        for (Case caseRecord : caseScope) {
            String activity = deriveActivity(caseRecord);
            if (activity != null) {
                derivedActivityByCaseId.put(caseRecord.Id, activity);
            }
        }
        System.debug(LoggingLevel.DEBUG, 'Derived activities (count)=' + derivedActivityByCaseId.size());

        List<Case> casesToUpdate = new List<Case>();
        for (CC_CaseAssignmentWrapper.CaseAssignmentDecision decision : assignmentResult.decisions) {
            Case relatedCase = scopeCasesById.get(decision.caseId);
            if (relatedCase == null) continue;
            Case updated = new Case(Id = relatedCase.Id);
            updated.CC_ContactCenter__c = decision.targetCenterCode;
            // Optional: trace derived activity for future routing integration
            String derivedActivity = derivedActivityByCaseId.get(relatedCase.Id);
            if (derivedActivity != null) {
                System.debug(LoggingLevel.DEBUG, 'Derived activity for caseId=' + relatedCase.Id + ' => ' + derivedActivity);
            }
            String chosenNeedScope = chosenNeedScopeByCaseId.get(relatedCase.Id);
            if (chosenNeedScope != null) {
                System.debug(LoggingLevel.DEBUG, 'Chosen Need Scope for caseId=' + relatedCase.Id + ' => ' + chosenNeedScope);
            }
            // SLA fields population
            Date slaStart = relatedCase.SLA_Start_Date__c == null ? Date.today() : relatedCase.SLA_Start_Date__c;
            updated.SLA_Start_Date__c = slaStart;
            if (relatedCase.SLA_Expiration_Date__c == null) {
                // Compute SLA days from rules (Type + chosen Need Scope), fallback to General, then to default
                String typeValue = relatedCase.Type;
                String scopeValue = chosenNeedScopeByCaseId.get(relatedCase.Id);
                Integer slaDays = null;
                if (!String.isBlank(typeValue)) {
                    if (!String.isBlank(scopeValue)) {
                        slaDays = slaDaysByTypeAndScope.get(typeValue + '|' + scopeValue);
                    }
                    if (slaDays == null) {
                        slaDays = slaDaysByTypeAndScope.get(typeValue + '|' + 'Tutti');
                    }
                }
                if (slaDays == null) slaDays = CC_CaseSlaUtils.DEFAULT_SLA_DAYS;
                updated.SLA_Expiration_Date__c = slaStart.addDays(slaDays);
                System.debug(LoggingLevel.DEBUG, '[Assign] SLA compute — caseId=' + relatedCase.Id + ' type=' + typeValue + ' scope=' + scopeValue + ' slaDays=' + slaDays + ' start=' + String.valueOf(slaStart) + ' expiration=' + String.valueOf(updated.SLA_Expiration_Date__c));
            }

            String baseQueueDeveloperName = CC_CaseUtils.getBaseQueueDeveloperName(relatedCase.Type);
            if (!String.isBlank(baseQueueDeveloperName)) {
                String queueDeveloperName = baseQueueDeveloperName + '_' + decision.targetCenterCode + '_Case';
                Id queueId = queueIdByDeveloperName.get(queueDeveloperName);
                if (queueId != null) {
                    updated.OwnerId = queueId;
                    System.debug(LoggingLevel.DEBUG, 'Assign decision — caseId=' + updated.Id + ' center=' + decision.targetCenterCode + ' ownerQueue=' + queueDeveloperName + ' queueId=' + queueId);
                }
                else {
                    System.debug(LoggingLevel.WARN, 'Assign decision — queue not found for caseId=' + updated.Id + ' expectedDeveloperName=' + queueDeveloperName);
                }
            } else {
                System.debug(LoggingLevel.WARN, 'Assign decision — no mapping for Case.Type=' + relatedCase.Type + '; OwnerId unchanged');
            }
            casesToUpdate.add(updated);
        }

        if (!casesToUpdate.isEmpty()) {
            Database.SaveResult[] updateResults = Database.update(casesToUpdate, false);
            Integer failures = 0;
            for (Database.SaveResult saveResult : updateResults) {
                if (!saveResult.isSuccess()) {
                    failures++;
                    for (Database.Error error : saveResult.getErrors()) {
                        System.debug(LoggingLevel.ERROR, 'Update failure — caseId=' + saveResult.getId() + ' msg=' + error.getMessage());
                    }
                }
            }
            System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.execute — updatedCases=' + casesToUpdate.size() + ' failures=' + failures);
        }

        // Carry forward updated counters across chunks
        if (assignmentResult.finalAssignedCounts != null) {
            countersByCenter = new Map<String, Integer>(assignmentResult.finalAssignedCounts);
            System.debug(LoggingLevel.DEBUG, 'CC_CaseAssignmentBatch.execute — counters updated=' + countersByCenter);
        }
    }

    public void finish(Database.BatchableContext context) {
        System.debug(LoggingLevel.INFO, 'CC_CaseAssignmentBatch.finish — completed');
    }

    // Queue mapping is provided by CC_CaseUtils via Custom Metadata

    // Helper: derive activity name from Case (temporary business rule)
    private static String deriveActivity(Case caseRecord) {
        if (caseRecord == null) return null;
        return (caseRecord.Opportunity__c != null) ? 'Trattativa' : null;
    }

    // Helper: split Salesforce MultiselectPicklist (semicolon-separated)
    private static List<String> parseNeedScopes(String multiSelectValue) {
        List<String> result = new List<String>();
        if (String.isBlank(multiSelectValue)) return result;
        for (String token : multiSelectValue.split(';')) {
            if (!String.isBlank(token)) result.add(token.trim());
        }
        return result;
    }

    // Helper: tie-break among two scopes with same priority -> prefer Eccezione (not 'Tutti'), then alphabetical
    private static Boolean tieBreakScope(String candidateScope, String currentBestScope) {
        if (String.isBlank(currentBestScope)) return true;
        Boolean candidateIsException = (candidateScope != 'Tutti');
        Boolean currentBestIsException = (currentBestScope != 'Tutti');
        if (candidateIsException && !currentBestIsException) return true;
        if (!candidateIsException && currentBestIsException) return false;
        return candidateScope < currentBestScope;
    }

    // Helper: verify that there is at least one non-zero routing percentage for (Type|Need Scope)
    private static Boolean hasRoutingPercentagesFor(
        Map<String, Map<String, Integer>> assignmentPercentageByTypeAndNeedScopeAndCenter,
        Map<String, Decimal> contactCentersReference,
        String typeValue,
        String needScopeValue
    ) {
        if (String.isBlank(typeValue) || String.isBlank(needScopeValue)) return false;
        String typeAndNeedScopeKey = typeValue + '|' + needScopeValue;
        Map<String, Integer> rawPercentagesByCenter = assignmentPercentageByTypeAndNeedScopeAndCenter.get(typeAndNeedScopeKey);
        if (rawPercentagesByCenter == null) return false;
        for (String centerCode : contactCentersReference.keySet()) {
            Integer percentageValue = rawPercentagesByCenter.get(centerCode);
            if (percentageValue != null && percentageValue != 0) return true;
        }
        return false;
    }

    // Helper: base precedence among Types when no explicit/general priority exists.
    private static Integer getBaseTypeRank(String caseType) {
        if (String.isBlank(caseType)) return null;
        // Lower rank means higher priority
        if (caseType == 'CallMeBack') return 1;
        if (caseType == 'Acquisto non concluso') return 2;
        if (caseType == 'Salva Preventivo') return 3;
        return 999; // unknown types at the end
    }
}
