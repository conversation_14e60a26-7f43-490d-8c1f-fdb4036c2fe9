@isTest
private class CC_CaseAssignmentBatchSlaTest {
  @isTest
  static void testBatchComputesSlaDaysFromRules() {
    // Prepare routing queues and target queue (CallMeBack + CC3)
    Group q = new Group(Name = 'Queue CallMeBack CC3', DeveloperName = 'CC_Queue_CallMeBack_CC3_Case', Type = 'Queue');
    insert q;
    insert new QueueSobject(QueueId = q.Id, SobjectType = 'Case');

    // Stub routing rules to direct 100% to CC3 for CallMeBack|Tutti
    List<CC_CaseRoutingRules.RoutingRuleResponseItem> routingStub = new List<CC_CaseRoutingRules.RoutingRuleResponseItem>();
    // Order in fetchRoutingRules: type -> center -> scope
    // CC1
    CC_CaseRoutingRules.RoutingRuleResponseItem r1 = new CC_CaseRoutingRules.RoutingRuleResponseItem();
    r1.rule_id = 'R_CC1'; r1.assignment_percentage = 0; r1.priority = 1;
    routingStub.add(r1);
    // CC2
    CC_CaseRoutingRules.RoutingRuleResponseItem r2 = new CC_CaseRoutingRules.RoutingRuleResponseItem();
    r2.rule_id = 'R_CC2'; r2.assignment_percentage = 0; r2.priority = 1;
    routingStub.add(r2);
    // CC3
    CC_CaseRoutingRules.RoutingRuleResponseItem r3 = new CC_CaseRoutingRules.RoutingRuleResponseItem();
    r3.rule_id = 'R_CC3'; r3.assignment_percentage = 100; r3.priority = 1;
    routingStub.add(r3);
    CC_CaseRoutingRules.testStubbedResponse = routingStub;

    // Stub SLA rules: for CallMeBack|Tutti -> sla_days = 5
    List<CC_CaseSlaRules.SlaRuleResponseItem> slaStub = new List<CC_CaseSlaRules.SlaRuleResponseItem>();
    CC_CaseSlaRules.SlaRuleResponseItem s0 = new CC_CaseSlaRules.SlaRuleResponseItem();
    s0.rule_id = 'S_General'; s0.priority = 2; s0.sla_days = 5;
    slaStub.add(s0);
    // Eccezione (same scope here, unused)
    CC_CaseSlaRules.SlaRuleResponseItem s1 = new CC_CaseSlaRules.SlaRuleResponseItem();
    s1.rule_id = 'S_Exception'; s1.priority = 3; // no sla_days -> ignored
    slaStub.add(s1);
    CC_CaseSlaRules.testStubbedResponse = slaStub;

    // Create an Opportunity to derive activity = 'Trattativa'
    Opportunity opp = new Opportunity(Name = 'Opp', CloseDate = Date.today().addDays(10), StageName = 'Prospecting');
    insert opp;

    // Prepare Case to assign
    Case c = new Case(Subject = 'Compute SLA', Origin = 'Phone', Status = 'New', Type = 'CallMeBack', Opportunity__c = opp.Id);
    insert c;

    Test.startTest();
    Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ c.Id }), 1);
    Test.stopTest();

    Case reloaded = [SELECT OwnerId, CC_ContactCenter__c, SLA_Start_Date__c, SLA_Expiration_Date__c FROM Case WHERE Id = :c.Id LIMIT 1];
    System.assertEquals(q.Id, reloaded.OwnerId, 'Owner must be assigned to CC3 queue');
    System.assertEquals('CC3', reloaded.CC_ContactCenter__c, 'Contact Center code must be CC3');
    System.assertEquals(Date.today(), reloaded.SLA_Start_Date__c, 'SLA start defaults to today');
    System.assertEquals(Date.today().addDays(5), reloaded.SLA_Expiration_Date__c, 'SLA expiration must use sla_days from rules');
  }
}

