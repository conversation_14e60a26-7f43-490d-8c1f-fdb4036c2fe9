@isTest
private class CC_CaseAssignmentBatchTest {
    @isTest
    static void testBatchRunsOnProvidedIds() {
        // Prepare sample Cases
        Case caseOne = new Case(Subject = 'Assign Me 1', Origin = 'Phone', Status = 'New');
        Case caseTwo = new Case(Subject = 'Assign Me 2', Origin = 'Web', Status = 'New');
        insert new List<Case>{ caseOne, caseTwo };

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ caseOne.Id, caseTwo.Id }), 50);
        Test.stopTest();

        // If we reach here without exceptions, the batch infrastructure is sound.
        System.assert(true, 'Batch executed without exceptions');
    }

    @isTest
    static void testBatchWithDefaultSelectionNoop() {
        // No queue or matching records are created; start should return empty scope
        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(), 50);
        Test.stopTest();

        System.assert(true, 'Default batch execution completed');
    }

    @isTest
    static void testBatchUpdatesContactCenterAndOwner() {
        // Prepare Queue for Type 'CallMeBack' and center CC3 (expected first pick)
        Group queue = new Group(Name = 'Queue CallMeBack CC3', DeveloperName = 'CC_Queue_CallMeBack_CC3_Case', Type = 'Queue');
        insert queue;
        insert new QueueSobject(QueueId = queue.Id, SobjectType = 'Case');

        // Prepare a Case to be assigned
        Case toAssign = new Case(Subject = 'Assign Owner', Origin = 'Phone', Status = 'New', Type = 'CallMeBack');
        insert toAssign;

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ toAssign.Id }), 1);
        Test.stopTest();

        Case reloaded = [SELECT CC_ContactCenter__c, OwnerId, Type FROM Case WHERE Id = :toAssign.Id LIMIT 1];
        System.assertEquals('CC3', reloaded.CC_ContactCenter__c, 'Contact Center must be set to CC3');
        System.assertEquals(queue.Id, reloaded.OwnerId, 'Owner must be set to the CallMeBack+CC3 queue');
    }

    @isTest
    static void testBatchMappingWithSpacedType() {
        // Uses existing CMDT mapping: 'Acquisto non concluso' -> 'CC_Queue_AcquistoNonConcluso'
        Group queue = new Group(Name = 'Queue AcquistoNonConcluso CC3', DeveloperName = 'CC_Queue_AcquistoNonConcluso_CC3_Case', Type = 'Queue');
        insert queue;
        insert new QueueSobject(QueueId = queue.Id, SobjectType = 'Case');

        Case toAssign = new Case(Subject = 'Assign Owner with spaced Type', Origin = 'Phone', Status = 'New', Type = 'Acquisto non concluso');
        insert toAssign;

        Test.startTest();
        Database.executeBatch(new CC_CaseAssignmentBatch(new List<Id>{ toAssign.Id }), 1);
        Test.stopTest();

        Case reloaded = [SELECT CC_ContactCenter__c, OwnerId FROM Case WHERE Id = :toAssign.Id LIMIT 1];
        System.assertEquals('CC3', reloaded.CC_ContactCenter__c, 'Contact Center must be set to CC3');
        System.assertEquals(queue.Id, reloaded.OwnerId, 'Owner must be set to the AcquistoNonConcluso+CC3 queue');
    }
}
