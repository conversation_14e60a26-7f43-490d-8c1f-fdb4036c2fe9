// Engine proporzionale per l'assegnazione dei Case: solo logica, nessuna derivazione interna
public with sharing class CC_CaseAssignmentEngine {
  /**
   * Eccezione custom per segnalare errori bloccanti di configurazione / stato.
   * Evitiamo AuraHandledException nell'engine core per distinguere chiaramente i problemi di dominio.
   */
  public class InvalidStateException extends Exception {
  }
  /**
   * Rappresenta la configurazione di un singolo centro (Contact Center) con:
   * - centerCode: identificativo logico del centro (es. CC1)
   * - percentage: quota target (può essere grezza; l'engine normalizza la somma a ≈1)
   * - assignedCount: conteggio storico iniziale (baseline) + assegnazioni incrementalmente aggiornate dall'engine
   */
  public class CenterConfiguration {
    public String centerCode;
    public Decimal percentage; // Somma percentuali di tutti i centri ≈ 1 dopo normalizzazione
    public Integer assignedCount; // Baseline iniziale (storico) + incrementi
    public CenterConfiguration(
      String centerCode,
      Decimal percentage,
      Integer assignedCount
    ) {
      this.centerCode = centerCode;
      this.percentage = percentage;
      this.assignedCount = assignedCount;
    }
  }

  /**
   * Stato operativo esterno fornito al costruttore:
   * - initialTotal: somma di tutti gli assignedCount iniziali (baseline)
   * - globalStep: numero di assegnazioni eseguite in questa run (incrementa ad ogni decisione)
   * - centersByCode: lookup rapido centro -> configurazione
   * - orderedCenters: lista ordinata per applicare tie-break deterministico
   */
  public class State {
    public Integer initialTotal;
    public Integer globalStep;
    public Map<String, CenterConfiguration> centersByCode;
    public List<CenterConfiguration> orderedCenters;
  }

  // Stato interno dell'engine (mutabile durante la run di assegnazione)
  private State engineState;

  public CC_CaseAssignmentEngine(State providedState) {
    this.engineState = providedState; // reference (side-effect voluto)
    ensureCentersLookupBuilt();
    normalizePercentages(this.engineState); // normalizzazione automatica
  }

  /**
   * Costruttore overload: consente di istanziare l'engine passando solo
   * - mappa percentuali per centro (grezze o già normalizzate)
   * - mappa conteggi iniziali per centro (opzionale; default 0 se assente)
   * - globalStep iniziale (opzionale; default 0)
   * Eliminando la necessità di costruire esternamente lo State completo.
   */
  public CC_CaseAssignmentEngine(
    Map<String, Decimal> percentagesByCenter,
    Map<String, Integer> initialCountsByCenter,
    Integer globalStep
  ) {
    this(buildState(percentagesByCenter, initialCountsByCenter, globalStep));
  }

  /**
   * Builder statico di State a partire da mappe (API semplificata per consumer esterni):
   * - Ordine deterministico: ordinamento alfabetico dei centerCode per garantire tie-break stabile.
   * - initialTotal calcolato come somma dei conteggi iniziali (0 se assenti).
   * - centersByCode lasciato null: sarà costruito dal costruttore principale.
   */
  public static State buildState(
    Map<String, Decimal> percentagesByCenter,
    Map<String, Integer> initialCountsByCenter,
    Integer globalStep
  ) {
    if (percentagesByCenter == null || percentagesByCenter.isEmpty()) {
      throw new InvalidStateException('percentagesByCenter nullo/vuoto');
    }
    State state = new State();
    state.globalStep = (globalStep == null) ? 0 : globalStep;
    state.orderedCenters = new List<CenterConfiguration>();
    Integer total = 0;
    // Ordine deterministico
    List<String> codes = new List<String>(percentagesByCenter.keySet());
    codes.sort();
    for (String code : codes) {
      Decimal percentage = percentagesByCenter.get(code);
      Integer count = (initialCountsByCenter != null &&
        initialCountsByCenter.containsKey(code))
        ? initialCountsByCenter.get(code)
        : 0;
      state.orderedCenters.add(new CenterConfiguration(code, percentage, count));
      total += count;
    }
    state.initialTotal = total;
    // centersByCode verrà popolato da ensureCentersLookupBuilt() nel costruttore principale
    return state;
  }

  private void ensureCentersLookupBuilt() {
    if (engineState == null) {
      return;
    }
    if (
      engineState.centersByCode != null && !engineState.centersByCode.isEmpty()
    ) {
      return; // già presente
    }
    engineState.centersByCode = new Map<String, CenterConfiguration>();
    if (engineState.orderedCenters == null) {
      return; // validazione segnalerà
    }
    for (CenterConfiguration configuration : engineState.orderedCenters) {
      validateCenterSkeleton(configuration);
      if (engineState.centersByCode.containsKey(configuration.centerCode)) {
        throw new InvalidStateException(
          'centerCode duplicato: ' + configuration.centerCode
        );
      }
      engineState.centersByCode.put(configuration.centerCode, configuration);
    }
  }

  private static void validateCenterSkeleton(CenterConfiguration configuration) {
    if (configuration == null) {
      throw new InvalidStateException('Center null in orderedCenters');
    }
    if (String.isBlank(configuration.centerCode)) {
      throw new InvalidStateException('centerCode mancante');
    }
  }

  /**
   * Validazione opzionale dello stato per intercettare incoerenze di configurazione.
   * Lancia eccezione su condizioni bloccanti (null / liste vuote) e produce WARNING per mismatch tollerabili.
   */
  public static void validateState(State state) {
    baseStateChecks(state);
    Decimal percentageSum = 0;
    Integer assignedCountsSum = 0;
    for (CenterConfiguration centerConfiguration : state.orderedCenters) {
      validateCenterValues(centerConfiguration);
      percentageSum += centerConfiguration.percentage;
      assignedCountsSum += centerConfiguration.assignedCount;
    }
    warnIf(
      Math.abs(percentageSum - 1) > 0.001,
      'Somma percentuali dopo normalizzazione=' + percentageSum
    );
    warnIf(
      state.initialTotal != assignedCountsSum,
      'initialTotal=' +
        state.initialTotal +
        ' diverso da somma assignedCount=' +
        assignedCountsSum
    );
  }

  private static void baseStateChecks(State state) {
    if (state == null) {
      throw new InvalidStateException('State nullo');
    }
    if (state.orderedCenters == null || state.orderedCenters.isEmpty()) {
      throw new InvalidStateException('orderedCenters vuoto');
    }
    if (state.centersByCode == null || state.centersByCode.isEmpty()) {
      throw new InvalidStateException('centersByCode vuoto (non costruito)');
    }
  }

  private static void validateCenterValues(CenterConfiguration centerConfiguration) {
    validateCenterSkeleton(centerConfiguration);
    if (centerConfiguration.percentage == null || centerConfiguration.percentage <= 0) {
      throw new InvalidStateException(
        'percentage non valido per center ' + centerConfiguration.centerCode
      );
    }
    if (centerConfiguration.assignedCount == null || centerConfiguration.assignedCount < 0) {
      throw new InvalidStateException(
        'assignedCount negativo/null per center ' + centerConfiguration.centerCode
      );
    }
  }

  private static void warnIf(Boolean condition, String message) {
    if (condition) {
      System.debug(LoggingLevel.WARN, message);
    }
  }

  /**
   * Esegue l'assegnazione sequenziale dei Case forniti, producendo una lista di decisioni.
   * Ogni decisione incrementa globalStep e il contatore del centro selezionato.
   */
  public CC_CaseAssignmentWrapper.CaseAssignmentResult assign(
    List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem> inputItems
  ) {
    CC_CaseAssignmentWrapper.CaseAssignmentResult assignmentResult = new CC_CaseAssignmentWrapper.CaseAssignmentResult();
    assignmentResult.finalAssignedCounts = new Map<String, Integer>();
    if (inputItems == null) {
      return assignmentResult;
    }
    for (
      CC_CaseAssignmentWrapper.CaseAssignmentInputItem inputItem : inputItems
    ) {
      Integer nextStep = engineState.globalStep + 1; // step 1-based della prossima assegnazione
      CenterConfiguration selectedCenterConfiguration = pickCenter(nextStep);
      Decimal deficitAtDecision = calcDeficit(
        selectedCenterConfiguration,
        nextStep
      );
      // Aggiorna stato
      selectedCenterConfiguration.assignedCount += 1;
      engineState.globalStep = nextStep;
      // Costruzione decisione (refactored senza Params annidato)
      assignmentResult.decisions.add(
        new CC_CaseAssignmentWrapper.CaseAssignmentDecision(
          inputItem.getId(),
          selectedCenterConfiguration.centerCode,
          deficitAtDecision,
          engineState.globalStep
        )
      );
    }
    // Popola conteggi finali
    for (CenterConfiguration centerConfiguration : engineState.orderedCenters) {
      assignmentResult.finalAssignedCounts.put(
        centerConfiguration.centerCode,
        centerConfiguration.assignedCount
      );
    }
    assignmentResult.finalGlobalStep = engineState.globalStep;
    return assignmentResult;
  }

  /**
   * Seleziona il centro con deficit maggiore rispetto all'aspettativa teorica.
   * Tie-break: percentuale maggiore, poi ordine alfabetico del codice.
   */
  private CenterConfiguration pickCenter(Integer nextStep) {
    CenterConfiguration bestCenterConfiguration;
    Decimal bestDeficitValue = null; // inizializza alla prima iterazione
    for (CenterConfiguration centerConfiguration : engineState.orderedCenters) {
      Decimal currentDeficitValue = calcDeficit(centerConfiguration, nextStep);
      if (
        bestCenterConfiguration == null ||
        currentDeficitValue > bestDeficitValue ||
        (currentDeficitValue == bestDeficitValue &&
        tieBreak(centerConfiguration, bestCenterConfiguration))
      ) {
        bestCenterConfiguration = centerConfiguration;
        bestDeficitValue = currentDeficitValue;
      }
    }
    return bestCenterConfiguration;
  }

  /**
   * Calcola il deficit = expectedSoFar - assignedCount per un centro dato lo step futuro.
   */
  private Decimal calcDeficit(
    CenterConfiguration centerConfiguration,
    Integer nextStep
  ) {
    Decimal expectedSoFar =
      (engineState.initialTotal + nextStep) * centerConfiguration.percentage;
    return expectedSoFar - centerConfiguration.assignedCount;
  }

  /**
   * Tie-break tra due centri con stesso deficit:
   * 1) percentuale più alta
   * 2) codice alfabeticamente minore
   */
  private Boolean tieBreak(
    CenterConfiguration candidateCenterConfiguration,
    CenterConfiguration currentBestCenterConfiguration
  ) {
    if (
      candidateCenterConfiguration.percentage >
      currentBestCenterConfiguration.percentage
    ) {
      return true;
    }
    if (
      candidateCenterConfiguration.percentage <
      currentBestCenterConfiguration.percentage
    ) {
      return false;
    }
    return candidateCenterConfiguration.centerCode <
      currentBestCenterConfiguration.centerCode; // ordine alfabetico
  }

  // ===== Internals =====
  private static void normalizePercentages(State state) {
    if (state == null || state.orderedCenters == null) {
      return;
    }
    Decimal sum = 0;
    for (CenterConfiguration centerConfiguration : state.orderedCenters) {
      if (centerConfiguration != null && centerConfiguration.percentage != null) {
        sum += centerConfiguration.percentage;
      }
    }
    if (sum <= 0) {
      throw new InvalidStateException('Somma percentuali <= 0');
    }
    if (Math.abs(sum - 1) <= 0.0001) {
      return; // già normalizzate
    }
    for (CenterConfiguration centerConfiguration : state.orderedCenters) {
      if (centerConfiguration != null) {
        centerConfiguration.percentage = centerConfiguration.percentage / sum;
      }
    }
  }
}
