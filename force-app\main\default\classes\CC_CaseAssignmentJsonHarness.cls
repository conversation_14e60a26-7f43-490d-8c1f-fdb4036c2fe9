// Harness minimale: nessuna logica/derivazione. Accetta DTO esterno (StateSeed) definito nel Wrapper
// e una lista di caseId. Scopo: deserializzare, trasformare StateSeed -> State engine, invocare assign,
// restituire il risultato + debug.
public with sharing class CC_CaseAssignmentJsonHarness {
  // Custom domain exception (internal). Public so it can be caught externally if needed.
  public class HarnessException extends Exception {
  }
  /**
   * JSO<PERSON> atteso (formato con StateSeed + solo CaseId):
   * {
   *   "stateSeed": {
   *     "globalStep": 0,
   *     "initialTotal": 10, // opzionale; se omesso calcolato da somma assignedCount
   *     "centers": [
   *       { "centerCode": "CC1", "percentage": 20 },
   *       { "centerCode": "CC2", "percentage": 30 },
   *       { "centerCode": "CC3", "percentage": 50 }
   *     ]
   *   },
   *   "cases": [
   *     { "caseId": "500XXX001" },
   *     { "caseId": "500XXX002" }
   *   ]
   * }
   * NOTE:
   * - Percentuali possono essere grezze (es. 20/30/50): l'engine normalizza.
   * - Regole di validazione: duplicate/percentuali <= 0 => eccezione; mismatch initialTotal => warning.
   */

  // Input payload semplificato
  public class Payload {
    public CC_CaseAssignmentWrapper.StateSeed stateSeed; // obbligatorio
    public List<RawCase> cases;
  }

  // Internal small context holder (lint: reduce long parameter list)
  private class BuildContext {
    public CC_CaseAssignmentWrapper.CaseAssignmentResult result;
    public CC_CaseAssignmentEngine.State engineState;
    public CC_CaseAssignmentWrapper.StateSeed seed;
    public String validationWarning;
  }
  public class RawCase {
    public String caseId;
  }
  // Output
  public class HarnessResponse {
    public List<CC_CaseAssignmentWrapper.CaseAssignmentDecision> decisions;
    public Map<String, Integer> finalCounts;
    public Integer finalGlobalStep;
    public String debugLog;
  }

  // API principale
  public static HarnessResponse runFromJson(String jsonPayload) {
    if (String.isBlank(jsonPayload)) {
      throw new HarnessException('Payload vuoto');
    }
    Payload payload = deserialize(jsonPayload);
    if (payload.stateSeed == null) {
      throw new HarnessException('stateSeed obbligatorio');
    }
    // Build input items dai raw case
    List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem> inputItems = buildInputItems(
      payload.cases
    );
    // Costruzione State engine dal seed esterno
    CC_CaseAssignmentEngine.State engineState = payload.stateSeed.toEngineState();
    CC_CaseAssignmentEngine assignmentEngine = new CC_CaseAssignmentEngine(engineState);
    String validationWarning = validate(assignmentEngine, engineState);
    CC_CaseAssignmentWrapper.CaseAssignmentResult result = assignmentEngine.assign(
      inputItems
    );
    BuildContext contextHolder = new BuildContext();
    contextHolder.result = result;
    contextHolder.engineState = engineState;
    contextHolder.seed = payload.stateSeed;
    contextHolder.validationWarning = validationWarning;
    return buildResponse(contextHolder);
  }

  private static Payload deserialize(String rawJson) {
    try {
      return (Payload) JSON.deserialize(rawJson, Payload.class);
    } catch (Exception ex) {
      throw new HarnessException('JSON non valido: ' + ex.getMessage());
    }
  }
  private static String validate(
    CC_CaseAssignmentEngine assignmentEngine,
    CC_CaseAssignmentEngine.State stateObject
  ) {
    try {
      CC_CaseAssignmentEngine.validateState(stateObject);
      return null;
    } catch (CC_CaseAssignmentEngine.InvalidStateException validationException) {
      throw new HarnessException(validationException.getMessage());
    } catch (Exception unexpectedException) {
      return unexpectedException.getMessage();
    }
  }
  private static List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem> buildInputItems(
    List<RawCase> rawCases
  ) {
    List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem> inputItems = new List<CC_CaseAssignmentWrapper.CaseAssignmentInputItem>();
    if (rawCases == null) {
      return inputItems;
    }
    for (RawCase rawCase : rawCases) {
      if (rawCase == null) continue;
      inputItems.add(new CC_CaseAssignmentWrapper.CaseAssignmentInputItem(
        safeParseId(rawCase.caseId)
      ));
    }
    return inputItems;
  }
  private static HarnessResponse buildResponse(BuildContext context) {
    HarnessResponse response = new HarnessResponse();
    response.decisions = context.result.decisions;
    response.finalCounts = context.result.finalAssignedCounts;
    response.finalGlobalStep = context.result.finalGlobalStep;
    Map<String, Object> debug = new Map<String, Object>();
    debug.put('stateSeed', context.seed);
    debug.put('engineState', context.engineState);
    debug.put('validationWarning', context.validationWarning);
    debug.put('result', context.result);
    response.debugLog = JSON.serializePretty(debug);
    return response;
  }

  // Helpers
  private static Id safeParseId(String rawId) {
    if (String.isBlank(rawId)) {
      return null;
    }
    try {
      return (Id) rawId;
    } catch (Exception ex) {
      return null;
    }
  }

  /**
   * Wrapper Aura/LWC friendly entry point.
   * - Colleziona gli errori domain (HarnessException) e li re-invia come singola AuraHandledException
   *   con payload JSON (chiave "errors").
   * - Restituisce il risultato (JSON serializzato) in caso di successo.
   * NOTE: si usa una stringa JSON per evitare di annotare tutte le proprietà @AuraEnabled su classi interne.
   */
  @AuraEnabled(cacheable=false)
  public static String runFromJsonAura(String jsonPayload) {
    List<String> errors = new List<String>();
    HarnessResponse response;
    try {
      response = runFromJson(jsonPayload);
    } catch (HarnessException harnessException) {
      errors.add(harnessException.getMessage());
    } catch (Exception unexpectedException) {
      // Catch-all imprevisti: non esporre stack completo, solo messaggio
      errors.add('Errore imprevisto: ' + unexpectedException.getMessage());
    }
    if (!errors.isEmpty()) {
      Map<String, Object> errorPayload = new Map<String, Object>{ 'errors' => errors };
      throw new AuraHandledException(JSON.serialize(errorPayload));
    }
    return JSON.serialize(response);
  }
}
