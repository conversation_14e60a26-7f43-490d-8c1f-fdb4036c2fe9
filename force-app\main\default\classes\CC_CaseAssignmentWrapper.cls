// Wrapper data structures per assegnazione Case (prefisso CC, senza interfaccia)
public class CC_CaseAssignmentWrapper {
  public class CaseAssignmentInputItem {
    public Id caseId { get; private set; }

    // DTO constructor only. Use external factories/parsers.
    public CaseAssignmentInputItem(Id caseId) {
      this.caseId = caseId;
    }
    // Accessor helper minimal (solo Id necessario per engine)
    public Id getId() {
      return caseId;
    }
  }

  public class CaseAssignmentDecision {
    public Id caseId { get; private set; }
    public String targetCenterCode { get; private set; }
    public Decimal deficitAtDecision { get; private set; }
    public Integer globalStep { get; private set; }
    public CaseAssignmentDecision(
      Id caseId,
      String targetCenterCode,
      Decimal deficitAtDecision,
      Integer globalStep
    ) {
      this.caseId = caseId;
      this.targetCenterCode = targetCenterCode;
      this.deficitAtDecision = deficitAtDecision;
      this.globalStep = globalStep;
    }
  }

  public class CaseAssignmentResult {
    public List<CaseAssignmentDecision> decisions = new List<CaseAssignmentDecision>();
    public Map<String, Integer> finalAssignedCounts;
    public Integer finalGlobalStep;
  }

  // Pannello di debug tipizzato per evitare Map<String,Object> non strutturate
  public class CaseAssignmentDebugPanel {
    public CaseAssignmentResult result; // risultato completo
    public Decimal configPercentagesRawSum; // somma percentuali grezze input
    public Integer usedInitialTotal; // initialTotal effettivamente usato dall'engine
    public Boolean warningInitialTotalWithoutBreakdown; // true se initialTotal fornito senza breakdown per centro
    public String validationWarning; // eventuale warning/errore catturato in validazione stato
    public List<CC_CaseAssignmentEngine.CenterConfiguration> centersOrdered; // configurazioni ordinate
  }
  // Parsing e conversioni rimossi: ora in CCCaseAssignmentInputParser.

  /**
   * =====================
   *  External State DTOs
   * =====================
   * Scopo: permettere ai consumer esterni di passare una rappresentazione neutra dello stato iniziale
   * senza dover conoscere direttamente CC_CaseAssignmentEngine.State. Favorisce incapsulazione e stabilità.
   */
  public class CenterSeed {
    public String centerCode;
    public Decimal percentage; // grezza o già normalizzata
    public Integer assignedCount; // baseline (default 0 se null in build)
  }
  public class StateSeed {
    public Integer globalStep; // default 0 se null
    public Integer initialTotal; // opzionale: se assente viene ricalcolato
    public List<CenterSeed> centers;

    /**
     * Costruisce lo State dell'engine a partire dai seed.
     * Regole:
     * - Ordine deterministico alfabetico per centerCode (tie-break stabile).
     * - initialTotal: se valorizzato e differisce dalla somma conteggi => warning in validate, non errore.
     */
    public CC_CaseAssignmentEngine.State toEngineState() {
      if (centers == null || centers.isEmpty()) {
        throw new CC_CaseAssignmentEngine.InvalidStateException(
          'centers vuoto in StateSeed'
        );
      }
      Map<String, Decimal> percentagesByCenterMap = new Map<String, Decimal>();
      Map<String, Integer> countsByCenterMap = new Map<String, Integer>();
      for (CenterSeed centerSeed : centers) {
        if (centerSeed == null || String.isBlank(centerSeed.centerCode)) {
          throw new CC_CaseAssignmentEngine.InvalidStateException(
            'centerCode mancante in CenterSeed'
          );
        }
        percentagesByCenterMap.put(centerSeed.centerCode, centerSeed.percentage);
        countsByCenterMap.put(
          centerSeed.centerCode,
          centerSeed.assignedCount == null ? 0 : centerSeed.assignedCount
        );
      }
      CC_CaseAssignmentEngine.State built = CC_CaseAssignmentEngine.buildState(
        percentagesByCenterMap,
        countsByCenterMap,
        globalStep
      );
      // Se l'utente ha fornito initialTotal lo sovrascriviamo (manteniamo scelta esplicita)
      if (initialTotal != null) {
        built.initialTotal = initialTotal;
      }
      return built;
    }
  }

  public class ExternalAssignmentRequest {
    public StateSeed stateSeed; // obbligatorio per nuova modalità
    public List<CaseAssignmentInputItem> cases;
  }

  public class ExternalAssignmentResponse {
    public CaseAssignmentResult result;
    public StateSeed inputStateSeed;
    public String validationWarning;
  }

  /**
   * Helper per eseguire l'engine da un ExternalAssignmentRequest.
   */
  public static ExternalAssignmentResponse execute(
    ExternalAssignmentRequest request
  ) {
    if (request == null || request.stateSeed == null) {
      throw new AuraHandledException('stateSeed obbligatorio');
    }
    CC_CaseAssignmentEngine.State engineState = request.stateSeed.toEngineState();
    CC_CaseAssignmentEngine engine = new CC_CaseAssignmentEngine(engineState);
    String validationWarning;
    try {
      CC_CaseAssignmentEngine.validateState(engineState);
    } catch (CC_CaseAssignmentEngine.InvalidStateException validationException) {
      throw new AuraHandledException(validationException.getMessage());
    } catch (Exception unexpectedException) {
      validationWarning = unexpectedException.getMessage();
    }
    List<CaseAssignmentInputItem> items = request.cases == null
      ? new List<CaseAssignmentInputItem>()
      : request.cases;
  CaseAssignmentResult result = engine.assign(items);
    ExternalAssignmentResponse response = new ExternalAssignmentResponse();
    response.result = result;
    response.inputStateSeed = request.stateSeed;
    response.validationWarning = validationWarning;
    return response;
  }
}
