/*
 * Execution snippets (run this Batch)
 *
 * Anonymous Apex (Developer Console ▶ Execute Anonymous):
 *   // Run immediately with batch size 200
 *   Database.executeBatch(new CC_CaseCheckSLAExpiring(), 200);
 *
 *   // Run immediately with a smaller scope (e.g., for testing)
 *   Database.executeBatch(new CC_CaseCheckSLAExpiring(), 50);
 *
 * One‑off schedule (delayed start in minutes):
 *   // Start in 10 minutes, scope 200
 *   System.scheduleBatch(new CC_CaseCheckSLAExpiring(), 'CC_SLA_OneOff', 10, 200);
 *
 * Unit test pattern:
 *   Test.startTest();
 *   Id jobId = Database.executeBatch(new CC_CaseCheckSLAExpiring(), 200);
 *   Test.stopTest(); // forces finish() to run
 */
public with sharing class CC_CaseCheckSLAExpiring implements Database.Batchable<SObject> {
    private Date targetDate;

    public CC_CaseCheckSLAExpiring() {
        this.targetDate = computeNextBusinessDate(Date.today());
    }

    public Database.QueryLocator start(Database.BatchableContext context) {
        // Query Cases expiring on the target date and not yet flagged
        return Database.getQueryLocator([
            SELECT Id
            FROM Case
            WHERE RecordType.DeveloperName = 'CC_Contact_Center'
              AND SLA_Expiration_Date__c <= :targetDate
              AND (SLA_Expiration_Notice__c = false OR SLA_Expiration_Notice__c = null)
        ]);
    }

    public void execute(Database.BatchableContext context, List<Case> scope) {
        for (Case caseRecord : scope) {
            caseRecord.SLA_Expiration_Notice__c = true;
        }
        if (!scope.isEmpty()) {
            update scope;
        }
    }

    public void finish(Database.BatchableContext context) {
        // No-op
    }

    @TestVisible
    private static Date computeNextBusinessDate(Date fromDate) {
        // Start from the next calendar day
        Date candidate = fromDate.addDays(1);
        Id defaultBusinessHoursId = null;
        try {
            List<BusinessHours> businessHoursList = [SELECT Id FROM BusinessHours WHERE IsDefault = true LIMIT 1];
            if (!businessHoursList.isEmpty()) {
                defaultBusinessHoursId = businessHoursList[0].Id;
            }
        } catch (Exception ignored) {
            // Ignore and fallback to weekend-only exclusion
        }

        while (true) {
            if (defaultBusinessHoursId != null) {
                // Check if a mid-day time falls within business hours (handles weekends and org holidays)
                DateTime midday = DateTime.newInstance(candidate, Time.newInstance(12, 0, 0, 0));
                if (BusinessHours.isWithin(defaultBusinessHoursId, midday)) {
                    break;
                }
            } else {
                // Fallback: exclude Saturday (7) and Sunday (1)
                // Compute day-of-week using start-of-week reference (Sunday=1 ... Saturday=7)
                Integer dayOfWeek = candidate.toStartOfWeek().daysBetween(candidate) + 1;
                if (dayOfWeek != 1 && dayOfWeek != 7) {
                    break;
                }
            }
            candidate = candidate.addDays(1);
        }
        return candidate;
    }
}
