@isTest
private class CC_CaseCheckSLAExpiringTest {
    // Tries to resolve the Case RecordType 'CC_Contact_Center'; returns null if missing
    private static Id findCCRecordTypeIdIfAny() {
        List<RecordType> recordTypes = [SELECT Id FROM RecordType WHERE SobjectType = 'Case' AND DeveloperName = 'CC_Contact_Center' LIMIT 1];
        return recordTypes.isEmpty() ? null : recordTypes[0].Id;
    }

    @isTest
    static void testExecuteBatchFlagsCorrectCases() {
        Date target = CC_CaseCheckSLAExpiring.computeNextBusinessDate(Date.today());
        Id contactCenterRecordTypeId = findCCRecordTypeIdIfAny();

        Case shouldBeFlagged = new Case(
            Subject = 'SLA Expiring',
            Origin = 'Phone',
            Status = 'New',
            Expiration_Date__c = target,
            SLA_Expiration_Notice__c = false
        );
        if (contactCenterRecordTypeId != null) shouldBeFlagged.RecordTypeId = contactCenterRecordTypeId;

        Case shouldRemainUnchanged = new Case(
            Subject = 'Different Date',
            Origin = 'Phone',
            Status = 'New',
            Expiration_Date__c = target.addDays(1)
        );
        if (contactCenterRecordTypeId != null) shouldRemainUnchanged.RecordTypeId = contactCenterRecordTypeId;

        insert new List<Case>{ shouldBeFlagged, shouldRemainUnchanged };

        Test.startTest();
        Database.executeBatch(new CC_CaseCheckSLAExpiring(), 50);
        Test.stopTest();

        Map<Id, Case> afterMap = new Map<Id, Case>([
            SELECT SLA_Expiration_Notice__c, Expiration_Date__c FROM Case WHERE Id IN :new Set<Id>{shouldBeFlagged.Id, shouldRemainUnchanged.Id}
        ]);

        System.assertEquals(true, afterMap.get(shouldBeFlagged.Id).SLA_Expiration_Notice__c, 'Case on target date must be flagged');
        System.assertEquals(false, afterMap.get(shouldRemainUnchanged.Id).SLA_Expiration_Notice__c, 'Case on other date must not be flagged');
    }
}
