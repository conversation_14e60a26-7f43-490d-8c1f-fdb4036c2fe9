/**
 * CC_CaseRoutingRules
 *
 * Builds and submits a request to the OmniStudio Integration Procedure
 * 'CC_MotoreSmistamento' to retrieve routing rules for Case assignment.
 *
 * Test hooks:
 * - lastBuiltRequest exposes the last generated payload for assertions.
 * - testStubbedResponse allows tests to short-circuit the external call.
 */
public class CC_CaseRoutingRules {
  @TestVisible static RoutingRuleRequest lastBuiltRequest;
  @TestVisible static List<RoutingRuleResponseItem> testStubbedResponse;
  /**
   * Single combination used to query routing rules.
   * Matches the Integration Procedure expected request shape.
   */
  public class RoutingRuleRequestItem {
    public String activity;
    public String type;
    public String need_scope;
    public String rule_type;
    public String contact_center;
  }

  /** Envelope containing all combinations to be evaluated by the IP. */
  public class RoutingRuleRequest {
    public List<RoutingRuleRequestItem> request;
  }

  /**
   * Response item of the routing rules IP, enriched with the originating
   * request fields (merged by array position).
   */
  public class RoutingRuleResponseItem {
    public String rule_id;
    public Integer priority;
    public Integer assignment_percentage;
    // Enriched fields from request (merged by array position)
    public String activity;
    public String type;
    public String need_scope;
    public String rule_type;
    public String contact_center;
  }

  /**
   * Fetches routing rules from the Integration Procedure based on the
   * provided activity, types, contact centers and need scopes.
   *
   * Parameters:
   * - activity: business activity identifier (e.g., CaseRouting).
   * - types: list of Case.Type values to evaluate.
   * - contact_center: list of Contact Center codes (e.g., CC1, CC2).
   * - need_scope: list describing need scope (e.g., 'Tutti' or specifics) used to derive rule_type.
   *
   * Returns: list of RoutingRuleResponseItem merged by array index with input
   *          (each item includes both IP response fields and originating request fields).
   * Side effects: assigns lastBuiltRequest; in tests can use testStubbedResponse as base.
   */
  public static List<RoutingRuleResponseItem> fetchRoutingRules(
    String activity,
    List<String> types,
    List<String> contact_center,
    List<String> need_scope
  ) {
    Integer typesCount = (types == null) ? 0 : types.size();
    Integer contactCentersCount = (contact_center == null) ? 0 : contact_center.size();
    Integer needScopesCount = (need_scope == null) ? 0 : need_scope.size();
    System.debug(LoggingLevel.DEBUG,
      '[CC_CaseRoutingRules] fetchRoutingRules START activity=' + String.valueOf(activity) +
      ' typesCount=' + typesCount +
      ' contactCentersCount=' + contactCentersCount +
      ' needScopesCount=' + needScopesCount
    );

    RoutingRuleRequest requestPayload = new RoutingRuleRequest();
    requestPayload.request = new List<RoutingRuleRequestItem>();

    List<String> safeTypes = (types == null) ? new List<String>() : types;
    List<String> safeContactCenters = (contact_center == null) ? new List<String>() : contact_center;
    List<String> safeNeedScopes = (need_scope == null) ? new List<String>() : need_scope;

    for (String typeValue : safeTypes) {
      for (String contactCenterValue : safeContactCenters) {
        for (String needScopeValue : safeNeedScopes) {
          RoutingRuleRequestItem requestItem = new RoutingRuleRequestItem();
          requestItem.activity = activity;
          requestItem.type = typeValue;
          requestItem.need_scope = needScopeValue;
          requestItem.rule_type = (needScopeValue != null && needScopeValue.trim().toLowerCase() == 'tutti') ? 'Generale' : 'Eccezione';
          requestItem.contact_center = contactCenterValue;
          requestPayload.request.add(requestItem);
        }
      }
    }

    // expose last request for tests
    lastBuiltRequest = requestPayload;
    System.debug(LoggingLevel.DEBUG, '[CC_CaseRoutingRules] requestCombinations=' + requestPayload.request.size());

    // Prepare base response (stubbed in tests or from IP call)
    List<RoutingRuleResponseItem> baseResponseItems;
    if (Test.isRunningTest() && testStubbedResponse != null) {
      System.debug(LoggingLevel.DEBUG, '[CC_CaseRoutingRules] usingStubbedResponse size=' + testStubbedResponse.size());
      baseResponseItems = testStubbedResponse;
    } else {
      Map<String, Object> integrationProcedureInput = (Map<String, Object>) JSON.deserializeUntyped(JSON.serialize(requestPayload));
      String integrationProcedureName = getIntegrationProcedureName();
      System.debug(LoggingLevel.DEBUG, '[CC_CaseRoutingRules] callingIntegrationProcedure name=' + integrationProcedureName);
      Object integrationProcedureResult = omnistudio.IntegrationProcedureService.runIntegrationService(
        integrationProcedureName,
        integrationProcedureInput,
        null
      );

      // Tolerant parsing: allow null entries in the array.
      List<Object> rawResultList;
      if (integrationProcedureResult instanceof List<Object>) {
        rawResultList = (List<Object>) integrationProcedureResult;
      } else {
        // Try to deserialize untyped from JSON string
        String serializedResultJson = JSON.serialize(integrationProcedureResult);
        Object untypedResult = JSON.deserializeUntyped(serializedResultJson);
        if (untypedResult instanceof List<Object>) {
          rawResultList = (List<Object>) untypedResult;
        } else if (untypedResult instanceof Map<String, Object>) {
          Map<String, Object> responseMap = (Map<String, Object>) untypedResult;
          Object possibleList = responseMap.get('result');
          if (!(possibleList instanceof List<Object>)) possibleList = responseMap.get('results');
          if (!(possibleList instanceof List<Object>)) possibleList = responseMap.get('data');
          if (possibleList instanceof List<Object>) {
            rawResultList = (List<Object>) possibleList;
          } else {
            // Fallback: treat the whole object as a single element list
            rawResultList = new List<Object>{ untypedResult };
          }
        } else {
          rawResultList = new List<Object>();
        }
      }

      baseResponseItems = new List<RoutingRuleResponseItem>();
      Integer index = 0;
      for (Object rawElement : rawResultList) {
        if (rawElement == null) {
          baseResponseItems.add(null);
        } else if (rawElement instanceof Map<String, Object>) {
          Map<String, Object> responseItemMap = (Map<String, Object>) rawElement;
          RoutingRuleResponseItem responseItem = new RoutingRuleResponseItem();
          responseItem.rule_id = (String) responseItemMap.get('rule_id');
          Object priorityValue = responseItemMap.get('priority');
          if (priorityValue != null) {
            if (priorityValue instanceof Integer) responseItem.priority = (Integer) priorityValue;
            else if (priorityValue instanceof Long) responseItem.priority = Integer.valueOf(String.valueOf(priorityValue));
            else if (priorityValue instanceof Decimal) responseItem.priority = ((Decimal) priorityValue).intValue();
          }
          Object assignmentPercentageValue = responseItemMap.get('assignment_percentage');
          if (assignmentPercentageValue != null) {
            if (assignmentPercentageValue instanceof Integer) responseItem.assignment_percentage = (Integer) assignmentPercentageValue;
            else if (assignmentPercentageValue instanceof Long) responseItem.assignment_percentage = Integer.valueOf(String.valueOf(assignmentPercentageValue));
            else if (assignmentPercentageValue instanceof Decimal) responseItem.assignment_percentage = ((Decimal) assignmentPercentageValue).intValue();
          }
          baseResponseItems.add(responseItem);
        } else {
          // Last resort: attempt typed deserialize of the single element
          String itemJson = JSON.serialize(rawElement);
          try {
            baseResponseItems.add((RoutingRuleResponseItem) JSON.deserialize(itemJson, RoutingRuleResponseItem.class));
          } catch (Exception ex) {
            baseResponseItems.add(null);
          }
        }
        index++;
      }
    }

    // Build min explicit priority among GENERAL rules per Type (from response aligned to request)
    Integer minLenForScan = Math.min((requestPayload.request == null) ? 0 : requestPayload.request.size(),
                                     (baseResponseItems == null) ? 0 : baseResponseItems.size());
    Map<String, Integer> minGeneralExplicitPriorityByType = new Map<String, Integer>();
    for (Integer i = 0; i < minLenForScan; i++) {
      RoutingRuleRequestItem req = requestPayload.request[i];
      RoutingRuleResponseItem res = baseResponseItems[i];
      if (req == null || res == null || res.priority == null) continue;
      Boolean isGeneral = (req.need_scope == 'Tutti') || (req.rule_type == 'Generale');
      if (!isGeneral) continue;
      Integer current = minGeneralExplicitPriorityByType.get(req.type);
      minGeneralExplicitPriorityByType.put(req.type, (current == null || res.priority < current) ? res.priority : current);
    }

    // Merge input request items with response items by index.
    Integer requestSize = (requestPayload.request == null) ? 0 : requestPayload.request.size();
    Integer responseSize = (baseResponseItems == null) ? 0 : baseResponseItems.size();
    Integer maxLen = (requestSize > responseSize) ? requestSize : responseSize;
    Integer nullResponseElements = 0;
    if (baseResponseItems != null) {
      for (RoutingRuleResponseItem item : baseResponseItems) {
        if (item == null) nullResponseElements++;
      }
    }
    System.debug(LoggingLevel.DEBUG,
      '[CC_CaseRoutingRules] mergeSummary requestSize=' + requestSize +
      ' responseSize=' + responseSize +
      ' nullResponseElements=' + nullResponseElements +
      ' maxLen=' + maxLen
    );

    List<RoutingRuleResponseItem> mergedItems = new List<RoutingRuleResponseItem>();
    for (Integer index = 0; index < maxLen; index++) {
      RoutingRuleRequestItem requestItemAtIndex = (index < requestSize) ? requestPayload.request[index] : null;
      RoutingRuleResponseItem responseItemAtIndex = (index < responseSize) ? baseResponseItems[index] : null;

      RoutingRuleResponseItem merged = new RoutingRuleResponseItem();

      // Copy request portion (if present)
      if (requestItemAtIndex != null) {
        merged.activity = requestItemAtIndex.activity;
        merged.type = requestItemAtIndex.type;
        merged.need_scope = requestItemAtIndex.need_scope;
        merged.rule_type = requestItemAtIndex.rule_type;
        merged.contact_center = requestItemAtIndex.contact_center;
      }

      // Copy response portion (if present; nodes may be null)
      if (responseItemAtIndex != null) {
        merged.rule_id = responseItemAtIndex.rule_id;
        merged.priority = responseItemAtIndex.priority;
        merged.assignment_percentage = responseItemAtIndex.assignment_percentage;
      }

      // Apply fallback for missing priority: explicit -> min(General by Type)+1 -> base Type ranking
      if (merged.priority == null && requestItemAtIndex != null) {
        Integer minGeneral = minGeneralExplicitPriorityByType.get(requestItemAtIndex.type);
        if (minGeneral != null) {
          merged.priority = minGeneral + 1;
        } else {
          Integer baseRank = getBaseTypeRank(requestItemAtIndex.type);
          if (baseRank != null) merged.priority = baseRank * 1000 + 1;
        }
      }

      mergedItems.add(merged);
    }

    System.debug(LoggingLevel.DEBUG, '[CC_CaseRoutingRules] fetchRoutingRules END mergedItems=' + mergedItems.size());
    return mergedItems;
  }

  /** Returns the OmniStudio Integration Procedure API name used for routing. */
  private static String getIntegrationProcedureName() {
    // Keep as a dedicated method to simplify future configuration changes
    return 'CC_MotoreSmistamento';
  }

  // Helper: base precedence among Types when no explicit/general priority exists.
  private static Integer getBaseTypeRank(String caseType) {
    if (String.isBlank(caseType)) return null;
    if (caseType == 'CallMeBack') return 1;
    if (caseType == 'Acquisto non concluso') return 2;
    if (caseType == 'Salva Preventivo') return 3;
    return 999;
  }
}
