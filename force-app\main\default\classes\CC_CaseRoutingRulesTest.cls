@isTest
private class CC_CaseRoutingRulesTest {
  @isTest
  static void testPriorityNormalizationAndMerge() {
    List<String> types = new List<String>{ 'CallMeBack', 'Salva Preventivo' };
    List<String> centers = new List<String>{ 'CC1' };
    List<String> scopes = new List<String>{ 'Tutti', 'AmbitoX' };

    // Build stubbed response ordered by: type -> center -> scope
    // Indexes:
    // 0: CallMeBack | CC1 | Tutti   -> explicit priority 6
    // 1: CallMeBack | CC1 | AmbitoX -> null priority (expect 7)
    // 2: Salva Preventivo | CC1 | Tutti -> null priority (expect 3001)
    // 3: Salva Preventivo | CC1 | AmbitoX -> null priority (expect 3001)
    List<CC_CaseRoutingRules.RoutingRuleResponseItem> stub = new List<CC_CaseRoutingRules.RoutingRuleResponseItem>();
    CC_CaseRoutingRules.RoutingRuleResponseItem r0 = new CC_CaseRoutingRules.RoutingRuleResponseItem();
    r0.rule_id = 'RR0'; r0.priority = 6; r0.assignment_percentage = 60;
    stub.add(r0);
    stub.add(null);
    stub.add(null);
    CC_CaseRoutingRules.RoutingRuleResponseItem r3 = new CC_CaseRoutingRules.RoutingRuleResponseItem();
    r3.rule_id = 'RR3'; r3.assignment_percentage = 40; // priority null
    stub.add(r3);
    CC_CaseRoutingRules.testStubbedResponse = stub;

    Test.startTest();
    List<CC_CaseRoutingRules.RoutingRuleResponseItem> out = CC_CaseRoutingRules.fetchRoutingRules('CaseRouting', types, centers, scopes);
    Test.stopTest();

    System.assertEquals(4, out.size(), 'Expected one result per combination');

    Map<String, CC_CaseRoutingRules.RoutingRuleResponseItem> byKey = new Map<String, CC_CaseRoutingRules.RoutingRuleResponseItem>();
    for (CC_CaseRoutingRules.RoutingRuleResponseItem item : out) {
      byKey.put(item.type + '|' + item.contact_center + '|' + item.need_scope, item);
    }

    System.assertEquals(6, byKey.get('CallMeBack|CC1|Tutti').priority, 'Explicit priority must be preserved');
    System.assertEquals(7, byKey.get('CallMeBack|CC1|AmbitoX').priority, 'Missing priority falls back to min(General)+1');
    System.assertEquals(3001, byKey.get('Salva Preventivo|CC1|Tutti').priority, 'No explicit general -> base type rank fallback');
    System.assertEquals(3001, byKey.get('Salva Preventivo|CC1|AmbitoX').priority, 'No explicit general -> base type rank fallback');
    System.assertEquals('CC1', byKey.get('CallMeBack|CC1|Tutti').contact_center, 'Contact center merged from request');
  }
}

