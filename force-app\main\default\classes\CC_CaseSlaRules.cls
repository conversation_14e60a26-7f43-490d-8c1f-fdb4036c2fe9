/**
 * CC_CaseSlaRules
 *
 * Builds and submits a request to the OmniStudio Integration Procedure
 * 'CC_MotoreSLA' to retrieve SLA rules for Case handling.
 *
 * Test hooks:
 * - lastBuiltRequest exposes the last generated payload for assertions.
 * - testStubbedResponse allows tests to short-circuit the external call.
 */
public class CC_CaseSlaRules {
  @TestVisible static SlaRuleRequest lastBuiltRequest;
  @TestVisible static List<SlaRuleResponseItem> testStubbedResponse;

  /**
   * Single combination used to query SLA rules.
   * Matches the Integration Procedure expected request shape.
   */
  public class SlaRuleRequestItem {
    public String activity;
    public String type;
    public String need_scope;
    public String rule_type;
  }

  /** Envelope containing all combinations to be evaluated by the IP. */
  public class SlaRuleRequest {
    public List<SlaRuleRequestItem> request;
  }

  /**
   * Response item of the SLA rules IP, enriched with the originating
   * request fields (merged by array position).
   */
  public class SlaRuleResponseItem {
    public String rule_id;
    public Integer priority;
    public Integer sla_days;
    // Enriched fields from request (merged by array position)
    public String activity;
    public String type;
    public String need_scope;
    public String rule_type;
  }

  /**
   * Fetches SLA rules from the Integration Procedure based on the
   * provided activity, types, need scopes and rule types.
   *
   * Parameters:
   * - activity: business activity identifier (e.g., CaseSLA).
   * - types: list of Case.Type values to evaluate.
   * - need_scope: list describing need scope (e.g., 'Tutti' or specifics).
   * - rule_types: list of rule types (e.g., 'Generale', 'Eccezione').
   *
   * Returns: list of SlaRuleResponseItem merged by array index with input
   *          (each item includes both IP response fields and originating request fields).
   * Side effects: assigns lastBuiltRequest; in tests can use testStubbedResponse as base.
   */
  public static List<SlaRuleResponseItem> fetchSlaRules(
    String activity,
    List<String> types,
    List<String> need_scope,
    List<String> rule_types
  ) {
    Integer typesCount = (types == null) ? 0 : types.size();
    Integer needScopesCount = (need_scope == null) ? 0 : need_scope.size();
    Integer ruleTypesCount = (rule_types == null) ? 0 : rule_types.size();
    System.debug(LoggingLevel.DEBUG,
      '[CC_CaseSlaRules] fetchSlaRules START activity=' + String.valueOf(activity) +
      ' typesCount=' + typesCount +
      ' needScopesCount=' + needScopesCount +
      ' ruleTypesCount=' + ruleTypesCount
    );

    SlaRuleRequest requestPayload = new SlaRuleRequest();
    requestPayload.request = new List<SlaRuleRequestItem>();

    List<String> safeTypes = (types == null) ? new List<String>() : types;
    List<String> safeNeedScopes = (need_scope == null) ? new List<String>() : need_scope;
    List<String> safeRuleTypes = (rule_types == null) ? new List<String>() : rule_types;

    for (String typeValue : safeTypes) {
      for (String needScopeValue : safeNeedScopes) {
        for (String ruleTypeValue : safeRuleTypes) {
          SlaRuleRequestItem requestItem = new SlaRuleRequestItem();
          requestItem.activity = activity;
          requestItem.type = typeValue;
          requestItem.need_scope = needScopeValue;
          requestItem.rule_type = ruleTypeValue;
          requestPayload.request.add(requestItem);
        }
      }
    }

    // expose last request for tests
    lastBuiltRequest = requestPayload;
    System.debug(LoggingLevel.DEBUG, '[CC_CaseSlaRules] requestCombinations=' + requestPayload.request.size());

    // Prepare base response (stubbed in tests or from IP call)
    List<SlaRuleResponseItem> baseResponseItems;
    if (Test.isRunningTest() && testStubbedResponse != null) {
      System.debug(LoggingLevel.DEBUG, '[CC_CaseSlaRules] usingStubbedResponse size=' + testStubbedResponse.size());
      baseResponseItems = testStubbedResponse;
    } else {
      Map<String, Object> integrationProcedureInput = (Map<String, Object>) JSON.deserializeUntyped(JSON.serialize(requestPayload));
      String integrationProcedureName = getIntegrationProcedureName();
      System.debug(LoggingLevel.DEBUG, '[CC_CaseSlaRules] callingIntegrationProcedure name=' + integrationProcedureName);
      Object integrationProcedureResult = omnistudio.IntegrationProcedureService.runIntegrationService(
        integrationProcedureName,
        integrationProcedureInput,
        null
      );

      // Tolerant parsing: allow null entries in the array.
      List<Object> rawResultList;
      if (integrationProcedureResult instanceof List<Object>) {
        rawResultList = (List<Object>) integrationProcedureResult;
      } else {
        // Try to deserialize untyped from JSON string
        String serializedResultJson = JSON.serialize(integrationProcedureResult);
        Object untypedResult = JSON.deserializeUntyped(serializedResultJson);
        if (untypedResult instanceof List<Object>) {
          rawResultList = (List<Object>) untypedResult;
        } else if (untypedResult instanceof Map<String, Object>) {
          Map<String, Object> responseMap = (Map<String, Object>) untypedResult;
          Object possibleList = responseMap.get('result');
          if (!(possibleList instanceof List<Object>)) possibleList = responseMap.get('results');
          if (!(possibleList instanceof List<Object>)) possibleList = responseMap.get('data');
          if (possibleList instanceof List<Object>) {
            rawResultList = (List<Object>) possibleList;
          } else {
            // Fallback: treat the whole object as a single element list
            rawResultList = new List<Object>{ untypedResult };
          }
        } else {
          rawResultList = new List<Object>();
        }
      }

      baseResponseItems = new List<SlaRuleResponseItem>();
      Integer index = 0;
      for (Object rawElement : rawResultList) {
        if (rawElement == null) {
          baseResponseItems.add(null);
        } else if (rawElement instanceof Map<String, Object>) {
          Map<String, Object> responseItemMap = (Map<String, Object>) rawElement;
          SlaRuleResponseItem responseItem = new SlaRuleResponseItem();
          responseItem.rule_id = (String) responseItemMap.get('rule_id');
          Object priorityValue = responseItemMap.get('priority');
          if (priorityValue != null) {
            if (priorityValue instanceof Integer) responseItem.priority = (Integer) priorityValue;
            else if (priorityValue instanceof Long) responseItem.priority = Integer.valueOf(String.valueOf(priorityValue));
            else if (priorityValue instanceof Decimal) responseItem.priority = ((Decimal) priorityValue).intValue();
          }
          Object slaDaysValue = responseItemMap.get('sla_days');
          if (slaDaysValue != null) {
            if (slaDaysValue instanceof Integer) responseItem.sla_days = (Integer) slaDaysValue;
            else if (slaDaysValue instanceof Long) responseItem.sla_days = Integer.valueOf(String.valueOf(slaDaysValue));
            else if (slaDaysValue instanceof Decimal) responseItem.sla_days = ((Decimal) slaDaysValue).intValue();
          }
          baseResponseItems.add(responseItem);
        } else {
          // Last resort: attempt typed deserialize of the single element
          String itemJson = JSON.serialize(rawElement);
          try {
            baseResponseItems.add((SlaRuleResponseItem) JSON.deserialize(itemJson, SlaRuleResponseItem.class));
          } catch (Exception ex) {
            baseResponseItems.add(null);
          }
        }
        index++;
      }
    }

    // Build min explicit priority among GENERAL rules per Type (from response)
    Integer minLenForScan = Math.min((requestPayload.request == null) ? 0 : requestPayload.request.size(),
                                     (baseResponseItems == null) ? 0 : baseResponseItems.size());
    Map<String, Integer> minGeneralExplicitPriorityByType = new Map<String, Integer>();
    for (Integer i = 0; i < minLenForScan; i++) {
      SlaRuleRequestItem req = requestPayload.request[i];
      SlaRuleResponseItem res = baseResponseItems[i];
      if (req == null || res == null || res.priority == null) continue;
      Boolean isGeneral = (req.need_scope == 'Tutti') || (req.rule_type == 'Generale');
      if (!isGeneral) continue;
      Integer current = minGeneralExplicitPriorityByType.get(req.type);
      minGeneralExplicitPriorityByType.put(req.type, (current == null || res.priority < current) ? res.priority : current);
    }

    // Merge input request items with response items by index, applying fallback priority rules
    Integer requestSize = (requestPayload.request == null) ? 0 : requestPayload.request.size();
    Integer responseSize = (baseResponseItems == null) ? 0 : baseResponseItems.size();
    Integer maxLen = (requestSize > responseSize) ? requestSize : responseSize;
    Integer nullResponseElements = 0;
    if (baseResponseItems != null) {
      for (SlaRuleResponseItem item : baseResponseItems) {
        if (item == null) nullResponseElements++;
      }
    }
    System.debug(LoggingLevel.DEBUG,
      '[CC_CaseSlaRules] mergeSummary requestSize=' + requestSize +
      ' responseSize=' + responseSize +
      ' nullResponseElements=' + nullResponseElements +
      ' maxLen=' + maxLen
    );

    List<SlaRuleResponseItem> mergedItems = new List<SlaRuleResponseItem>();
    for (Integer index = 0; index < maxLen; index++) {
      SlaRuleRequestItem requestItemAtIndex = (index < requestSize) ? requestPayload.request[index] : null;
      SlaRuleResponseItem responseItemAtIndex = (index < responseSize) ? baseResponseItems[index] : null;

      SlaRuleResponseItem merged = new SlaRuleResponseItem();

      // Copy request portion (if present)
      if (requestItemAtIndex != null) {
        merged.activity = requestItemAtIndex.activity;
        merged.type = requestItemAtIndex.type;
        merged.need_scope = requestItemAtIndex.need_scope;
        merged.rule_type = requestItemAtIndex.rule_type;
      }

      // Copy response portion (if present; nodes may be null)
      if (responseItemAtIndex != null) {
        merged.rule_id = responseItemAtIndex.rule_id;
        merged.priority = responseItemAtIndex.priority;
        merged.sla_days = responseItemAtIndex.sla_days;
      }

      // Apply fallback for missing priority: explicit -> min(General by Type)+1 -> base Type ranking
      if (merged.priority == null && requestItemAtIndex != null) {
        Integer minGeneral = minGeneralExplicitPriorityByType.get(requestItemAtIndex.type);
        if (minGeneral != null) {
          merged.priority = minGeneral + 1;
        } else {
          Integer baseRank = getBaseTypeRank(requestItemAtIndex.type);
          if (baseRank != null) merged.priority = baseRank * 1000 + 1;
        }
      }

      mergedItems.add(merged);
    }

    System.debug(LoggingLevel.DEBUG, '[CC_CaseSlaRules] fetchSlaRules END mergedItems=' + mergedItems.size());
    return mergedItems;
  }

  /** Returns the OmniStudio Integration Procedure API name used for SLA. */
  private static String getIntegrationProcedureName() {
    // Keep as a dedicated method to simplify future configuration changes
    return 'CC_MotoreSLA';
  }

  // Helper: base precedence among Types when no explicit/general priority exists.
  private static Integer getBaseTypeRank(String caseType) {
    if (String.isBlank(caseType)) return null;
    if (caseType == 'CallMeBack') return 1;
    if (caseType == 'Acquisto non concluso') return 2;
    if (caseType == 'Salva Preventivo') return 3;
    return 999;
  }
}
