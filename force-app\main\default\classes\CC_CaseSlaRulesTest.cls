@isTest
private class CC_CaseSlaRulesTest {
  @isTest
  static void testPriorityFallbacks() {
    // Build inputs: two Types, two Scopes, one RuleType ('Generale')
    List<String> types = new List<String>{ 'CallMeBack', 'Acquisto non concluso' };
    List<String> needScopes = new List<String>{ 'Tutti', 'AmbitoX' };
    List<String> ruleTypes = new List<String>{ 'Generale' };

    // Prepare stubbed response aligned with request order:
    // Order produced by fetchSlaRules: for type in types -> for scope in scopes -> for ruleType in ruleTypes
    // Index map:
    // 0: CallMeBack | <PERSON>tti   | Generale  -> explicit priority 6
    // 1: CallMeBack | AmbitoX | Generale  -> priority null (expect fallback 6+1=7)
    // 2: Acquisto non concluso | Tutti   | Generale  -> priority null (expect base rank fallback 2001)
    // 3: Acquisto non concluso | AmbitoX | Generale  -> priority null (expect base rank fallback 2001)
    List<CC_CaseSlaRules.SlaRuleResponseItem> stub = new List<CC_CaseSlaRules.SlaRuleResponseItem>();
    CC_CaseSlaRules.SlaRuleResponseItem r0 = new CC_CaseSlaRules.SlaRuleResponseItem();
    r0.rule_id = 'R0'; r0.priority = 6; r0.sla_days = 5;
    stub.add(r0);
    stub.add(null);
    stub.add(null);
    CC_CaseSlaRules.SlaRuleResponseItem r3 = new CC_CaseSlaRules.SlaRuleResponseItem();
    r3.rule_id = 'R3'; r3.sla_days = 10; // no explicit priority
    stub.add(r3);
    CC_CaseSlaRules.testStubbedResponse = stub;

    Test.startTest();
    List<CC_CaseSlaRules.SlaRuleResponseItem> out = CC_CaseSlaRules.fetchSlaRules('CaseSLA', types, needScopes, ruleTypes);
    Test.stopTest();

    System.assertEquals(4, out.size(), 'Expected one result per combination');

    // Helper: find by type+scope
    Map<String, CC_CaseSlaRules.SlaRuleResponseItem> byKey = new Map<String, CC_CaseSlaRules.SlaRuleResponseItem>();
    for (CC_CaseSlaRules.SlaRuleResponseItem item : out) {
      byKey.put(item.type + '|' + item.need_scope, item);
    }

    System.assertEquals(6, byKey.get('CallMeBack|Tutti').priority, 'Explicit priority must be preserved');
    System.assertEquals(7, byKey.get('CallMeBack|AmbitoX').priority, 'Missing priority falls back to min(General)+1');
    System.assertEquals(2001, byKey.get('Acquisto non concluso|Tutti').priority, 'No explicit general -> base type rank fallback');
    System.assertEquals(2001, byKey.get('Acquisto non concluso|AmbitoX').priority, 'No explicit general -> base type rank fallback');
  }
}

