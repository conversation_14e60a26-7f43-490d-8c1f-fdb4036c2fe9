global class CaseHandlerBatch implements Database.Batchable<SObject> {
    Set<Id> setIdCase = new Set<Id>();

    global Database.QueryLocator start(Database.BatchableContext BC) {
        Integer leaveDays = Integer.valueOf(System.Label.LeaveDaysCase);
        Date dt = Date.today().addDays(-leaveDays);
        String query = 'SELECT Id, Status, dueDate__c, ClosedDate__c FROM Case WHERE Status != \'Closed\' AND RecordType.DeveloperName = \'AttivitaContatto\' AND ((DueDate__c >= :dt AND DueDate__c <= TODAY) OR (ClosedDate__c >= :dt AND ClosedDate__c <= TODAY))';
        return Database.getQueryLocator(query);
    }

    global void execute(Database.BatchableContext BC, List<Case> scope) {
        List<Case> casesToUpdate = new List<Case>();

        Date today = Date.today();
        for (Case c : scope) {
            
            if (c.Status != 'Expired' && c.DueDate__c != null && c.DueDate__c <= today && c.ClosedDate__c > today) {
                c.Status = 'Expired';
                casesToUpdate.add(c);
            }
            
            if (c.ClosedDate__c != null && c.ClosedDate__c <= Date.today()) {
                c.Status = 'Closed';
                casesToUpdate.add(c);
            }
            this.setIdCase.add(c.Id);
        }

        
        if (!casesToUpdate.isEmpty()) {
            //update casesToUpdate;
            try {
                Database.SaveResult[] results = Database.update(casesToUpdate, false);
                for (Integer i = 0; i < results.size(); i++) {
                    if (!results[i].isSuccess()) {
                        System.debug('Errore aggiornando il Case con Id: ' + casesToUpdate[i].Id + ' - ' + results[i].getErrors()[0].getMessage());
                    }
                }
            } catch (Exception ex) {
                System.debug('Eccezione durante l\'update dei Case: ' + ex.getMessage());
            }
        }
    }

    global void finish(Database.BatchableContext BC) {
        if (!this.setIdCase.isEmpty()) {
            ConiQueRefUpdate insApexShares = new ConiQueRefUpdate(
                new Map<String, Object>{
                    System.label.ConiRefUpdateIdKey => setIdCase,
                    System.label.ConiObjNameKey => Schema.SObjectType.Case.getName()
                }
            );
            System.enqueueJob(insApexShares);
        }
        /*Integer batchSize = 30;
        Database.executeBatch(new ManageCaseBatch(), batchSize);*/
    }

}