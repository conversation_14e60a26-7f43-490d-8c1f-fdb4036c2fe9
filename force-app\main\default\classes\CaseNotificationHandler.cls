public without sharing class CaseNotificationHandler {
   

  public void notificationManagement(List<Case> newList, Map<Id, Case> newMap, Map<Id, Case> oldMap, String notificationName) {
   // List<Map<String,Object>> caseToNotify = new List<Map<String,Object>>();
    Map<String, Object> flowParams = new Map<String, Object>();
    List<Case> filteredList =new List<Case>();

        if(notificationName == 'Creation'){
          for(Case c : newList){
             if(c.AreaOfNeed__c == 'VITA'){
              filteredList.add(c);
             }
           }
        }
        
        if(notificationName == 'Reassignment'){
          for(Case c : newList){
            System.debug('%%%% '+c.AssignedTo__c + '%%%% '+oldMap.get(c.id).AssignedTo__c);
            if(c.AssignedTo__c != oldMap.get(c.id).AssignedTo__c){
                filteredList.add(c);
            }
           }
        }

        for(Case c : filteredList){ 
                flowParams = new Map<String, Object>{
                                'NotificationName' => notificationName,
                                    'Object' => 'Case',
                                    'ToWhom' => 'AssignedTo',
                                    'Channel' => 'Bell',
                                    'TargetRecordID' => c.Id,
                                    'RecipientID' => c.AssignedTo__c,
                                    'NotificationBodyDetails' => c.CaseNumber
                                    };      
        }
        

     Flow.Interview.NotificationSystemOrchestratorNew flowInstance = new Flow.Interview.NotificationSystemOrchestratorNew(flowParams);
     flowInstance.start(); 
  }
}