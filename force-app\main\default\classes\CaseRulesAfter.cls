public without sharing class CaseRulesAfter {

    public static void processNewInformationOnCase(List<Case> cases) {
        if (cases == null || cases.isEmpty()) {
            return;
        }

        List<Case> candidateCases = getCandidateCases(cases);
        if (candidateCases.isEmpty() || !Schema.sObjectType.User.isAccessible()) {
            return;
        }

        Map<Id, User> createdByUsers = getCreatedByUsers(candidateCases);
        Map<String, String> contextEntityMap = getCaseActivityInitContexts(candidateCases);
        Map<Id, InsurancePolicy> insurancePoliciesMap = getInsurancePoliciesMap(candidateCases);

        List<Case> casesToUpdate = buildCasesToUpdate(candidateCases, createdByUsers, contextEntityMap, insurancePoliciesMap);

        performBulkUpdate(casesToUpdate);
    }

    private static List<Case> getCandidateCases(List<Case> cases) {
        Set<Id> createdByIds = new Set<Id>();
        List<Case> candidateCases = new List<Case>();
        for (Case c : cases) {
            if (c.Id != null) {
                candidateCases.add(c);
            }
        }
        return candidateCases;
    }

    private static Map<Id, User> getCreatedByUsers(List<Case> candidateCases) {
        Set<Id> createdByIds = new Set<Id>();
        for (Case c : candidateCases) {
            if (c.CreatedById != null) {
                createdByIds.add(c.CreatedById);
            }
        }
        Map<Id, User> createdByUsers = new Map<Id, User>();
        if (!createdByIds.isEmpty()) {
            for (User user : [SELECT Id, Name FROM User WHERE Id IN :createdByIds]) {
                createdByUsers.put(user.Id, user);
            }
        }
        return createdByUsers;
    }
    
    private static String calculateSourceSystem(Case originalCase, Map<Id, User> createdByUsers) {
        if (String.isNotBlank(originalCase.DrCreatoDaUtente__c)) {
            return originalCase.DrCreatoDaUtente__c;
        }
        else if (String.isNotBlank(originalCase.DrCreatoDaNominativo__c)) {
            return originalCase.DrCreatoDaNominativo__c;
        }
        else {
            User createdByUser = createdByUsers.get(originalCase.CreatedById);
            if (createdByUser != null && createdByUser.Name != null && createdByUser.Name.toLowerCase().contains('mulesoft')) {
                return 'SEXT';
            } else {
                return 'SF';
            }
        }
    }

    private static Map<String, String> getCaseActivityInitContexts(List<Case> candidateCases) {
        Set<String> areaValues = new Set<String>();
        Set<String> activityValues = new Set<String>();
        Set<String> detailValues = new Set<String>();
        
        for (Case c : candidateCases) {
            if (String.isNotBlank(c.Area__c)) {
                areaValues.add(c.Area__c);
            }
            if (String.isNotBlank(c.Activity__c)) {
                activityValues.add(c.Activity__c);
            }
            if (String.isNotBlank(c.Detail__c)) {
                detailValues.add(c.Detail__c);
            }
        }
        
        Map<String, String> contextEntityMap = new Map<String, String>();
        
        if (!areaValues.isEmpty() && !activityValues.isEmpty() && !detailValues.isEmpty()) {
            List<CaseActivityInit__c> caseActivityInits = [
                SELECT Area__c, Activity__c, Detail__c, ContextEntity__c 
                FROM CaseActivityInit__c 
                WHERE Area__c IN :areaValues 
                AND Activity__c IN :activityValues 
                AND Detail__c IN :detailValues
                AND ContextEntity__c != null
            ];
            
            for (CaseActivityInit__c cai : caseActivityInits) {
                System.debug('%%cai: ' + cai);
                String key = buildContextKey(cai.Area__c, cai.Activity__c, cai.Detail__c);
                contextEntityMap.put(key, cai.ContextEntity__c);
            }
        }
        
        return contextEntityMap;
    }

    private static String buildContextKey(String area, String activity, String detail) {
        return (area != null ? area : '') + '|' + 
            (activity != null ? activity : '') + '|' + 
            (detail != null ? detail : '');
    }

    private static Map<Id, InsurancePolicy > getInsurancePoliciesMap(List<Case> candidateCases) {
        Set<Id> insurancePolicyIds = new Set<Id>();
        
        for (Case c : candidateCases) {
            if (String.isNotBlank(c.Insurance_Policy__c)) {
                insurancePolicyIds.add(c.Insurance_Policy__c);
            }
        }
        
        Map<Id, InsurancePolicy > insurancePoliciesMap = new Map<Id, InsurancePolicy >();
        
        if (!insurancePolicyIds.isEmpty()) {
            System.debug('Query bulk di ' + insurancePolicyIds.size() + ' InsurancePolicy');

            List<InsurancePolicy > policies = [
                SELECT Id, RecordType.DeveloperName, CompanyCode__c, MotherAgencyCode__c, 
                       AgencyCode__c, PolicyBranchCode__c, ReferencePolicyNumber,
                       ParentPolicy.ReferencePolicyNumber
                FROM InsurancePolicy  
                WHERE Id IN :insurancePolicyIds
            ];

            for (InsurancePolicy policy : policies) {
                insurancePoliciesMap.put(policy.Id, policy);
            }

            System.debug('InsurancePolicy caricate: ' + insurancePoliciesMap.size());
        }
        
        return insurancePoliciesMap;
    }
   

    private static List<Case> buildCasesToUpdate(List<Case> candidateCases, Map<Id, User> createdByUsers, 
                                                Map<String, String> contextEntityMap, Map<Id, InsurancePolicy> insurancePoliciesMap) {
        List<Case> casesToUpdate = new List<Case>();
        for (Case originalCase : candidateCases) {
            Case caseUpdate = new Case(Id = originalCase.Id);
            boolean needsUpdate = false;

            if (String.isBlank(originalCase.ExternalId__c)) {
                caseUpdate.ExternalId__c = 'SF' + originalCase.Id;
                needsUpdate = true;
            }

            if (String.isBlank(originalCase.SourceSystem__c)) {
                String newSourceSystem = calculateSourceSystem(originalCase, createdByUsers);
                if (newSourceSystem != null) {
                    caseUpdate.SourceSystem__c = newSourceSystem;
                    needsUpdate = true;
                }
            }

            User createdByUser = createdByUsers.get(originalCase.CreatedById);
            String contextEntity = getContextEntityForCase(originalCase, contextEntityMap);
            system.debug('%%createdByUser: ' + createdByUser);
            if (contextEntity != null && createdByUser != null && createdByUser.Name != null && !createdByUser.Name.toLowerCase().contains('mulesoft')) {
                Map<String, Object> contextLogicResults = applyContextEntityLogic(originalCase, contextEntity, insurancePoliciesMap);
                if (contextLogicResults != null && !contextLogicResults.isEmpty()) {
                    for (String fieldName : contextLogicResults.keySet()) {
                        caseUpdate.put(fieldName, contextLogicResults.get(fieldName));
                        needsUpdate = true;
                    }
                }
            }

            if (needsUpdate) {
                casesToUpdate.add(caseUpdate);
            }
        }
        return casesToUpdate;
    }

    private static String getContextEntityForCase(Case originalCase, Map<String, String> contextEntityMap) {
        String key = buildContextKey(originalCase.Area__c, originalCase.Activity__c, originalCase.Detail__c);
        system.debug('%%' + key + 'value: ' + contextEntityMap.get(key));
        return contextEntityMap.get(key);
    }

    private static Map<String, Object> applyContextEntityLogic(Case originalCase, String contextEntity, Map<Id, InsurancePolicy> insurancePoliciesMap) {
        Map<String, Object> fieldsToUpdate = new Map<String, Object>();
        
        if (String.isBlank(contextEntity)) {
            return fieldsToUpdate;
        }
        System.debug('%%' + contextEntity);
        switch on contextEntity.toUpperCase() {
            when 'CLAIM' {
                fieldsToUpdate.putAll(handleClaimLogic(originalCase));
            }
            when 'CONTRACT' {
                fieldsToUpdate.putAll(handleContractLogic(originalCase, insurancePoliciesMap));
            }
            when 'NONE' {
                fieldsToUpdate.putAll(handleNoneLogic(originalCase));
            }
            when else {
                System.debug('ContextEntity non gestito: ' + contextEntity);
            }
        }
        
        return fieldsToUpdate;
    }

    private static Map<String, Object> handleNoneLogic(Case originalCase) {
        Map<String, Object> updates = new Map<String, Object>();
        
        if (String.isBlank(originalCase.BusinessKey__c)) {
            updates.put('BusinessKey__c', 'NULL');
        }
        
        return updates;
    }

    private static Map<String, Object> handleClaimLogic(Case originalCase) {
        Map<String, Object> updates = new Map<String, Object>();
        
        if (String.isBlank(originalCase.BusinessKey__c) && String.isNotBlank(originalCase.ClaimNumber__c)) {
            String convertedBusinessKey = convertClaimNumberToBusinessKey(originalCase.ClaimNumber__c);
            if (convertedBusinessKey != null) {
                updates.put('BusinessKey__c', convertedBusinessKey);
                System.debug('CLAIM - BusinessKey convertito: ' + originalCase.ClaimNumber__c + ' -> ' + convertedBusinessKey);
            }
        }
        
        return updates;
    }

    private static String convertClaimNumberToBusinessKey(String claimNumber) {
        if (String.isBlank(claimNumber)) {
            return null;
        }
        
        try {
            List<String> parts = claimNumber.split('-');
            
            /*if (parts.size() != 4) {
                System.debug('Formato ClaimNumber non valido: ' + claimNumber);
                return null;
            }*/
            
            String part1 = parts[0].trim();  
            String part2 = parts[1].trim();  
            String part3 = parts[2].trim();  
            String part4 = parts[3].trim();  
            
            String paddedPart4 = part4.leftPad(9, '0');
            return part1 + part3 + part2 + paddedPart4;
            
        } catch (Exception ex) {
            System.debug('Errore conversione ClaimNumber: ' + ex.getMessage());
            return null;
        }
    }

    private static Map<String, Object> handleContractLogic(Case originalCase, Map<Id, InsurancePolicy> insurancePoliciesMap) {
        Map<String, Object> updates = new Map<String, Object>();
        
        System.debug('Applicando logica CONTRACT per Case: ' + originalCase.Id);
        
        if (String.isBlank(originalCase.BusinessKey__c) && String.isNotBlank(originalCase.Insurance_Policy__c)) {
            String businessKey = calculateContractBusinessKeyFromMap(originalCase.Insurance_Policy__c, insurancePoliciesMap);
            if (businessKey != null) {
                updates.put('BusinessKey__c', businessKey);
                System.debug('CONTRACT - BusinessKey calcolato: ' + businessKey);
            }
        }
        
        return updates;
    }

    private static String calculateContractBusinessKeyFromMap(String insurancePolicyId, Map<Id, InsurancePolicy> insurancePoliciesMap) {
        if (String.isBlank(insurancePolicyId)) {
            return null;
        }

        InsurancePolicy  policy = insurancePoliciesMap.get(insurancePolicyId);
        if (policy == null) {
            System.debug('InsurancePolicy non trovata nella mappa: ' + insurancePolicyId);
            return null;
        }
        
        String recordTypeName = policy.RecordType?.DeveloperName;
        if (String.isBlank(recordTypeName)) {
            System.debug('RecordType.DeveloperName non trovato per Policy: ' + insurancePolicyId);
            return null;
        }
        
        System.debug('CONTRACT - RecordType: ' + recordTypeName);

        if (recordTypeName == 'PU_POSITION') {
            return policy.ParentPolicy?.ReferencePolicyNumber;
        }
        else if (recordTypeName == 'PU_FOLDER') {
            return policy.ReferencePolicyNumber;
        }
        else if (recordTypeName.contains('ESSIG')) {
            return buildEssigBusinessKey(policy);
        }
        else {
            System.debug('CONTRACT - RecordType non gestito: ' + recordTypeName);
            return null;
        }
    }

    private static String buildEssigBusinessKey(InsurancePolicy policy) {
        try {
            String companyCode = normalizeField(policy.CompanyCode__c, 1);
            String motherAgencyCode;
            if (String.isBlank(policy.MotherAgencyCode__c)) {
                motherAgencyCode = normalizeField(policy.AgencyCode__c, 5);
            } else {
                motherAgencyCode = normalizeField(policy.MotherAgencyCode__c, 5);
            }
            String agencyCode = normalizeField(policy.AgencyCode__c, 5);
            String policyBranchMode = normalizeField(policy.PolicyBranchCode__c, 3);
            String referencePolicyNumber = normalizeField(policy.ReferencePolicyNumber, 9);
            
            return companyCode + motherAgencyCode + agencyCode + policyBranchMode + referencePolicyNumber;
            
        } catch (Exception ex) {
            System.debug('Errore costruzione ESSIG BusinessKey: ' + ex.getMessage());
            return null;
        }
    }

    private static String normalizeField(String fieldValue, Integer targetLength) {
        if (String.isBlank(fieldValue)) {
            return '0'.repeat(targetLength);
        }
        
        String cleanValue = fieldValue.trim();
        
        if (cleanValue.length() > targetLength) {
            cleanValue = cleanValue.substring(0, targetLength);
        }
        
        return cleanValue.leftPad(targetLength, '0');
    }
    
    private static void performBulkUpdate(List<Case> casesToUpdate) {
        if (casesToUpdate.isEmpty()) {
            return;
        }
        
        try {
            System.debug('Aggiornamento bulk di ' + casesToUpdate.size() + ' Case');
            Database.SaveResult[] results = Database.update(casesToUpdate, false);
            Integer successCount = 0;
            for (Integer i = 0; i < results.size(); i++) {
                if (results[i].isSuccess()) {
                    successCount++;
                } else {
                    System.debug('Errore aggiornamento Case ID ' + casesToUpdate[i].Id + ': ' + results[i].getErrors());
                }
            }
            System.debug('Aggiornamento completato: ' + successCount + '/' + casesToUpdate.size() + ' Case processati');    
        } catch (Exception ex) {
            System.debug('Eccezione in performBulkUpdate: ' + ex.getMessage());
        }
    }
}