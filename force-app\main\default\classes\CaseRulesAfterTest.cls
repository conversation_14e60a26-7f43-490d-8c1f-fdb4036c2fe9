@isTest
private class CaseRulesAfterTest {

    @testSetup
    static void setupTestData() {

        Account testAccount = new Account(Name = 'Test Account');
        insert testAccount;

        User standardUser = createTestUser('Standard', 'User', '<EMAIL>', testAccount.Id);
        User mulesoftUser = createTestUser('MuleSoft', 'Integration User', '<EMAIL>', testAccount.Id);
        insert new List<User>{standardUser, mulesoftUser};

        List<CaseActivityInit__c> caseActivityInits = new List<CaseActivityInit__c>{
            new CaseActivityInit__c(
                Area__c = 'Portafoglio Vita',
                Activity__c = 'Altre Gestioni Collettive',
                Detail__c = 'Richiesta esclusione assicurato',
                ContextEntity__c = 'CLAIM'
            ),
            new CaseActivityInit__c(
                Area__c = 'Portafoglio Danni',
                Activity__c = 'Gestione Contratti',
                Detail__c = 'Modifica contratto',
                ContextEntity__c = 'CONTRACT'
            ),
            new CaseActivityInit__c(
                Area__c = 'Altro',
                Activity__c = 'Informazioni Generali',
                Detail__c = 'Richiesta info',
                ContextEntity__c = 'NONE'
            )
        };
        insert caseActivityInits;

        Account agency = new Account(Name = 'Agency', ExternalId__c = 'AGE_12345');
        insert agency;
    
        Account account = new Account(Name = 'Client', ExternalId__c = 'C123', VatCode__c = 'C123');
        insert account;

        String recordTypePU = Schema.SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('PU_FOLDER').getRecordTypeId();
        String recordTypeEssig = Schema.SObjectType.InsurancePolicy.getRecordTypeInfosByDeveloperName().get('ESSIG_RE').getRecordTypeId();

        InsurancePolicy testPolicy = new InsurancePolicy(
            Name = 'Test Policy',
            CompanyCode__c = '5',
            MotherAgencyCode__c = '123',
            AgencyCode__c = '4567',
            PolicyBranchCode__c = '89',
            ReferencePolicyNumber = '12345',
            CIP__c = '100',
            NameInsuredId = account.Id,
            Society__c = agency.Id,
            RecordTypeId = recordTypePU
        );
        insert testPolicy;
        
        InsurancePolicy essigPolicy = new InsurancePolicy(
            Name = 'ESSIG Policy',
            CompanyCode__c = '7',
            AgencyCode__c = '8901',
            PolicyBranchCode__c = '12',
            ReferencePolicyNumber = '67890',
            CIP__c = '100',
            NameInsuredId = account.Id,
            Society__c = agency.Id,
            RecordTypeId = recordTypeEssig
        );
        insert essigPolicy;
    }

    private static User createTestUser(String name, String surname, String email, Id accountId) {
        Profile profile = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        
        return new User(
            FirstName = name,
            LastName = surname,
            Email = email,
            Username = email + '.test' + System.currentTimeMillis(),
            Alias = name.substring(0, Math.min(name.length(), 8)),
            TimeZoneSidKey = 'America/Los_Angeles',
            LocaleSidKey = 'en_US',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'en_US',
            ProfileId = profile.Id,
            IdAzienda__c = accountId
        );
    }

    private static Case createBaseCase(String subject) {
        return new Case(
            Subject = subject,
            Area__c = 'Portafoglio Vita',
            Activity__c = 'Altre Gestioni Collettive',
            Detail__c = 'Richiesta esclusione assicurato',
            Status = 'New',
            Origin = 'Web'
        );
    }

    @isTest
    static void testExternalIdPopulation() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            for (Integer i = 0; i < 3; i++) {
                cases.add(createBaseCase('Test Case ' + i));
            }
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        List<Case> updatedCases = [SELECT Id, ExternalId__c FROM Case WHERE Id IN :cases];
        for (Case c : updatedCases) {
            System.assertEquals('SF' + c.Id, c.ExternalId__c);
        }
    }

    @isTest
    static void testSourceSystemWithDrCreatoDaUtente() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case testCase = createBaseCase('Test Dr Utente');
            testCase.DrCreatoDaUtente__c = 'Leonardo';
            cases.add(testCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, SourceSystem__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('Leonardo', updatedCase.SourceSystem__c);
    }

    @isTest
    static void testSourceSystemWithDrCreatoDaNominativo() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case testCase = createBaseCase('Test Dr Nominativo');
            testCase.DrCreatoDaNominativo__c = 'Sistema Esterno';
            cases.add(testCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, SourceSystem__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('Sistema Esterno', updatedCase.SourceSystem__c);
    }

    @isTest
    static void testClaimContextEntity() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case claimCase = createBaseCase('Test Claim');
            claimCase.ClaimNumber__c = '1-8001-2024-0004419';
            cases.add(claimCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('120248001000004419', updatedCase.BusinessKey__c);
    }

    @isTest
    static void testNoneContextEntity() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case noneCase = new Case(
                Subject = 'Test None',
                Area__c = 'Altro',
                Activity__c = 'Informazioni Generali',
                Detail__c = 'Richiesta info',
                Status = 'New'
            );
            cases.add(noneCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('NULL', updatedCase.BusinessKey__c);
    }

    @isTest
    static void testContractContextEntity() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        InsurancePolicy policy = [SELECT Id FROM InsurancePolicy WHERE Name = 'Test Policy' LIMIT 1];
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case contractCase = new Case(
                Subject = 'Test Contract',
                Area__c = 'Portafoglio Danni',
                Activity__c = 'Gestione Contratti',
                Detail__c = 'Modifica contratto',
                Insurance_Policy__c = policy.Id,
                Status = 'New'
            );
            cases.add(contractCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
    }

    @isTest
    static void testEssigBusinessKeyWithEmptyMotherAgencyCode() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        InsurancePolicy essigPolicy = [SELECT Id FROM InsurancePolicy WHERE Name = 'ESSIG Policy' LIMIT 1];

        essigPolicy.RecordType = new RecordType(DeveloperName = 'ESSIG_TEST');
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case essigCase = new Case(
                Subject = 'Test ESSIG',
                Area__c = 'Portafoglio Danni',
                Activity__c = 'Gestione Contratti',
                Detail__c = 'Modifica contratto',
                Insurance_Policy__c = essigPolicy.Id,
                Status = 'New'
            );
            cases.add(essigCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();

        System.assert(true, 'ESSIG logic completed without errors');
    }

    @isTest
    static void testClaimNumberWithExtraParts() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case claimCase = createBaseCase('Test Claim Extra Parts');
            claimCase.ClaimNumber__c = '1-8001-2024-0004419-EXTRA-DATA';
            cases.add(claimCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('120248001000004419', updatedCase.BusinessKey__c);
    }

    @isTest
    static void testEmptyAndNullLists() {
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(null);
        CaseRulesAfter.processNewInformationOnCase(new List<Case>());
        Test.stopTest();
        
        System.assert(true, 'Empty/null list handling completed');
    }

    @isTest
    static void testContractWithoutInsurancePolicy() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case contractCase = new Case(
                Subject = 'Test Contract No Policy',
                Area__c = 'Portafoglio Danni',
                Activity__c = 'Gestione Contratti',
                Detail__c = 'Modifica contratto',
                Status = 'New'
            );
            cases.add(contractCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals(null, updatedCase.BusinessKey__c);
    }

    @isTest
    static void testNoOverwriteExistingValues() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case existingCase = createBaseCase('Test No Overwrite');
            existingCase.ExternalId__c = 'EXISTING_ID';
            existingCase.SourceSystem__c = 'EXISTING_SOURCE';
            existingCase.BusinessKey__c = 'EXISTING_KEY';
            cases.add(existingCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, ExternalId__c, SourceSystem__c, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals('EXISTING_ID', updatedCase.ExternalId__c);
        System.assertEquals('EXISTING_SOURCE', updatedCase.SourceSystem__c);
        System.assertEquals('EXISTING_KEY', updatedCase.BusinessKey__c);
    }

    @isTest
    static void testBulkProcessing() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            for (Integer i = 0; i < 20; i++) {
                Case bulkCase = createBaseCase('Bulk Test ' + i);
                if (Math.mod(i, 2) == 0) {
                    bulkCase.ClaimNumber__c = '1-8001-2024-000' + String.valueOf(i).leftPad(4, '0');
                }
                cases.add(bulkCase);
            }
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        List<Case> updatedCases = [SELECT Id, ExternalId__c, BusinessKey__c FROM Case WHERE Id IN :cases];
        System.assertEquals(20, updatedCases.size());
        
        for (Case c : updatedCases) {
            System.assert(c.ExternalId__c.startsWith('SF'));
        }
    }

    @isTest
    static void testInvalidClaimNumberFormat() {
        User standardUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(standardUser) {
            Case invalidClaimCase = createBaseCase('Test Invalid Claim');
            invalidClaimCase.ClaimNumber__c = '1-8001'; 
            cases.add(invalidClaimCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals(null, updatedCase.BusinessKey__c);
    }

    @isTest
    static void testMulesoftUserSkipsContextEntity() {
        User mulesoftUser = [SELECT Id FROM User WHERE Email = '<EMAIL>' LIMIT 1].get(0);
        
        List<Case> cases = new List<Case>();
        System.runAs(mulesoftUser) {
            Case mulesoftCase = createBaseCase('Test MuleSoft Context');
            mulesoftCase.ClaimNumber__c = '1-8001-2024-0004419';
            cases.add(mulesoftCase);
            insert cases;
        }
        
        Test.startTest();
        CaseRulesAfter.processNewInformationOnCase(cases);
        Test.stopTest();
        
        Case updatedCase = [SELECT Id, BusinessKey__c FROM Case WHERE Id = :cases[0].Id];
        System.assertEquals(null, updatedCase.BusinessKey__c);
    }
}