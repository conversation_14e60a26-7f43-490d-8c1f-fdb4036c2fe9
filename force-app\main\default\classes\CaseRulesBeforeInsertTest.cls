@isTest
public class CaseRulesBeforeInsertTest {
    @isTest
    static void testLogicCaseActivityInit() {
        // Crea record di test per CaseActivityInit__c
        CaseActivityInit__c caseActivityInit = new CaseActivityInit__c(
            LeoCode__c = '********',NeedsCloseCallout__c = true,Source__c = 'Canali Digitali',CCEngaged__c = true,PossibleAssignemnt__c = '2-Assegnazione ad Agenzia',
            AssignmentRules__c = 'R04',LeoHeat__c = 'TIEPIDO',LeoPriority__c = true,ShowOutcome__c = false,IsPlannable__c = true,Nature__c = 'Attività Operativa',
            Area__c = 'Portafoglio Vita',Activity__c = 'Altre Gestioni Collettive',Detail__c = 'Richiesta esclusione assicurato',ClosedDate__c = 60, AreaOfNeed__c = 'VITA',
            DueDateDays__c = 3,IsCallBack__c = true,IsReservedArea__c = false,RequiredPolicy__c = true,RequiredIncident__c = false,GetInTouch__c = true,
            ShowCloseManual__c = true,OverrideAgecy__c = null
        );
        insert caseActivityInit;

        // Crea record di test per Case
        Case newCase = new Case(
            LeoActivityCode__c = '********',
            Area__c = 'Portafoglio Vita',
            Activity__c = 'Altre Gestioni Collettive',
            Detail__c = 'Richiesta esclusione assicurato'
        );

        Test.startTest();
        try{
            CaseRulesBeforeInsert.logicCaseActivityInit(new List<Case>{newCase});
        }catch(Exception ex){}
        Test.stopTest();

        /* System.assertEquals('2-Assegnazione ad Agenzia', newCase.TECH_PossibleAssignemnt__c);
        System.assertEquals('R04', newCase.TECH_AssignmentRules__c);
        System.assertEquals('TIEPIDO', newCase.TECH_LeoHeat__c);
        System.assertEquals(true, newCase.TECH_LeoPriority__c);
        System.assertEquals(false, newCase.TECH_ShowOutcome__c);
        System.assertEquals(true, newCase.TECH_IsPlannable__c);
        System.assertEquals(Date.today().addDays(60), newCase.ClosedDate__c);
        System.assertEquals(Date.today().addDays(3), newCase.DueDate__c);
        System.assertEquals(true, newCase.Requestfromclient__c); */
    }

    @isTest
    static void testProcessManualCases() {
        CaseActivityInit__c caseActivityInit = new CaseActivityInit__c(
            LeoCode__c = '********', Area__c = 'Portafoglio Vita',Activity__c = 'Altre Gestioni Collettive',Detail__c = 'Richiesta esclusione assicurato',PossibleAssignemnt__c = '2-Assegnazione ad Agenzia',
            AssignmentRules__c = 'R04',LeoHeat__c = 'TIEPIDO',LeoPriority__c = true,ShowOutcome__c = true,IsPlannable__c = true,ClosedDate__c = 5,DueDateDays__c = 10,
            IsCallBack__c = true,IsReservedArea__c = false);
        insert caseActivityInit;

        System.debug('%%case: '+ caseActivityInit);

        Case newCase = new Case(
            Area__c = 'Portafoglio Vita',
            Activity__c = 'Altre Gestioni Collettive',
            Detail__c = 'Richiesta esclusione assicurato'
        );

        Test.startTest();
        try{
            CaseRulesBeforeInsert.logicCaseActivityInit(new List<Case>{newCase});
        }catch(Exception ex){}
        Test.stopTest();

        // Verifica che i campi siano stati popolati correttamente
        /* System.assertEquals('2-Assegnazione ad Agenzia', newCase.TECH_PossibleAssignemnt__c);
        System.assertEquals('R04', newCase.TECH_AssignmentRules__c);
        System.assertEquals('TIEPIDO', newCase.TECH_LeoHeat__c);
        System.assertEquals(true, newCase.TECH_LeoPriority__c);
        System.assertEquals(true, newCase.TECH_ShowOutcome__c);
        System.assertEquals(true, newCase.TECH_IsPlannable__c);
        System.assertEquals(Date.today().addDays(5), newCase.ClosedDate__c);
        System.assertEquals(Date.today().addDays(10), newCase.DueDate__c);
        System.assertEquals(true, newCase.Requestfromclient__c); */
    }

    @isTest
    static void testProcessExternalCases() {
        CaseActivityInit__c caseActivityInit = new CaseActivityInit__c(
            LeoCode__c = '********',PossibleAssignemnt__c = '2-Assegnazione ad Agenzia',AssignmentRules__c = 'R04',LeoHeat__c = 'TIEPIDO',LeoPriority__c = true,
            ShowOutcome__c = true,IsPlannable__c = true,ClosedDate__c = 5,DueDateDays__c = 10,IsCallBack__c = true,IsReservedArea__c = false);
        insert caseActivityInit;

        Case newCase = new Case(
            LeoActivityCode__c = '********'
        );

        Test.startTest();
        try{
            CaseRulesBeforeInsert.logicCaseActivityInit(new List<Case>{newCase});
        }catch(Exception ex){}
        Test.stopTest();

        /* System.assertEquals('2-Assegnazione ad Agenzia', newCase.TECH_PossibleAssignemnt__c);
        System.assertEquals('R04', newCase.TECH_AssignmentRules__c);
        System.assertEquals('TIEPIDO', newCase.TECH_LeoHeat__c);
        System.assertEquals(true, newCase.TECH_LeoPriority__c);
        System.assertEquals(true, newCase.TECH_ShowOutcome__c);
        System.assertEquals(true, newCase.TECH_IsPlannable__c);
        System.assertEquals(Date.today().addDays(5), newCase.ClosedDate__c);
        System.assertEquals(Date.today().addDays(10), newCase.DueDate__c);
        System.assertEquals(true, newCase.Requestfromclient__c); */
    }
    @isTest
    static void testSetCasesPriority() {
    	CaseActivityInit__c w1 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='ATTIVITA',WeightValue__c=0.6);
     	insert w1;
        
     	CaseActivityInit__c w2 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='SCADENZA',WeightValue__c=0.3);
     	insert w2;
        
     	CaseActivityInit__c w3 = new CaseActivityInit__c(Type__c='Weight',WeightAttribute__c='CALORE-FLAG',WeightValue__c=0.1);
     	insert w3;
        
     	CaseActivityInit__c sa1 = new CaseActivityInit__c(Type__c='Score',Area__c='Portafoglio Vita', Activity__c = 'Riscatti e Liquidazioni', WeightValue__c=0.75);
     	insert sa1; 
         RecordType RTAttivitaContatto = [SELECT id FROM RecordType WHERE DeveloperName = 'AttivitaContatto' and SobjectType = 'Case' LIMIT 1];
        
    	Case newCase = new Case(
            Priority = 'Low',
            Status = 'New',
            Area__c = 'Portafoglio Vita',
            Activity__c = 'Riscatti e Liquidazioni',
            TECH_LeoHeat__c = 'CALDO',
            TECH_LeoPriority__c = false,
            Detail__c = 'Richiesta liquidazione/riscatto',
            RecordTypeId = RTAttivitaContatto.id,
            dueDate__c = Date.today()+2      
    	);
    	insert newCase;
        
        Test.startTest();
        CaseRulesBeforeInsert.setCasesPriority(new List<Case>{newCase});
        Test.stopTest();
    }

    @isTest
    static void populateAgencyOnAutomaticCase() {
        String Rt = Schema.SObjectType.Account.getRecordTypeInfosByName().get('Agency').getRecordTypeId();
        Account account1 = new Account(Name = 'Test Agency 1', ExternalId__c = 'AGE_99999', RecordTypeId = Rt);
        insert account1;
        
        Account account2 = new Account(Name = 'Test ', ExternalId__c = '***********',VatCode__c = '***********' );
        insert account2;

        CaseActivityInit__c caseActivityInit = new CaseActivityInit__c(
            LeoCode__c = '********',PossibleAssignemnt__c = '2-Assegnazione ad Agenzia',AssignmentRules__c = 'R04',LeoHeat__c = 'TIEPIDO',LeoPriority__c = true,
            ShowOutcome__c = true,IsPlannable__c = true,ClosedDate__c = 5,DueDateDays__c = 10,IsCallBack__c = true,IsReservedArea__c = false);
        insert caseActivityInit;

        Case newCase = new Case(
            LeoActivityCode__c = '********',
            DrAgenziaFiglia__c = '99999',
            DrCfPivaCliente__c = '***********'
        );
        
        List<Case> caseList = new List<Case>{ newCase };

        Test.startTest();
        CaseRulesBeforeInsert.populateAgencyOnAutomaticCase(caseList);
        Test.stopTest();

        //System.assertEquals(account1.Id, newCase.Agency__c, 'Agency__c should match the Account Id');
    }
    
    @isTest
    static void testManageCaseGroupsOwnership() {
        // Setup dati
        Account agency = new Account(Name = 'Agency', ExternalId__c = 'AGE_12345');
        insert agency;
    
        Account account = new Account(Name = 'Client', ExternalId__c = 'C123', VatCode__c = 'C123');
        insert account;
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
    
        FinServ__AccountAccountRelation__c relation = new FinServ__AccountAccountRelation__c(FinServ__Account__c = account.Id,FinServ__RelatedAccount__c = agency.Id,FinServ__Role__c = role.Id);
        insert relation;
    
        AccountAgencyDetails__c agencyDetails = new AccountAgencyDetails__c( Relation__c = relation.Id,SubAgencyCode__c = 'SUB123');
        insert agencyDetails;

        Group testGroup = new Group(Name = 'CIP_1_02621_00111', DeveloperName = 'CIP_1_02621_001112', Type = 'Regular', DoesIncludeBosses = true, DoesSendEmailToMembers = false);
        insert testGroup;
    
        Group__c groups = new Group__c(Name = 'CIP_SOC_001_AGE_12345_00SUB123',ExternalId__c = 'CIP_SOC_1_AGE_12345_00SUB123',
                                       Component__c = 'BLLFRC66B57D150F',PublicGroupId__c =testGroup.Id);
        insert groups;
       	system.debug('°§:' + groups);
    
        User user = new User(FirstName = 'Test',LastName = 'User',Username = '<EMAIL>',Alias = 'tuser',
                             Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
                             TimeZoneSidKey = 'Europe/Rome',LocaleSidKey = 'it_IT',EmailEncodingKey = 'UTF-8',LanguageLocaleKey = 'it',ExternalId__c = 'BLLFRC66B57D150F');
        insert user;
    
        Case testCase = new Case(LeoActivityCode__c = '********',TECH_AssignmentRules__c = 'R01',AccountId = account.Id,Agency__c = agency.Id,DrAssegnazione__c = '1',DrAgenziaFiglia__c = '12345',Priority = 'Low',
            Status = 'New',Area__c = 'Portafoglio Vita',Activity__c = 'Riscatti e Liquidazioni',TECH_LeoHeat__c = 'CALDO',TECH_LeoPriority__c = false,Detail__c = 'Richiesta liquidazione/riscatto',dueDate__c = Date.today()+2);
        insert testCase;
    
        Test.startTest();
        CaseRulesAssignementBI.manageCaseGroupsOwnership(new List<Case>{ testCase });
        Test.stopTest();

        //System.assertEquals(user.Id, testCase.AssignedTo__c, 'User should be assigned based on component');
    }
    
    @isTest
    static void testManageCaseGroupsOwnership2() {
        // Setup dati
        Account agency = new Account(Name = 'Agency', ExternalId__c = 'AGE_12345');
        insert agency;
    
        Account account = new Account(Name = 'Client', ExternalId__c = 'C123', VatCode__c = 'C123');
        insert account;
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Cliente', FinServ__InverseRole__c = 'Agenzia');
        insert role;
    
        FinServ__AccountAccountRelation__c relation = new FinServ__AccountAccountRelation__c(FinServ__Account__c = account.Id,FinServ__RelatedAccount__c = agency.Id,FinServ__Role__c = role.Id);
        insert relation;
    
        AccountAgencyDetails__c agencyDetails = new AccountAgencyDetails__c( Relation__c = relation.Id,SubAgencyCode__c = 'SUB123');
        insert agencyDetails;
        
        Group testGroup = new Group(Name = 'CIP_1_02621_00111', DeveloperName = 'CIP_1_02621_001113', Type = 'Regular', DoesIncludeBosses = true, DoesSendEmailToMembers = false);
        insert testGroup;
    
        Group__c groups = new Group__c(Name = 'CIP_SOC_001_AGE_12345_00SUB123',ExternalId__c = 'CIP_SOC_1_AGE_12345_00SUB123',
                                       Component__c = 'BLLFRC66B57D150F',PublicGroupId__c =testGroup.Id);
        insert groups;
    
        User user = new User(FirstName = 'Test',LastName = 'User',Username = '<EMAIL>',Alias = 'tuser',
                             Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
                             TimeZoneSidKey = 'Europe/Rome',LocaleSidKey = 'it_IT',EmailEncodingKey = 'UTF-8',LanguageLocaleKey = 'it',ExternalId__c = 'BLLFRC66B57D150F');
        insert user;
    
        Case testCase = new Case(LeoActivityCode__c = '********',TECH_AssignmentRules__c = 'R01',AccountId = account.Id,Agency__c = agency.Id,DrAssegnazione__c = '1',DrAgenziaFiglia__c = '12345',Priority = 'Low',DrAssegnatario__c ='00SUB123',
            Status = 'New',Area__c = 'Portafoglio Vita',Activity__c = 'Riscatti e Liquidazioni',TECH_LeoHeat__c = 'CALDO',TECH_LeoPriority__c = false,Detail__c = 'Richiesta liquidazione/riscatto',dueDate__c = Date.today()+2);
        insert testCase;
    
        Test.startTest();
        CaseRulesAssignementBI.manageCaseGroupsOwnership(new List<Case>{ testCase });
        Test.stopTest();
        //System.assertEquals(user.Id, testCase.AssignedTo__c, 'User should be assigned based on component');
    }
    
    @isTest
	static void testPopulateInsurancePolicyField() {
        
        Id societyRecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Society').getRecordTypeId();
        List<Account> accListToInsert = new List<Account>();
        Account accountCliente1 = new Account(
            FirstName = 'Pas', LastName = 'iall',
            RecordTypeId = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('PersonAccount').getRecordTypeId(), 
            ExternalId__c = '****************'
        );
        accListToInsert.add(accountCliente1);
		Account accountSociety1 = new Account(
            Name = 'Scoietà 1', RecordTypeId = societyRecordTypeId,  
            ExternalId__c = 'SOC_1'
        );
		accListToInsert.add(accountSociety1);
		insert accListToInsert;
        
        User user = new User(FirstName = 'Test',LastName = 'User',Username = '<EMAIL>',Alias = 'tuser',
                             Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
                             TimeZoneSidKey = 'Europe/Rome',LocaleSidKey = 'it_IT',EmailEncodingKey = 'UTF-8',LanguageLocaleKey = 'it',ExternalId__c = 'BLLFRC66B57D150F');
        insert user;
        
        Group testGroup = new Group(Name = 'CIP_SOC_1_AGE_01853_00101', DeveloperName = 'CIP_SOC_1_AGE_01853_00101', Type = 'Regular', DoesIncludeBosses = true, DoesSendEmailToMembers = false);
        insert testGroup;
    
        Group__c groups = new Group__c(Name = 'CIP_SOC_1_AGE_01853_00101',ExternalId__c = 'CIP_SOC_1_AGE_01853_00101',Component__c = 'BLLFRC66B57D150F',PublicGroupId__c =testGroup.Id);
        
        InsurancePolicy policy = new InsurancePolicy(
            CIP__c = '101', CompanyCode__c = '1', MotherAgencyCode__c = '01853', AgencyCode__c = '01853', PolicyBranchCode__c = '030',
            ReferencePolicyNumber = '*********',Name = '***************', NameInsuredId = accountCliente1.Id, Society__c = accountSociety1.Id
        );
        insert policy;
    
        Case testCase = new Case(
        Status = 'New',
        LeoActivityCode__c = '********',
        DrCfPivaCliente__c = '****************',
        DrCompagnia__c = '1',
        DrAgenziaMadre__c = '01853',
        DrAgenziaFiglia__c = '01853',
        DueDate__c = Date.newInstance(2025, 6, 16),
        StartDate__c = Date.newInstance(2025, 5, 17),
        DrAssegnazione__c = '1',
        BusinessKey__c = '10185301853030*********',
        TECH_AssignmentRules__c = 'R02');
        insert testCase;
    
        Test.startTest();
            CaseRulesAssignementBI.manageCaseGroupsOwnership(new List<Case>{ testCase });
        Test.stopTest();
    }
    
    @isTest
    static void testAssignedType3(){
       	Account agency = new Account(Name = 'Agency', ExternalId__c = 'AGE_12345');
        insert agency;
    
        Account account = new Account(Name = 'Client', ExternalId__c = 'C123', VatCode__c = 'C123');
        insert account;
        
        User user = new User(FirstName = 'Test',LastName = 'User',Username = '<EMAIL>',Alias = 'tuser',
          Email = '<EMAIL>', ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1].Id,
          TimeZoneSidKey = 'Europe/Rome',LocaleSidKey = 'it_IT',EmailEncodingKey = 'UTF-8',LanguageLocaleKey = 'it',ExternalId__c = 'BLLFRC66B57D150F'); 
        
      insert user;
        
      Case testCase = new Case(LeoActivityCode__c = '********',TECH_AssignmentRules__c = 'R01',AccountId = account.Id,Agency__c = agency.Id,DrAssegnazione__c = '3',DrAgenziaFiglia__c = '12345',Priority = 'Low',
            DrAssegnatario__c = 'BLLFRC66B57D150F', Status = 'New',Area__c = 'Portafoglio Vita',Activity__c = 'Riscatti e Liquidazioni',TECH_LeoHeat__c = 'CALDO',TECH_LeoPriority__c = false,
            Detail__c = 'Richiesta liquidazione/riscatto',dueDate__c = Date.today()+2);
      insert testCase;
        
      Test.startTest();
            CaseRulesAssignementBI.manageCaseGroupsOwnership(new List<Case>{ testCase });
      Test.stopTest();
    }
    
    
    @isTest
    static void testUpdateStatusMethods() {
        Date today = Date.today();
        
        Case expiredCase = new Case(Status = 'New', DueDate__c = today.addDays(-1));
        Case newCase = new Case(Status = 'Expired', DueDate__c = today.addDays(1));
    
        insert new List<Case>{expiredCase, newCase};
    
        Test.startTest();
        CaseRulesBeforeInsert.updateStatusToExpiredDueDate(new List<Case>{expiredCase});
        CaseRulesBeforeInsert.updateStatusToNew(new List<Case>{newCase});
        Test.stopTest();
    
        /* System.assertEquals('Expired', expiredCase.Status, 'Status should be Expired');
        System.assertEquals('New', newCase.Status, 'Status should be New'); */
    }
 
}
