public with sharing class CoverageWSWrapper {
    @AuraEnabled
    public String id;
    @AuraEnabled
    public String externalId;
    @AuraEnabled
    public String name;
    @AuraEnabled
    public String asset;
    @AuraEnabled
    public String stageName;
    @AuraEnabled
    public Decimal amount;
    @AuraEnabled
    public String rating;
    @AuraEnabled
    public String areaOfNeed;
    @AuraEnabled
    public String conventionCode;
    @AuraEnabled
    public String firstName;
    @AuraEnabled
    public String lastName;
    @AuraEnabled
    public String fiscalCode;
    @AuraEnabled
    public Date birthDate;
    @AuraEnabled
    public String email;
    @AuraEnabled
    public String mobilePhone;
    @AuraEnabled
    public String fractionation;
    @AuraEnabled
    public String engagementPoint;
    @AuraEnabled
    public String description;
    @AuraEnabled
    public Double expectedYearlyGrowth;
    @AuraEnabled
    public Double numberOfChildren;
    @AuraEnabled
    public Double previdentialGap;
    @AuraEnabled
    public String productOfInterest;
    @AuraEnabled
    public Double ral;
    @AuraEnabled
    public String sector;
    @AuraEnabled
    public Double yearOfRetirement;
    @AuraEnabled
    public String sourceSystemIdentifier;
    @AuraEnabled
    public String serviceProviderIdentifier;
    @AuraEnabled
    public Date policyEffectiveDate;
    @AuraEnabled
    public Date policyExpirationDate;
    @AuraEnabled
    public Date travelStartDate;
    @AuraEnabled
    public Date travelEndDate;
    @TestVisible
    private static Boolean testCoverage { get; set; }
}