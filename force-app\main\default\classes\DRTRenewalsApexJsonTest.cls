@IsTest
private class DRTRenewalsApexJsonTest {
    
    @TestSetup
    static void setupData() {
        // Crea un profilo fake (non si può in test, quindi si usa quello di sistema)
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];

        // Crea un utente di test con FiscalCode
        User u = new User(
            Alias = 'testu',
            Email='<EMAIL>',
            EmailEncodingKey='UTF-8',
            LastName='TestUser',
            LanguageLocaleKey='it',
            LocaleSidKey='it_IT',
            TimeZoneSidKey='Europe/Paris',
            UserName='testuserdrt' + DateTime.now().getTime() + '@test.com',
            ProfileId=p.Id,
            FiscalCode__c = 'ABC12345'
        );
        insert u;

        // Mock di NetworkUser__c collegato all’utente
        NetworkUser__c netUser = new NetworkUser__c(
            FiscalCode__c = 'ABC12345'
        );
        insert netUser;

        // Mock di relazione agenzia
        /*FinServ__AccountAccountRelation__c rel = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = netUser.Agency__c,
            Identifier__c = '999',
            FinServ__ExternalId__c = 'SOC_123456',
            RecordTypeId = [SELECT Id FROM RecordType WHERE SObjectType='FinServ__AccountAccountRelation__c' LIMIT 1].Id
        );
        insert rel;*/
    }

    @IsTest
    static void testInvokeGetSchemaInit() {
        // Recupera l’utente creato
        User u = [SELECT Id FROM User WHERE FiscalCode__c = 'ABC12345' LIMIT 1];
        System.runAs(u) {
            // Prepara mappe
            Map<String,Object> args = new Map<String,Object>{
                'input'  => new Map<String,Object>(),
                'output' => new Map<String,Object>(),
                'options'=> new Map<String,Object>()
            };

            // Istanzia la classe
            DRTRenewalsApexJson cls = new DRTRenewalsApexJson();
            Object result = cls.call('getSchemaInit', args);
        }
    }

    @IsTest
    static void testInvokeInvalidMethod() {
        Map<String,Object> args = new Map<String,Object>{
            'input'  => new Map<String,Object>(),
            'output' => new Map<String,Object>(),
            'options'=> new Map<String,Object>()
        };

        DRTRenewalsApexJson cls = new DRTRenewalsApexJson();
        Boolean result = cls.invokeMethod('MetodoNonEsistente', new Map<String,Object>(), new Map<String,Object>(), new Map<String,Object>());
    }

    @IsTest
    static void testBuildReferenceListHelper() {
        // Simula relazioni manuali
        FinServ__AccountAccountRelation__c fakeRel = new FinServ__AccountAccountRelation__c(
            Identifier__c = '123',
            FinServ__ExternalId__c = 'SOC_ABC999'
        );
        List<FinServ__AccountAccountRelation__c> rels = new List<FinServ__AccountAccountRelation__c>{ fakeRel };

        Test.startTest();
        DRTRenewalsApexJson cls = new DRTRenewalsApexJson();
        Set<String> res = cls.buildReferenceList(rels);
        Test.stopTest();
    }

    @IsTest
    static void testFormatDatesHelper() {
        Date d = Date.newInstance(2025, 1, 9);
        String res = DRTRenewalsApexJson.formatDates(d);
    }
}