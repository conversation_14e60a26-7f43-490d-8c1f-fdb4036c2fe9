@IsTest
private class DRTRenewalsApexTest {
    
    // Metodo di utilità per creare User di test
    private static User createTestUser(String fiscalCode) {
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        User u = new User(
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'Test',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'Europe/Paris',
            UserName = 'testuser' + System.currentTimeMillis() + '@example.com',
            ProfileId = p.Id,
            FiscalCode__c = fiscalCode
        );
        insert u;
        return u;
    }

    @IsTest
    static void testGetStartDates_success() {
        // Setup dati
        User u = createTestUser('****************');
        System.runAs(u) {
            Account agency = new Account(Name='Agency Test');
            insert agency;

            NetworkUser__c netUser = new NetworkUser__c(
                Agency__c = agency.Id,
                FiscalCode__c = u.FiscalCode__c,
                Society__c = 'SocTest'
            );
            insert netUser;

            RecordType rt = [SELECT Id FROM RecordType WHERE SObjectType='FinServ__AccountAccountRelation__c' AND DeveloperName='AgencySociety' LIMIT 1];

            /*FinServ__AccountAccountRelation__c rel = new FinServ__AccountAccountRelation__c(
                FinServ__Account__c = agency.Id,
                RecordTypeId = rt.Id,
                Identifier__c = '123',
                FinServ__ExternalId__c = 'XXX_SOC_999'
            );
            insert rel;*/

            // Input e output
            Map<String,Object> input  = new Map<String,Object>{ 'inputDate' => '2025-09-01' };
            Map<String,Object> output = new Map<String,Object>();
            Map<String,Object> opts   = new Map<String,Object>();

            Map<String,Object> args = new Map<String,Object>{
                'input' => input,
                'output' => output,
                'options' => opts
            };

            DRTRenewalsApex apexClass = new DRTRenewalsApex();
            Object res = apexClass.call('getStartDates', args);

            Map<String,Object> resp = (Map<String,Object>)output.get('response');
        }
    }

    @IsTest
    static void testGetStartDates_noFiscalCode() {
        // Utente senza FiscalCode__c
        User u = createTestUser(null);
        System.runAs(u) {
            Map<String,Object> input  = new Map<String,Object>{ 'inputDate' => '2025-09-01' };
            Map<String,Object> output = new Map<String,Object>();
            Map<String,Object> opts   = new Map<String,Object>();

            Map<String,Object> args = new Map<String,Object>{
                'input' => input,
                'output' => output,
                'options' => opts
            };

            DRTRenewalsApex apexClass = new DRTRenewalsApex();
            Object res = apexClass.call('getStartDates', args);

            Map<String,Object> resp = (Map<String,Object>)output.get('response');
        }
    }
}