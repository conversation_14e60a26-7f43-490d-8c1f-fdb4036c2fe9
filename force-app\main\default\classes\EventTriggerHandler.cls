public without sharing class EventTriggerHandler {

    public static Boolean hasCloned = false;
    
    public void notificationManagement(List<Event> newList, Map<Id, Event> newMap, Map<Id, Event> oldMap, String notificationName) {
        Map<Id, Event> eventToNotify = new Map<Id, Event>();
        Map<Id, String> eventAttendeeString = new Map<Id, String>();
        
        if (notificationName != 'Delete') {
            for (Event e: newList) {
                if (e.OwnerId != UserInfo.getUserId() && e.ApptBookingInfoUrl__c == null 
                    || (e.ApptBookingInfoUrl__c != null  && oldMap.get(e.id).ApptBookingInfoUrl__c != null) ){
                    eventToNotify.put(e.id, e);        
                }
            }
        }
        else {
        	for (Event e: oldMap.Values()) {
                if (e.OwnerId != UserInfo.getUserId()) {
                    eventToNotify.put(e.id, e);        
                }
            }   
        }
        if (!eventToNotify.isEmpty()) { 
       		eventAttendeeString = getEventRelationForNotifications(eventToNotify.keySet());
            Map<String, Object> targetPageRefMap = new Map<String, Object>{
    			'type' => 'standard__objectPage',
    			'attributes' => new Map<String, String>{
        		'objectApiName' => 'Event',
        		'actionName' => 'home'
    			}
			};
			String targetPageRefJson = JSON.serialize(targetPageRefMap);
            
            for (Event e:eventToNotify.Values()) {  
                String notificationDetails = '\n'+e.Subject +'\n'+e.ActivityDateTime.format('dd/MM/yyyy HH:mm')+'\n'+e.Modalit_di_esecuzione_incontro__c+'\n'+(e.Description != null ? e.Description : '');
                if (eventAttendeeString.get(e.id) != null) {
                	notificationDetails = notificationDetails + '\n Partecipanti: ' + eventAttendeeString.get(e.id);   
                }
                Map<String, Object> flowParams = new Map<String, Object>{
                    'NotificationName' => notificationName,
                        'Object' => 'Event',
                        'ToWhom' => 'AssignedTo',
                        'Channel' => 'Bell',
                        'TargetRecordID' => e.Id,
                        'TargetPageReference' => targetPageRefJson,
                        'RecipientID' => e.OwnerId,
                        'NotificationBodyDetails' => notificationDetails   
                        };
                Flow.Interview.NotificationSystemOrchestratorNew flowInstance = new Flow.Interview.NotificationSystemOrchestratorNew(flowParams);
                flowInstance.start();
            }
    	}
    }
    
    public Map<Id, String> getEventRelationForNotifications(Set<Id> eventIds) {
        Map<Id, List<String>> tempMap = new Map<Id, List<String>>();
        // Recupera le relazioni evento, escludendo l'organizzatore
        List<EventRelation> relations = [
            SELECT EventId, Relation.Name, RelationId
            FROM EventRelation
            WHERE EventId IN :eventIds];
    
        for (EventRelation er : relations) {
            if (er.RelationId != null && er.Relation.Name != null) {
                if (!tempMap.containsKey(er.EventId)) {
                    tempMap.put(er.EventId, new List<String>());
                }
                tempMap.get(er.EventId).add(er.Relation.Name);
            }
        }
        // Costruisce la mappa finale con stringhe separate da virgola
        Map<Id, String> resultMap = new Map<Id, String>();
        for (Id eventId : tempMap.keySet()) {
            resultMap.put(eventId, String.join(tempMap.get(eventId), ', '));
        }
        return resultMap;
    }
       public Boolean shouldBlockUpdate(Event newEvent, Event oldEvent) {
        // Verify if event data are changed 
        // Boolean isChanged = newEvent.LastModifiedDate != oldEvent.LastModifiedDate;

        // Conditions
        Boolean isChild = newEvent.IsChild;
        Boolean isStatusDaPrendere = newEvent.Status__c == 'Da_prendere_in_carico';
        Boolean hasCollaborativeEvent = newEvent.CollaborativeEvent__c != null;
        Boolean isOwnedByUser = newEvent.IsOwnedByRunningUser__c;
        Boolean hasPermission = FeatureManagement.checkPermission('UnipolResponsabileUserPS');
        Boolean isUrlGoogleMeetModified = newEvent.ApptBookingInfoUrl__c != null && oldEvent.ApptBookingInfoUrl__c == null;

        Boolean condition1 = isChild;
        Boolean condition2 = isStatusDaPrendere && hasCollaborativeEvent;
        Boolean condition3 = !isStatusDaPrendere && !isOwnedByUser && !hasPermission;
		
        // system.debug ('isChanged = '+ isChanged);
        system.debug ('isChild = '+ isChild);
        system.debug ('isStatusDaPrendere= '+ isStatusDaPrendere);
        system.debug ('hasCollaborativeEvent= '+ hasCollaborativeEvent); 
        system.debug ('isOwnedByUser = '+ isOwnedByUser); 
        system.debug ('hasPermission  = '+ hasPermission );
        system.debug ('condition3  = '+ condition3 );
           
        // return isChanged && (condition1 || condition2 || condition3);
        return (!hasCloned && !isUrlGoogleMeetModified && (condition1 || condition2 || condition3));
    }
    
    public Boolean shouldBlockDelete(Event oldEvent) {
        // Conditions
        Boolean isChild = oldEvent.IsChild;
        Boolean isStatusDaPrendere = oldEvent.Status__c == 'Da_prendere_in_carico';
        Boolean hasCollaborativeEvent = oldEvent.CollaborativeEvent__c != null;
        Boolean isOwnedByUser = oldEvent.IsOwnedByRunningUser__c;
        Boolean hasPermission = FeatureManagement.checkPermission('UnipolResponsabileUserPS');

        Boolean condition1 = isChild;
        Boolean condition2 = isStatusDaPrendere && hasCollaborativeEvent;
        Boolean condition3 = !isStatusDaPrendere && !isOwnedByUser && !hasPermission;
		
        return (!hasCloned && (condition1 || condition2 || condition3));
    }
    
    public void deleteEventCollabolativoAfterPresaInCarico (List<Event> newList, Map<Id, Event> newMap, Map<Id, Event> oldMap) {
        Map<String, Event> eventToCheck = new Map<String, Event>(); //CollaborativeEventId - Event
        List<Event> eventToDelete = new List<Event>();
        
        for (Event e: newList) {
            if (e.Status__c == 'Preso_in_carico' && oldMap != null && oldMap.get(e.id).Status__c != e.Status__c) {
                eventToCheck.put(e.CollaborativeEvent__c ,e);
            }
        }
        eventToDelete = [SELECT id, CollaborativeEvent__c FROM Event 
                         WHERE CollaborativeEvent__c IN :eventToCheck.keySet()
                         AND id NOT IN :newMap.keySet() AND IsChild = false];
        
        if (!eventToDelete.isEmpty()) {
            delete eventToDelete;
        }
    }
    public void updateLinkGoogleMeetForCollaborativo(List<Event> newList, Map<Id, Event> newMap, Map<Id, Event> oldMap) {
    	Map<String, Event> eventToCheck = new Map<String, Event>(); //CollaborativeEventId - Event
        List<Event> eventToUpdate = new List<Event>();
        
        hasCloned = true;
        for (Event e: newList) { // If collaborative event has updated link google meet, in status "Da prendere in carico"
            if (e.Group__c != null && e.Status__c == 'Da_prendere_in_carico'  && e.ApptBookingInfoUrl__c != null 
                && (oldMap.get(e.id).ApptBookingInfoUrl__c == null) || e.ApptBookingInfoUrl__c != oldMap.get(e.id).ApptBookingInfoUrl__c  ) 
                {
                    eventToCheck.put(e.CollaborativeEvent__c ,e);
                }
    	}
        eventToUpdate = [SELECT id,CollaborativeEvent__c,ApptBookingInfoUrl__c FROM Event 
                         WHERE CollaborativeEvent__c IN :eventToCheck.keySet()
                         AND id NOT IN :newMap.keySet()];
        if (eventToUpdate.isEmpty()) {
            return;
        }
        for (Event e:eventToUpdate) {
        	Event eventWithLink = eventToCheck.get(e.CollaborativeEvent__c);
            e.ApptBookingInfoUrl__c = eventWithLink.ApptBookingInfoUrl__c;
     	}
        update eventToUpdate;
        
    }     
    public void collaborativeEventHandler(List<Event> newList, Map<Id, Event> newMap, Map<Id, Event> oldMap) {
        Map<Id,Id> eventGroupMap = new Map<Id,Id>(); // Id Event - Id Group__c (Collaborative)
        Map<Id,Id> groupPublicGroupMap = new Map<Id,Id>(); // Id Group__c - Id Public Group 
        
        Set<Id> collaborativeEventIds = new Set<Id>();
        Set<Id> groupIds = new Set<Id>();
        Set<Id> publicGroupIds = new Set<Id>();
        // Map for organize GroupMember on GroupId
        Map<Id, List<GroupMember>> groupToMembersMap = new Map<Id, List<GroupMember>>(); //Id Group - List GroupMember
        // Map for organize GroupMember on Event
        Map<Id, List<GroupMember>> eventToMembersMap = new Map<Id, List<GroupMember>>(); //Id Event - List GroupMember
        List<Event> eventsToUpdateList = new List<Event>();
        // Map for organize EventRelations (Attendee) on Event.Users who are also GroupMember are excluded
        Map<Id,List<EventRelation>> eventToEventRelationMap = new Map<Id,List<EventRelation>>(); //Id Event - List EventRelation
        Map<Id,String> eventCollaborativeEventId = new  Map<Id,String>(); //Id Event - Id CollaborativeEvent
        
        Id currentUserId = UserInfo.getUserId();
        
       	for (Event e: newList) {
            if (e.Group__c != null && !hasCloned && (oldMap == null || (oldMap != null && oldMap.get(e.id).Group__c != e.Group__c ))
               ) {
            	eventGroupMap.put(e.id,e.Group__c); 
                collaborativeEventIds.add(e.id);
                groupIds.add(e.Group__c);
                
                UUID randomUuid = UUID.randomUUID(); // Apex method for generate random UUID 
				String uuidStr = randomUuid.toString();
                eventCollaborativeEventId.put(e.id, uuidStr);
                
                // Event for update CollaborativeEvent__c. newList is read-only
                Event updatedEvent = new Event(Id = e.Id, CollaborativeEvent__c = uuidStr, Status__c = 'Da_prendere_in_carico', ShowAs = 'Free', Mail__c = false);
            	eventsToUpdateList.add(updatedEvent);
                hasCloned = true;
           	} 
            System.debug('e.Group__c: ' + e.Group__c);
    	}
        System.debug('Event newList: ' + newList);
        System.debug('Event to Group Map: ' + eventGroupMap);
        
        if (collaborativeEventIds.isEmpty()) {
            return;
        }
        
        if (!eventsToUpdateList.isEmpty()) {
        	update eventsToUpdateList;
    	}
        
        List<Group__c> groupEventList = [
            SELECT Id, PublicGroupId__c
            FROM Group__c 
            WHERE Id IN :groupIds
        ];
        
        for (Group__c g:groupEventList) {
            groupPublicGroupMap.put(g.id,g.PublicGroupId__c);
        	publicGroupIds.add(g.PublicGroupId__c);   
        }
        
        // We need to consider PublicGroupId__c related in Group__c record in query for extract GroupMember   
        List<GroupMember> groupMembers = [
            SELECT Id, GroupId, UserOrGroupId 
            FROM GroupMember 
            WHERE GroupId IN :publicGroupIds
            AND UserOrGroupId != :currentUserId];
        
        System.debug('groupMembers: ' + groupMembers);
        // Set values in Map for organize GroupMember on GroupId
        for (GroupMember gm : groupMembers) {
            if (!groupToMembersMap.containsKey(gm.GroupId)) {
                groupToMembersMap.put(gm.GroupId, new List<GroupMember>());
            }
            groupToMembersMap.get(gm.GroupId).add(gm);
        }
        
     	System.debug('groupToMembersMap: ' + groupToMembersMap);
        // Set values in Map for organize GroupMember on Event
		for (Id idEventGroup:eventGroupMap.keySet()) {
        	Id currentGroupId = eventGroupMap.get(idEventGroup); 
            if (currentGroupId != null) {
                Id currentPublicGroupId = groupPublicGroupMap.get(currentGroupId);
            	List<GroupMember> currentGroupMemberList = groupToMembersMap.get(currentPublicGroupId);
                if (currentGroupMemberList != null) {
    				eventToMembersMap.put(idEventGroup, currentGroupMemberList);
				}
         	}
      	}
        System.debug('Event to Members Map: ' + eventToMembersMap);
        
        eventToEventRelationMap = getEventRelations(eventToMembersMap);
        System.debug('Event to EventRelation Map: ' + eventToEventRelationMap);
        // For any member in map we need to create and assign an event
        for (Id eventMembers:eventToMembersMap.keySet()) {
            Event currentEvent = newMap.get(eventMembers);
            //Attendee List for current event, group memebers are excluded
            List<EventRelation> currentEventRelation = eventToEventRelationMap.get(currentEvent.id);
            String currentCollaborativeEventId = eventCollaborativeEventId.get(currentEvent.id);
            
            List<GroupMember> currentGroupMembers = eventToMembersMap.get(eventMembers);
            for (GroupMember gm:currentGroupMembers) {
            	cloneEventWithNewAssignee(currentEvent, gm.UserOrGroupId,currentEventRelation, currentCollaborativeEventId);    
        	}
        }
    }
    // Method for manage attendee to add. Users who are also in the group are excluded
    public map<Id,List<EventRelation>> getEventRelations(Map<Id, List<GroupMember>> eventMembers) {
    	map<Id,List<EventRelation>> eventRelations = new map<Id,List<EventRelation>>();
        
        List<EventRelation> eventRelationList = [SELECT id, EventId, RelationId FROM EventRelation WHERE EventId IN :eventMembers.keySet() AND IsInvitee = true];
        for (EventRelation er:eventRelationList) {
            // Get GroupMember list for current event
        	List<GroupMember> members = eventMembers.get(er.EventId);
        	Set<Id> memberIds = new Set<Id>();
            // Set of User Id in group members for current event
        	if (members != null) {
            	for (GroupMember gm : members) {
                	memberIds.add(gm.UserOrGroupId); 
            	}
        	}
            // Exclude EventRelation with RelationId in members (UserOrGroupId)
        	if (!memberIds.contains(er.RelationId)) {
            	if (!eventRelations.containsKey(er.EventId)) {
                eventRelations.put(er.EventId, new List<EventRelation>());
            	}
            	eventRelations.get(er.EventId).add(er); 
     		}
        }
        return eventRelations;
        
    }
    
    public void cloneEventWithNewAssignee(Event originalEvent, Id newAssigneeId, List<EventRelation> attendeesWitouhtMembers, String collaborativeEventId) {
        Event clonedEvent = new Event(Status__c = 'Da_prendere_in_carico', IsPrivate = originalEvent.IsPrivate , ShowAs = 'Free', Mail__c = false);
        // Event standard field
        clonedEvent.RecordTypeId = originalEvent.RecordTypeId; 
        // clonedEvent.WhoId = originalEvent.WhoId; // Set in flow EventConiVisibilita
        // clonedEvent.WhatId = originalEvent.WhatId; // Set in flow EventConiVisibilita
        clonedEvent.Subject = originalEvent.Subject;
        clonedEvent.Location = originalEvent.Location;
        clonedEvent.IsAllDayEvent = originalEvent.IsAllDayEvent; 
        clonedEvent.StartDateTime = originalEvent.StartDateTime;
        clonedEvent.EndDateTime = originalEvent.EndDateTime;
        clonedEvent.Description = originalEvent.Description;
        // clonedEvent.IsPrivate = originalEvent.IsPrivate; // set in new, otherwise it is not assigned correctly
        // clonedEvent.ShowAs = originalEvent.ShowAs; // set in new, otherwise it is not assigned correctly
        clonedEvent.ReminderDateTime = originalEvent.ReminderDateTime;
        clonedEvent.IsReminderSet = originalEvent.IsReminderSet;  
        clonedEvent.EventSubtype = originalEvent.EventSubtype;
        // Event-Activity custom field
        clonedEvent.FinServ__Household__c = originalEvent.FinServ__Household__c;
        clonedEvent.AreaOfNeed__c = originalEvent.AreaOfNeed__c;
        clonedEvent.CreatedDateTPD__c = originalEvent.CreatedDateTPD__c;
        clonedEvent.Executor__c = originalEvent.Executor__c;
        clonedEvent.JourneyStep__c = originalEvent.JourneyStep__c;
        clonedEvent.Step__c = originalEvent.Step__c;
        clonedEvent.TimeForContact__c = originalEvent.TimeForContact__c;
        clonedEvent.Modalit_di_esecuzione_incontro__c = originalEvent.Modalit_di_esecuzione_incontro__c;
        clonedEvent.Case__c = originalEvent.Case__c;
        // clonedEvent.Mail__c = originalEvent.Mail__c; // set in new, otherwise it is not assigned correctly
        clonedEvent.Service_Territory__c = originalEvent.Service_Territory__c;
        clonedEvent.Telefono_Cliente__c = originalEvent.Telefono_Cliente__c;
        clonedEvent.Trattativa__c = originalEvent.Trattativa__c;
        clonedEvent.ApptBookingInfoUrl__c = originalEvent.ApptBookingInfoUrl__c;
        clonedEvent.Group__c = originalEvent.Group__c;
        // clonedEvent.Status__c = originalEvent.Status__c; // set in new, otherwise it is not assigned correctly
        clonedEvent.CollaborativeEvent__c = collaborativeEventId;
    
        // New Owner
        clonedEvent.OwnerId = newAssigneeId;
        
        insert clonedEvent;
        
        // Clone EventRelations (Attendee) filtered: without memebers
        // TBD: Questa parte è da approfondire a livello funzionale perchè l'inserimento degli attendee genera eventi in automatico (standard) 
        // che si sommano a quelli generati per il collaborativo. 
        List<EventRelation> clonedRelations = new List<EventRelation>();
        if (attendeesWitouhtMembers != null) {
            for (EventRelation er : attendeesWitouhtMembers) {
                EventRelation clonedEr = new EventRelation(
                    EventId = clonedEvent.Id,
                    RelationId = er.RelationId
                );
                clonedRelations.add(clonedEr);
            }
        
            if (!clonedRelations.isEmpty()) {
                insert clonedRelations;
            }
     	}
    }
}