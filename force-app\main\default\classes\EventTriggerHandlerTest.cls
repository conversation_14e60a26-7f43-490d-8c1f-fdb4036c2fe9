@isTest
public class EventTriggerHandlerTest {
    @testSetup
    static void setupData() {
        Id publicGroupId;
        
        Profile pUser = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1];
        
       // Test User Creation
        User testUser = new User(
            FirstName = '<PERSON>',
            LastName = '<PERSON>',
            Alias = 'm<PERSON>i',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(), // Username deve essere unico
            ProfileId = pUser.Id,
            TimeZoneSidKey = 'Europe/Paris',
            LocaleSidKey = 'fr_FR',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'fr',
            UserRoleId = null,
            IsActive = true
        );
        insert testUser;
        
        Profile pAdmin = [SELECT Id FROM Profile WHERE Name = 'System Administrator' LIMIT 1];
        
        // Test User Creation
        User testAdmin = new User(
            FirstName = 'Mario',
            LastName = 'Admin',
            Alias = 'madmin',
            Email = '<EMAIL>',
            Username = '<EMAIL>' + System.currentTimeMillis(), // Username deve essere unico
            ProfileId = pAdmin.Id,
            TimeZoneSidKey = 'Europe/Paris',
            LocaleSidKey = 'fr_FR',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'fr',
            UserRoleId = null,
            IsActive = true
        );
        insert testAdmin;
        
        // This is for handle this error: System.DmlException: Insert failed. First exception on row 0; first error: MIXED_DML_OPERATION, DML operation on setup object is not permitted 
        // after you have updated a non-setup object
        System.runAs(testAdmin) {
            // Public Group Creation
            Group publicGroup = new Group(Name = 'COLL_Test Public Group', Type = 'Regular');
            insert publicGroup;
            publicGroupId = publicGroup.id;
            // Creazione GroupMember
            GroupMember gm1 = new GroupMember(GroupId = publicGroup.Id, UserOrGroupId = UserInfo.getUserId());
            insert gm1;
            
            GroupMember gm2 = new GroupMember(GroupId = publicGroup.Id, UserOrGroupId = testUser.id);
            insert gm2;
        }
        // Group__c Creation
        Group__c customGroup = new Group__c(Name = 'Collaborative Group', CollaborativeAgendaActivation__c = true, PublicGroupId__c=publicGroupId);
        insert customGroup;
        
    }

    @isTest
    static void testCollaborativeEventHandler() {
        // Recupera dati
        Group__c groupTest = [SELECT Id FROM Group__c LIMIT 1];

        // Crea evento con Group__c
        Event e = new Event(
            Subject = 'Test Event',
            StartDateTime = System.now().addDays(1),
            EndDateTime = System.now().addDays(1).addHours(1),
 			Group__c = groupTest.Id
        );
        insert e;
          
        // Verifica che siano stati creati eventi clonati
        List<Event> clonedEvents = [SELECT Id, CollaborativeEvent__c, OwnerId FROM Event WHERE CollaborativeEvent__c != null and id != :e.id ];
        System.assert(!clonedEvents.isEmpty(), 'Devono essere stati creati eventi clonati');
        
        User userForAttendee = [SELECT id FROM User WHERE alias = 'mrossi'];
        
        EventTriggerHandler.hasCloned = false;        
        EventRelation er = new EventRelation(EventId = e.id, RelationId = userForAttendee.id); //Group member as attendee 
        insert er;
		System.debug('Event Relation - Test ='+er);
        e.EndDateTime = System.now().addDays(1).addHours(2);
        e.ApptBookingInfoUrl__c = 'Test Url';
        update e;
        // Verify that aren't EventRelation in cloned events 
        List<EventRelation> relations = [SELECT Id FROM EventRelation WHERE EventId IN :clonedEvents];
        System.assert(relations.isEmpty(), 'NON devono essere stati creati EventRelation clonati');
        delete e;
    }
     @isTest
    static void testCollaborativeEventHandlerBlockUpdate() {
        // Recupera dati
        Group__c groupTest = [SELECT Id FROM Group__c LIMIT 1];

        // Crea evento con Group__c
        Event e = new Event(
            Subject = 'Test Event',
            StartDateTime = System.now().addDays(1),
            EndDateTime = System.now().addDays(1).addHours(1),
 			Group__c = groupTest.Id,
            Status__c = 'Da_prendere_in_carico'
        );
        insert e;
        e.Subject = 'Test Event - BlockUpdate';
        EventTriggerHandler.hasCloned = false;
        update e;
    }
}