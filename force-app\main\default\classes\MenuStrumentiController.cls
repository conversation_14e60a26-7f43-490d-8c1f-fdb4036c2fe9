public without sharing class MenuStrumentiController {

    public class Tabs {
        @AuraEnabled 
        public String label;
        @AuraEnabled 
        public String name;
        @AuraEnabled 
        public String context;
        @AuraEnabled 
        public String userContext;
    }

    @AuraEnabled(cacheable=true)
    public static List<Tabs> getTabs() {
        Map<String, Menu_Strumenti_Tabs__mdt> menuStrumentiTabsMap = Menu_Strumenti_Tabs__mdt.getAll();

        Set<String> mandatiAbilitati = new Set<String>();
        for(PermissionSetAssignment permissionSetAss : [SELECT Id, PermissionSet.Name, PermissionSetGroupId, AssigneeId, Assignee.FederationIdentifier 
                                                        FROM PermissionSetAssignment 
                                                        WHERE PermissionSet.Name IN ('MandatoUnipolSai', 'MandatoUnipolRental', 'MandatoUnipolTech', 'MandatoUniSalute') AND AssigneeId =: UserInfo.getUserId()]) {

            mandatiAbilitati.add(permissionSetAss.PermissionSet.Name);
        }

        UserUtils.UserContext usrContext = UserUtils.getUserContext();

        Map<String, String> societyMap = new Map<String, String>{
            'MandatoUnipolSai' => 'SOC_1',
            'MandatoUniSalute' => 'SOC_4'
        };

        /*User usr = [SELECT Id, FederationIdentifier FROM User WHERE Id =: UserInfo.getUserId()];

        Map<String, List<NetworkUser__c>> societyNetworkMap = new Map<String, List<NetworkUser__c>>();
        List<NetworkUser__c> networkList = [SELECT Id, Preferred__c, IsActive__c, NetworkUser__c, Society__c, FiscalCode__c FROM NetworkUser__c WHERE FiscalCode__c = :usr.FederationIdentifier AND IsActive__c = TRUE ORDER BY Preferred__c DESC, Society__c];
        for(NetworkUser__c network : networkList) {
            if(network.Society__c == null || network.NetworkUser__c == null) {
                continue;
            }
            if(!societyNetworkMap.containsKey(network.Society__c)) {
                societyNetworkMap.put(network.Society__c, new List<NetworkUser__c>());
            }

            societyNetworkMap.get(network.Society__c).add(network);
        }*/

        //system.debug(JSON.serialize(societyNetworkMap));
        //system.debug(JSON.serialize(societyNetworkMap.get('SOC_1')));
        //system.debug(JSON.serialize(societyNetworkMap.get('SOC_4')));

        List<Tabs> tabsList = new List<Tabs>();
        for(Menu_Strumenti_Tabs__mdt menuStrumentiTabs : menuStrumentiTabsMap.values()) {

            if(mandatiAbilitati.contains(menuStrumentiTabs.Mandato_Abilitato__c)) {
                String userContext = null;
                if(usrContext.userNetworks != null) {
                    userContext = usrContext.userNetworks.containsKey(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)) ? usrContext.userNetworks.get(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)).userId : null;
                }

                Tabs tabs = new Tabs();
                tabs.label = menuStrumentiTabs.MasterLabel;
                tabs.name = menuStrumentiTabs.DeveloperName;
                tabs.context = menuStrumentiTabs.Context_Target__c;
                tabs.userContext = userContext;
                //tabs.userContext = societyNetworkMap.get(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)) != null ? societyNetworkMap.get(societyMap.get(menuStrumentiTabs.Mandato_Abilitato__c)).get(0).NetworkUser__c : null;
                tabsList.add(tabs);
            }
        }

        //system.debug(JSON.serialize(tabsList));

        return tabsList;
    }

    public class Section {
        @AuraEnabled 
        public String label;
        @AuraEnabled 
        public String name;
        @AuraEnabled 
        public String context;
        @AuraEnabled 
        public Integer order;
    }

    @AuraEnabled(cacheable=true)
    public static List<Section> getSections(String context) {
        
        List<Section> sectionsList = new List<Section>();
        for(Menu_Strumenti_Section__mdt menuStrumentiSection : [SELECT Label, DeveloperName, Context__c, Order__c
                                                        FROM Menu_Strumenti_Section__mdt 
                                                        WHERE Context__c = :context ORDER BY Order__c]) {

            Section sections = new Section();
            sections.label = menuStrumentiSection.Label;
            sections.name = menuStrumentiSection.DeveloperName;
            sections.context = menuStrumentiSection.Context__c;
            sections.order = Integer.valueOf(menuStrumentiSection.Order__c);
            sectionsList.add(sections);
        }

        return sectionsList;
    }

    @AuraEnabled(cacheable=true)
    public static List<MenuStrumentiEngine.SectionWrapper> getTreeByContext(String context, String userContext) {
        return MenuStrumentiEngine.getTreeByContext(context, userContext);
    }

    @AuraEnabled
    public static List<String> getUserFavorite() {

        /*String favoriteList = [SELECT Menu_Strumenti_Preferiti__c FROM User WHERE Id = :UserInfo.getUserId()].Menu_Strumenti_Preferiti__c;
        
        if(favoriteList == null) return new List<String>();
        return (List<String>) JSON.deserialize(favoriteList, List<String>.class);*/

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        List<Asset> assetList = [SELECT User__c, Category__c, Value__c, Key__c FROM Asset WHERE User__c = :UserInfo.getUserId() AND Category__c = 'Preferiti' AND Status = 'Registered' AND RecordTypeId = :assetRtId];
        
        if(assetList == null || assetList.isEmpty()) return new List<String>();

        List<String> favoriteList = new List<String>();
        for(asset ast : assetList) {
            favoriteList.add(ast.Key__c);
        }

        return favoriteList;
    }

    @AuraEnabled
    public static void setUserFavorite(String favLabel,
                                        String favDevName,
                                        String favType,
                                        String favParams,
                                        String favLink,
                                        String feiid,
                                        String requestType,
                                        String userContext) {
        
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();
        
        Asset ast = new Asset();
        ast.Name = UserInfo.getUserId() + '_P_' + favDevName;
        ast.User__c = UserInfo.getUserId();
        ast.Category__c = 'Preferiti';
        ast.Key__c = favDevName;
        ast.Value__c = favLabel;
        ast.Status = 'Registered';
        ast.RecordTypeId = assetRtId;
        
        Map<String, Object> jsonObj = new Map<String, Object>();
        jsonObj.put('link', favLink);

        String param;
        if(favType == 'FEI' && favParams != null) {
            
            param = favParams;

        } else if(favType == 'FEI' && favParams == null) {
            
            String params = MenuStrumentiFei.getFeiParams(feiid, userContext);
            if(params != null) {
                param = params;
            }
        }
        jsonObj.put('type', requestType);
        jsonObj.put('param', param);
        
        if(jsonObj.get('link') != null || jsonObj.get('type') != null || jsonObj.get('param') != null) {
            ast.Fei_Parameters__c = JSON.serialize(jsonObj);
        }

        Database.insert(ast);
    }

    @AuraEnabled
    public static void removeUserFavorite(String favorite) {

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();
        
        List<Asset> assetList = [SELECT User__c, Category__c, Key__c FROM Asset WHERE 
                                User__c = :UserInfo.getUserId() AND 
                                Category__c = 'Preferiti' AND RecordTypeId = :assetRtId AND Status = 'Registered'];
        
        if(!assetList.isEmpty() && assetList != null) {
            Asset astToRemove = null;
            for(Asset ast : assetList) {
                if(ast.Key__c == favorite) {
                    astToRemove = ast;
                    astToRemove.Status = 'Obsolete';
                    break;
                }
            }
            if(astToRemove != null) {
                Database.update(astToRemove);
            }
        }
    }

    @AuraEnabled
    public static Map<String, String> getParamsForFei(String feiId, String userContext) {
        String permissionSetName = [
            SELECT Id, UCA_Permission_Name__c FROM FEI_Settings__mdt 
            WHERE Label =: feiId AND Environment__c =: FEI_Environment__c.getInstance().Environment__c LIMIT 1].UCA_Permission_Name__c;

        String fiscalCode = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1].FederationIdentifier;

        Map<String, String> objToReturn = new Map<String, String>();
        objToReturn.put('permissionSetName', permissionSetName);
        objToReturn.put('fiscalCode', fiscalCode);
        objToReturn.put('params', MenuStrumentiFei.getFeiParams(feiId, userContext));

        return objToReturn;
    }

    @AuraEnabled
    public static String getUserCf() {
        return [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1].FederationIdentifier;
    }
}