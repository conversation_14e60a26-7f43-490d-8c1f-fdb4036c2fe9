@isTest
private class MenuStrumentiControllerTest {

    @TestSetup
    static void setupData() {
        // Profilo base
        //Profile p = [SELECT Id FROM Profile WHERE Name = 'Unipol Standard User' LIMIT 1];

        // Creazione utente
        // User u = new User(
        //     FirstName = '<PERSON>',
        //     LastName  = 'Rossi',
        //     Alias     = 'mrossi',
        //     Email     = '<EMAIL>',
        //     Username  = 'mario.rossi' + System.currentTimeMillis() + '@test.com',
        //     FederationIdentifier = 'RSSMRA80A01H501U',
        //     CommunityNickname = 'mrossi' + System.currentTimeMillis(),
        //     TimeZoneSidKey = 'Europe/Rome',
        //     LocaleSidKey   = 'it_IT',
        //     EmailEncodingKey = 'UTF-8',
        //     LanguageLocaleKey = 'it',
        //     ProfileId = p.Id,
        //     UCA_Permissions__c = '{ "users": [ { "userId": "ext-123", "groups": ["GRP_A"] } ] }'
        // );
        // insert u;

        // List<PermissionSet> liveMandates = [
        //     SELECT Id, Name FROM PermissionSet
        //     WHERE Name IN ('MandatoUnipolSai')
        // ];

        // List<PermissionSetAssignment> psaList = new List<PermissionSetAssignment>();
        // for (PermissionSet ps : liveMandates) {
        //     psaList.add(new PermissionSetAssignment(
        //         AssigneeId = u.Id,
        //         PermissionSetId = ps.Id
        //     ));
        // }
        // if (!psaList.isEmpty()) insert psaList;
    }
    
    @isTest
    static void getTabsTest() {
        //User u = [SELECT Id FROM User WHERE FederationIdentifier = 'RSSMRA80A01H501U' LIMIT 1];

        Test.startTest();
        List<MenuStrumentiController.Tabs> tabs = MenuStrumentiController.getTabs();
        Test.stopTest();
    }

    @isTest
    static void getSectionsTest() {
        // User u = [SELECT Id FROM User WHERE FederationIdentifier = 'RSSMRA80A01H501U' LIMIT 1];

        Test.startTest();
        List<MenuStrumentiController.Section> sections = MenuStrumentiController.getSections('MENU_STRUMENTI_UNIPOL');
        Test.stopTest();
    }

    @isTest
    static void getTreeByContextTest() {
        // User u = [SELECT Id FROM User WHERE FederationIdentifier = 'RSSMRA80A01H501U' LIMIT 1];

        Test.startTest();
        List<MenuStrumentiEngine.SectionWrapper> sections = MenuStrumentiController.getTreeByContext('MENU_STRUMENTI_UNIPOL', 'ext-123');
        Test.stopTest();
    }

    @isTest
    static void getUserFavoriteTest() {
        // User u = [SELECT Id FROM User WHERE FederationIdentifier = 'RSSMRA80A01H501U' LIMIT 1];
        
        Test.startTest();
        // RecordType "User" per Asset
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        // Asset di test
        Asset ast = new Asset(
            Name = 'TEST',
            User__c = UserInfo.getUserId(),
            Category__c = 'Preferiti',
            Value__c = 'Label Preferito',
            Key__c = 'DEV_TEST',
            Fei_Parameters__c = '{"link":"http://test.com","param":"p1"}',
            Status = 'Registered',
            RecordTypeId = assetRtId
        );
        insert ast;

        List<String> favorites = MenuStrumentiController.getUserFavorite();
        Test.stopTest();
    }

    @isTest
    static void setUserFavoriteTest() {
        Test.startTest();
        MenuStrumentiController.setUserFavorite(
            'TEST',
            'DEV_TEST',
            'FEI',
            null,
            null,
            '',
            '',
            'ext-123'
        );
        Test.stopTest();
    }

    @isTest
    static void removeUserFavoriteTest() {
        Test.startTest();
        // RecordType "User" per Asset
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        // Asset di test
        Asset ast = new Asset(
            Name = 'TEST',
            User__c = UserInfo.getUserId(),
            Category__c = 'Preferiti',
            Value__c = 'Label Preferito',
            Key__c = 'DEV_TEST',
            Fei_Parameters__c = '{"link":"http://test.com","param":"p1"}',
            Status = 'Registered',
            RecordTypeId = assetRtId
        );
        insert ast;

        MenuStrumentiController.removeUserFavorite(ast.Key__c);
        Test.stopTest();
    }

    @isTest
    static void getParamsForFeiTest() {
        Test.startTest();

        FEI_Environment__c fei = new FEI_Environment__c();
        fei.SetupOwnerId = UserInfo.getUserId();
        fei.Environment__c = 'EURO';
        fei.Chrome_Extension_URL__c = 'test';
        fei.Safari_Extension_URL__c = 'test';
        fei.Extension_Required__c = false;
        fei.NOPROD_KeepAlive_Timeout_ms__c = '1000';
        fei.PROD_KeepAlive_Timeout_ms__c = '1000';
        fei.Skip_Extension__c = true;
        fei.KeepAlive_URL__c = 'test';

        insert fei;

        Map<String, String> paramsFei = MenuStrumentiController.getParamsForFei('FEI.LINK.POST', 'ext-123');
        Test.stopTest();
    }

    @isTest
    static void getUserCfTest() {
        Test.startTest();
        String cf = MenuStrumentiController.getUserCf();
        Test.stopTest();
    }
}