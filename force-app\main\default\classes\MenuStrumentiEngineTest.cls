@IsTest
private class MenuStrumentiEngineTest {

    @TestSetup
    static void setupData() {
        // Creiamo custom metadata fittizi
        // NB: per un test reale serve un file .csv con Test.loadData(Menu_Strumenti_Tree_Structure__mdt.sObjectType, 'MenuStrumentiMdt.csv');
        /*Menu_Strumenti_Tree_Structure__mdt root = new Menu_Strumenti_Tree_Structure__mdt(
            DeveloperName = 'ROOT_NODE',
            MasterLabel   = 'Root Nodo',
            Is_Active__c  = true,
            Context__c    = 'CTX1',
            Section__c    = 'SEC1',
            Order__c      = 1,
            Type__c       = 'MENU'
        );
        insert root;

        Menu_Strumenti_Tree_Structure__mdt child = new Menu_Strumenti_Tree_Structure__mdt(
            DeveloperName = 'CHILD_NODE',
            MasterLabel   = 'Child Nodo',
            Is_Active__c  = true,
            Context__c    = 'CTX1',
            Section__c    = 'SEC1',
            Order__c      = 2,
            Type__c       = 'FEI',
            Parent__c     = 'ROOT_NODE'
        );
        insert child;*/
    }

    @IsTest
    static void testGetTreeByContext_Base() {
        Test.startTest();
        List<MenuStrumentiEngine.SectionWrapper> tree = MenuStrumentiEngine.getTreeByContext('CTX1', null);
        Test.stopTest();
    }

    @IsTest
    static void testGetTreeByContext_WithUserContext() {
        // Qui forziamo il branch con userContext non null e context = MENU_STRUMENTI_UNIPOL
        Test.startTest();
        List<MenuStrumentiEngine.SectionWrapper> tree = MenuStrumentiEngine.getTreeByContext('MENU_STRUMENTI_UNIPOL', 'user123');
        Test.stopTest();
    }

    @IsTest
    static void testGetTreeFromExternalService() {
        // Costruzione manuale della response simulata
        MenuStrumentiService.Children child = new MenuStrumentiService.Children();
        child.id = 'node1';
        child.description = 'Nodo Esterno';
        child.url = 'http://link';
        child.level = 1;
        child.verb = 'GET';
        //child.params = new Map<String, Object>{ 'p1' => 'v1' };

        MenuStrumentiService.Response resp = new MenuStrumentiService.Response();
        resp.children = new List<MenuStrumentiService.Children>{ child };

        Test.startTest();
        MenuStrumentiEngine.SectionWrapper sw = MenuStrumentiEngine.getTreeFromExternalService('CTX2','SEC_EXT', resp);
        Test.stopTest();
    }

    @IsTest
    static void testNodeAndSectionCompareTo() {
        MenuStrumentiEngine.Node n1 = new MenuStrumentiEngine.Node();
        n1.order = 1;
        MenuStrumentiEngine.Node n2 = new MenuStrumentiEngine.Node();
        n2.order = 2;
        Integer cmp = n1.compareTo(n2);

        MenuStrumentiEngine.SectionWrapper s1 = new MenuStrumentiEngine.SectionWrapper();
        s1.order = 1;
        MenuStrumentiEngine.SectionWrapper s2 = new MenuStrumentiEngine.SectionWrapper();
        s2.order = 2;
        Integer cmp2 = s1.compareTo(s2);
    }

    @IsTest
    static void testInvokeIntegrationProcedure() {
        Test.startTest();
        Object result = MenuStrumentiEngine.invokeIntegrationProcedure('FakeProcedure', new Map<String,Object>(), new Map<String,Object>());
        Test.stopTest();
    }
}