public without sharing class MenuStrumentiFei {
    
    public static String getFeiParams(string feiid, string userContext) {
        if(feiid == null) {
            return feiid;
        }

        String payload = null;
        try {
            if(Test.isRunningTest()) {
                payload = [SELECT Id, sendFEIRequest_Payload__c FROM FEI_Settings__mdt WHERE Label =: feiId AND Environment__c = 'EURO' LIMIT 1].sendFEIRequest_Payload__c;
            } else {
                payload = [SELECT Id, sendFEIRequest_Payload__c FROM FEI_Settings__mdt WHERE Label =: feiId AND Environment__c =: FEI_Environment__c.getInstance().Environment__c LIMIT 1].sendFEIRequest_Payload__c;
            }
        } catch(Exception e) {
            System.debug(e);
        }

        String cf = [SELECT FederationIdentifier FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1].FederationIdentifier;

        Map<String, Object> mapObj = new Map<String, Object>();
        String jsonParam = null;
        if(feiid == 'PREVENTIVATORE.UNISALUTE') {
            if(payload != null) {
                Map<String, Object> objJson = (Map<String, Object>) JSON.deserializeUntyped(payload);
                String salt = objJson.containsKey('salt') ? String.valueOf(objJson.get('salt')) : null;

                if(salt == null) {
                    return salt;
                }

                String externalId = null;
                if(userContext != null) {
                    NetworkUser__c ne = [SELECT Id, NetworkUser__c, Agency__c FROM NetworkUser__c WHERE NetworkUser__c = :userContext LIMIT 1];
                    
                    //String azienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId()].IdAzienda__c;
                    if(ne != null) {
                        try {
                            Account acc = [SELECT ExternalId__c FROM Account WHERE Id = :ne.Agency__c];
                            
                            if(acc != null && acc.ExternalId__c != null) {
                                externalId = acc.ExternalId__c.replace('AGE_', '');
                            }
                        } catch(Exception e) {
                            System.debug(e);
                        }
                    }
                }

                mapObj.put('userId', userContext);
                mapObj.put('comp', '4');
                mapObj.put('agem', externalId);
                mapObj.put('agef', externalId);
                mapObj.put('ope', cf);
                mapObj.put('hash', buildSignedQueryString(userContext, '4', externalId, cf, salt));

                jsonParam = JSON.serialize(mapObj);
            }
        }

        return jsonParam;
    }

    private static Boolean isDefined(String s) {
        return s != null && s.trim().length() > 0;
    }

    public static String buildSignedQueryString(String userId, String comp, String age, String ope, String salt) {
        String saltBase64 = salt;

        String calculatedHash = null;
        try {

            Blob saltBlob;
            String saltUtf;
            if (isDefined(saltBase64)) {
                saltBlob = EncodingUtil.base64Decode(saltBase64);
                saltUtf  = saltBlob.toString();
            } else {
                saltBlob = Blob.valueOf('');
                saltUtf  = '';
            }

            String params = 'userId=' + userId + '&comp=' + comp + '&agem=' + age + '&agef=' + age + '&ope=' + ope;

            Blob dataBlob = Blob.valueOf(params + saltUtf);

            Blob hashBlob = Crypto.generateDigest('SHA-256', dataBlob);

            calculatedHash = EncodingUtil.convertToHex(hashBlob);

        } catch(Exception e) {
            System.debug(e);
        }

        return calculatedHash;
    }
}