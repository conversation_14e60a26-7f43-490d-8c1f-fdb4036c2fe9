@isTest
private class MenuStrumentiFeiTest {

    @testSetup
    static void makeData() {
        Id recordTypeAgency = Schema.SObjectType.Account.getRecordTypeInfosByDeveloperName().get('Agency').getRecordTypeId();
        Account agency = new Account(Name = 'TestAgency', RecordTypeId = recordTypeAgency);
        insert agency;

        User testUser = UTL_Datafactory.createUser('tusr', '<EMAIL>', 'User Test', 'Unipol Standard User', '', '<EMAIL>', true);
        testUser.FederationIdentifier = '****************';
        testUser.ExternalId__c = '****************';
        update testUser;

        NetworkUser__c nu = new NetworkUser__c();
        nu.NetworkUser__c = '000000';
        nu.FiscalCode__c = '****************';
        nu.IsActive__c = true;
        nu.Profile__c = 'A';
        nu.Society__c = 'SOC_1';
        nu.Agency__c = agency.Id;
        insert nu;
    }

    @isTest
    static void getFeiParamsPreventivatoreUnisaluteTest() {
        
        Test.startTest();
        User u = [SELECT Id FROM User WHERE FederationIdentifier = '****************' LIMIT 1];

        FEI_Environment__c fei = new FEI_Environment__c();
        fei.SetupOwnerId = UserInfo.getUserId();
        fei.Environment__c = 'EURO';
        fei.Chrome_Extension_URL__c = 'test';
        fei.Safari_Extension_URL__c = 'test';
        fei.Extension_Required__c = false;
        fei.NOPROD_KeepAlive_Timeout_ms__c = '1000';
        fei.PROD_KeepAlive_Timeout_ms__c = '1000';
        fei.Skip_Extension__c = true;
        fei.KeepAlive_URL__c = 'test';

        insert fei;

        System.runAs(u) {
            MenuStrumentiFei.getFeiParams('PREVENTIVATORE.UNISALUTE', '000000');  
        }
        Test.stopTest();
    }
}