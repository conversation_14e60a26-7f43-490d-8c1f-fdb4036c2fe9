@IsTest
private class MenuStrumentiOmnistudioTest {

    @TestSetup
    static void setupData() {
        // Profilo base
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];

        // Creazione utente
        User u = new User(
            FirstName = '<PERSON>',
            LastName  = '<PERSON>',
            Alias     = 'mrossi',
            Email     = '<EMAIL>',
            Username  = 'mario.rossi' + System.currentTimeMillis() + '@test.com',
            FederationIdentifier = 'RSSMRA80A01H501U',
            CommunityNickname = 'mrossi' + System.currentTimeMillis(),
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey   = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ProfileId = p.Id,
            UCA_Permissions__c = '{ "users": [ { "userId": "ext-123", "groups": ["GRP_A"] } ] }'
        );
        insert u;

        // RecordType "User" per Asset
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        // Asset di test
        Asset ast = new Asset(
            Name = 'TEST',
            User__c = u.Id,
            Category__c = 'Preferiti',
            Value__c = 'Label Preferito',
            Key__c = 'DEV_TEST',
            Fei_Parameters__c = '{"link":"http://test.com","param":"p1"}',
            Status = 'Registered',
            RecordTypeId = assetRtId
        );
        insert ast;

        // Metadata fittizio (custom metadata da creare a mano in org per test reale)
        // Qui non si può inserire in test, ma la query ritornerà vuota e verrà testato il ramo "asset senza metadata"
    }

    @IsTest
    static void testGetPreferiti() {
        Test.startTest();

        // RecordType "User" per Asset
        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        // Asset di test
        Asset ast = new Asset(
            Name = 'TEST',
            User__c = UserInfo.getUserId(),
            Category__c = 'Preferiti',
            Value__c = 'Label Preferito',
            Key__c = 'DEV_TEST',
            Fei_Parameters__c = '{"link":"http://test.com","param":"p1"}',
            Status = 'Registered',
            RecordTypeId = assetRtId
        );
        Asset ast2 = new Asset(
            Name = 'UN_AF_COMU_MONITOR',
            User__c = UserInfo.getUserId(),
            Category__c = 'Preferiti',
            Value__c = 'Label Preferito',
            Key__c = 'UN_AF_COMU_MONITOR',
            Fei_Parameters__c = '',
            Status = 'Registered',
            RecordTypeId = assetRtId
        );

        insert new List<Asset>{ast, ast2};
        
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('getPreferiti', input, output);
        Test.stopTest();
    }

    @IsTest
    static void testGetLDAPStrumentiHome() {
        Test.startTest();
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('getLDAPStrumentiHome', input, output);
        Test.stopTest();
    }

    @IsTest
    static void testGetModaleRinnovi() {
        Test.startTest();
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('getModaleRinnovi', input, output);
        Test.stopTest();
    }

    @IsTest
    static void testRemovePreferito() {
        Test.startTest();
        Map<String, Object> input = new Map<String, Object>();
        input.put('developerName', 'DEV_TEST');
        Map<String, Object> output = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('removePreferito', input, output);

        // Caso senza developerName
        Map<String, Object> input2 = new Map<String, Object>();
        Map<String, Object> output2 = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('removePreferito', input2, output2);
        Test.stopTest();
    }

    @IsTest
    static void testMetodoSconosciuto() {
        Test.startTest();
        Map<String, Object> input = new Map<String, Object>();
        Map<String, Object> output = new Map<String, Object>();
        new MenuStrumentiOmnistudio().invokeMethod('unknownMethod', input, output);
        Test.stopTest();
    }

    @IsTest
    static void testCallWrapper() {
        Test.startTest();
        MenuStrumentiOmnistudio cls = new MenuStrumentiOmnistudio();
        Map<String, Object> args = new Map<String, Object>();
        args.put('input', new Map<String, Object>());
        args.put('output', new Map<String, Object>());
        args.put('options', new Map<String, Object>());

        // call su getPreferiti
        cls.call('getPreferiti', args);

        // call su metodo sconosciuto
        cls.call('abc', args);
        Test.stopTest();
    }

    /*@IsTest
    static void testSafeParams() {
        Test.startTest();
        // safeParams con JSON valido
        Object parsed = Test.invokePrivateMethod(
            MenuStrumentiOmnistudio.class,
            'safeParams',
            new List<Object>{ '{"param":"ok"}' }
        );

        // safeParams con stringa non JSON
        Object fallback = Test.invokePrivateMethod(
            MenuStrumentiOmnistudio.class,
            'safeParams',
            new List<Object>{ 'notjson' }
        );

        // safeParams con stringa vuota
        Object empty = Test.invokePrivateMethod(
            MenuStrumentiOmnistudio.class,
            'safeParams',
            new List<Object>{ '' }
        );
        Test.stopTest();
    }*/
}