@isTest
public class OmnistudioUtilsTest {
    @TestSetup
    static void makeData() {
        Account testAgencyAccount = new Account(Name = 'Test Agency', ExternalId__c = 'AGE_12345');
        
        Account testClientAccount = new Account(Name = 'Test Client', ExternalId__c = 'STNBLZ66A10H666I');
        insert new List<Account> {testAgencyAccount, testClientAccount};
        
        FinServ__ReciprocalRole__c role = new FinServ__ReciprocalRole__c(Name = 'Agenzia', FinServ__InverseRole__c = 'Compagnia');
        insert role;
        
        FinServ__AccountAccountRelation__c accountAccountRelation = new FinServ__AccountAccountRelation__c(
            FinServ__Account__c = testClientAccount.Id, FinServ__RelatedAccount__c = testAgencyAccount.Id, FinServ__Role__c = role.Id);
        insert accountAccountRelation;
        
        insert new List<Asset>{new Asset(Name ='STNBLZ66A10H666I_HELL_SOULS_NUMBER', MasterRecordId__c = accountAccountRelation.Id, Key__c = 'HELL_SOULS_NUMBER', Value__c = '421'),
            new Asset(Name ='STNBLZ66A10H666I_HELL_SOULS_NUMBER', MasterRecordId__c = accountAccountRelation.Id, Key__c = 'HELL_INCOME_PER_SOUL', Value__c = '1500')};

        User testUser = new User(
            Username = '<EMAIL>',
            Alias = 'tuser',
            Email = '<EMAIL>',
            EmailEncodingKey = 'UTF-8',
            LastName = 'User',
            LanguageLocaleKey = 'en_US',
            LocaleSidKey = 'en_US',
            TimeZoneSidKey = 'Europe/Rome',
            ProfileId = [SELECT Id FROM Profile WHERE Name = 'Standard User']
            .Id,
            IdAzienda__c = testAgencyAccount.Id
        );
        insert testUser;
    }

    @isTest
    static void testValidateContactEmail() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => '<EMAIL>',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('<EMAIL>', output.get('result'), 'Result should be the same as the input');
    }

    @isTest
    static void testValidateContactPhone() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'CELL',
            'contactValue' => '+1234567890',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('+1234567890', output.get('result'), 'Result should be the same as the input');
    }

    @isTest
    static void testValidateContactInvalidEmail() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => 'invalid-email',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('invalid-email', output.get('result'), 'Result should be the same as the input');
        //System.assertEquals('Il recapito inserito non è valido', output.get('errorMessage'), 'Error message should be "Il recapito inserito non è valido"');
    }

    @isTest
    static void testValidateContactInvalidPhone() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'CELL',
            'contactValue' => '12345',
            'contactTarget' => 'target',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('12345', output.get('result'), 'Result should be the same as the input');
        //System.assertEquals('Il recapito inserito non è valido', output.get('errorMessage'), 'Error message should be "Il recapito inserito non è valido"');
    }

    @isTest
    static void testValidateContactMissingFields() {
        OmnistudioUtils utils = new OmnistudioUtils();
        Map<String, Object> inputs = new Map<String, Object>{
            'contactType' => 'MAIL',
            'contactValue' => '<EMAIL>',
            'inputMode' => 'INSERT'
        };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        Test.startTest();
        utils.call('validateContact', args);
        Test.stopTest();

        //System.assertEquals(false, output.get('success'), 'Success should be false');
        //System.assertEquals('I campi obbligatori non sono stati compilati', output.get('errorMessage'), 'Error message should be "I campi obbligatori non sono stati compilati"');
    }

    @isTest
    static void testGetAgencyCode() {
        User testUser = [SELECT Id FROM User WHERE Username = '<EMAIL>' LIMIT 1];

        Map<String, Object> inputs = new Map<String, Object>{ 'userId' => testUser.Id };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('getAgencyCode', args);
        Test.stopTest();

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('12345', output.get('result'), 'Result should be the agency code');
    }

    @isTest
    static void testUpdateObject() {
        Account target = [SELECT Id FROM Account LIMIT 1];

        Map<String, Object> fields = new Map<String, Object>{
            'Id' => target.Id,
            'Name' => 'Updated Agency',
            'ExternalId__c' => 'AGE_54321'
        };

        Map<String, Object> inputs = new Map<String, Object>{ 'objectApiName' => 'Account', 'fields' => fields };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('updateObject', args);
        Test.stopTest();

        Account updatedTarget = [SELECT Id, Name, ExternalId__c FROM Account WHERE Id = :target.Id LIMIT 1];

        //System.assertEquals(true, output.get('success'), 'Success should be true');
        //System.assertEquals('Updated Agency', updatedTarget.Name, 'Name should be updated');
        //System.assertEquals('AGE_54321', updatedTarget.ExternalId__c, 'ExternalId__c should be updated');
    }

    @isTest
    static void testNegativeUpdateObject() {
        Account target = [SELECT Id FROM Account LIMIT 1];

        Map<String, Object> fields = new Map<String, Object>{
            'Id' => target.Id,
            'Name' => 'Updated Agency',
            'ExternalId__c' => 'AGE_54321'
        };

        Map<String, Object> inputs = new Map<String, Object>{ 'fields' => fields };
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> args = new Map<String, Object>{
            'input' => inputs,
            'output' => output,
            'options' => new Map<String, Object>()
        };

        OmnistudioUtils utils = new OmnistudioUtils();

        Test.startTest();
        utils.call('updateObject', args);
        Test.stopTest();

        Account updatedTarget = [SELECT Id, Name, ExternalId__c FROM Account WHERE Id = :target.Id LIMIT 1];

        //System.assertEquals(false, output.get('success'), 'Success should be false');
    }

    //Guy De Roquefeuil: added test method
    @isTest
    static void testSetDatiSocioEconomiciRequestBody_datiAttributi_NOATTRIBUTI(){
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{ 'dati_attributi' => new List<String>{'', '', '', ''} },
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OmnistudioUtils().call('setDatiSocioEconomiciRequestBody_datiAttributi', args);
        List<Map<String, String>> datiAttributi = (List<Map<String, String>>) ((Map<String, Object>) args.get('output')).get('datiAttributi');
        System.assertEquals(true, datiAttributi.isEmpty());        
    }
    
    //Guy De Roquefeuil: added test method
    @isTest
    static void testSetDatiSocioEconomiciRequestBody_datiAttributi_WITHATTRIBUTI(){
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{'dati_attributi' => new List<String>{'SOC', 'LAV', 'ACQ'} },
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OmnistudioUtils().call('setDatiSocioEconomiciRequestBody_datiAttributi', args);
        List<Map<String, String>> datiAttributi = (List<Map<String, String>>) ((Map<String, Object>) args.get('output')).get('datiAttributi');
        System.assertEquals(3, datiAttributi.size());
        for(Map<String, String> datoAttributo : datiAttributi)
            System.assertEquals(false, String.isBlank(datoAttributo.get('attributo')));
    }
    
    //Guy De Roquefeuil: added test method
    @isTest
    static void testRetrieveKPI_withSociety(){
        FinServ__AccountAccountRelation__c accountAccountRelation = [SELECT Id FROM FinServ__AccountAccountRelation__c LIMIT 1].get(0);
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{'aarSociety' => accountAccountRelation.Id},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OmnistudioUtils().call('retrieveKpiAnag', args);
        List<Map<String, Object>> accountKpiList = (List<Map<String, Object>>) ((Map<String, Object>) args.get('output')).get('kpiList');
        System.assertEquals(2, accountKpiList.size());
        for(Map<String, Object> accountKpi : accountKpiList)
            System.assertEquals(false, accountKpi == null);
    }
    
    //Guy De Roquefeuil: added test method
    @isTest
    static void testRetrieveKPI_withAgency(){
        FinServ__AccountAccountRelation__c accountAccountRelation = [SELECT Id FROM FinServ__AccountAccountRelation__c LIMIT 1].get(0);
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{'aarAgency' => accountAccountRelation.Id},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OmnistudioUtils().call('retrieveKpiAnag', args);
        List<Map<String, Object>> accountKpiList = (List<Map<String, Object>>) ((Map<String, Object>) args.get('output')).get('kpiList');
        System.assertEquals(2, accountKpiList.size());
        for(Map<String, Object> accountKpi : accountKpiList)
            System.assertEquals(false, accountKpi == null);
    }
    
    //Guy De Roquefeuil: added test method
    @isTest
    static void testUpsertKPI(){
        List<Asset> oldAccountKpiList = [SELECT Id, Name, MasterRecordId__c FROM Asset];
        Map<String, Object> kpiListToBeUpserted = new Map<String, Object>
        {'ANAG2_DATENASCITAFIGLI' => new List<String>{'2002-04-03'}, 'HELL_SOULS_NUMBER' => '425'};
   		
        //NO accountAccountRelation
        Map<String, Object> args = new Map<String, Object>{
            'input' => new Map<String, Object>{'kpi_list_to_be_upserted' => kpiListToBeUpserted, 'society_account_account_relation_id' => ''},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        Object result = new OmnistudioUtils().call('upsertKpiAnag', args);
        List<Asset> newAccountKpiList = [SELECT Id, Name, MasterRecordId__c FROM Asset];
        System.assertEquals(2, newAccountKpiList.size());
        System.assertEquals(true, oldAccountKpiList == newAccountKpiList);
        
        //WITH accountAccountRelation and disabled upsert
        args = new Map<String, Object>{
            'input' => new Map<String, Object>{
                'kpi_list_to_be_upserted' => kpiListToBeUpserted,
                'society_account_account_relation_id' => oldAccountKpiList.get(0).MasterRecordId__c,
                'disable_upsert' => true},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        result = new OmnistudioUtils().call('upsertKpiAnag', args);
        newAccountKpiList = [SELECT Id, Name, MasterRecordId__c FROM Asset];
        System.assertEquals(2, newAccountKpiList.size());
        System.assertEquals(true, oldAccountKpiList == newAccountKpiList);
        
        //WITH accountAccountRelation and NO disabled upsert 
        oldAccountKpiList = [SELECT Id, Name, MasterRecordId__c FROM Asset];
        args = new Map<String, Object>{
            'input' => new Map<String, Object>{
                'kpi_list_to_be_upserted' => kpiListToBeUpserted, 
                'society_account_account_relation_id' => oldAccountKpiList.get(0).MasterRecordId__c},
            'output' => new Map<String, Object>(),
            'options' => new Map<String, Object>()
        };
        
        result = new OmnistudioUtils().call('upsertKpiAnag', args);
        newAccountKpiList = [SELECT Id, Name, MasterRecordId__c FROM Asset];
        System.assertEquals(3, newAccountKpiList.size());
        System.assertEquals(true, oldAccountKpiList != newAccountKpiList);
    }
}