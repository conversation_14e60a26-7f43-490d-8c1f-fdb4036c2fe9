public with sharing class QuoteWSWrapper {
    @AuraEnabled
    public String id;
    @AuraEnabled
    public String externalId;
    @AuraEnabled
    public String recordTypeId;
    @AuraEnabled
    public String domainType;
    @AuraEnabled
    public String name;
    @AuraEnabled
    public String accountId;
    @AuraEnabled
    public String status;
    @AuraEnabled
    public Date expirationDate;
    @AuraEnabled
    public String billingStreet;
    @AuraEnabled
    public String billingCity;
    @AuraEnabled
    public String billingState;
    @AuraEnabled
    public String billingPostalCode;
    @AuraEnabled
    public String billingCountry;
    @AuraEnabled
    public Double billingLatitude;
    @AuraEnabled
    public Double billingLongitude;
    @AuraEnabled
    public String quoteToStreet;
    @AuraEnabled
    public String quoteToCity;
    @AuraEnabled
    public String quoteToState;
    @AuraEnabled
    public String quoteToPostalCode;
    @AuraEnabled
    public String quoteToCountry;
    @AuraEnabled
    public Double quoteToLatitude;
    @AuraEnabled
    public Double quoteToLongitude;
    @AuraEnabled
    public String billingName;
    @AuraEnabled
    public String email;
    @AuraEnabled
    public String phone;
    @AuraEnabled
    public List<String> areasOfNeed;
    @AuraEnabled
    public Datetime createdDate;
    @AuraEnabled
    public Decimal quoteAmount;
    @AuraEnabled
    public String linkUnica;
    @AuraEnabled
    public Datetime createdDateTPD;
    @AuraEnabled
    public String folderId;
    @AuraEnabled
    public String engagementPoint;
    @AuraEnabled
    public Double monthlyContribution;
    @AuraEnabled
    public String cip;
    @AuraEnabled
    public String sourceSystemIdentifier;
    @AuraEnabled
    public String serviceProviderIdentifier;
    @AuraEnabled
    public Date policyEffectiveDate;
    @AuraEnabled
    public Date policyExpirationDate;
    @AuraEnabled
    public List<CoverageWSWrapper> coverages;
    @TestVisible
    private static Boolean testCoverage { get; set; }
}