@IsTest
private class UserUtilsTest {

    @TestSetup
    static void setupData() {
        // Creazione di un profilo valido
        Profile p = [SELECT Id FROM Profile WHERE Name = 'Standard User' LIMIT 1];

        // Creazione utente di test
        User u = new User(
            FirstName = '<PERSON>',
            LastName  = 'Rossi',
            Alias     = 'mrossi',
            Email     = '<EMAIL>',
            Username  = 'mario.rossi' + System.currentTimeMillis() + '@test.com',
            FederationIdentifier = '****************',
            CommunityNickname = 'mrossi' + System.currentTimeMillis(),
            TimeZoneSidKey = 'Europe/Rome',
            LocaleSidKey   = 'it_IT',
            EmailEncodingKey = 'UTF-8',
            LanguageLocaleKey = 'it',
            ProfileId = p.Id,
            UCA_Permissions__c = '{ "users": [ { "userId": "ext-123", "groups": ["GRP_A","GRP_B"] } ] }'
        );
        insert u;

        // Creazione account (Agenzia)
        Account acc = new Account(
            Name = 'Agenzia Test',
            ExternalId__c = 'AGE_001'
        );
        insert acc;

        // Creazione NetworkUser__c
        NetworkUser__c net1 = new NetworkUser__c(
            Agency__c = acc.Id,
            FiscalCode__c = u.FederationIdentifier,
            ExternalId__c = 'ext-123',
            IsActive__c = true,
            Society__c = 'SOC_1',
            NetworkUser__c = 'USR_NET_1',
            Preferred__c = true
        );
        insert net1;

        // PermissionSet
        /*PermissionSet ps = [SELECT Id FROM PermissionSet WHERE Name = 'Standard_User' LIMIT 1];
        insert new PermissionSetAssignment(
            AssigneeId = u.Id,
            PermissionSetId = ps.Id
        );*/
    }

    @IsTest
    static void testGetUserById() {
        User u = [SELECT Id FROM User WHERE FederationIdentifier = '****************' LIMIT 1];
        User usr = UserUtils.getUser(u.Id);
    }

    @IsTest
    static void testGetUserByFiscalCode() {
        User usr = UserUtils.getUserByFiscalCode('****************');
    }

    @IsTest
    static void testGetUserByUserCode() {
        User usr = UserUtils.getUserByUserCode('ext-123');
    }

    @IsTest
    static void testGetUserNetworksByFederationId() {
        List<NetworkUser__c> nets = UserUtils.getUserNetworksByFederationId('****************');
    }

    @IsTest
    static void testGetUserNetworksByAgency() {
        Account acc = [SELECT Id FROM Account WHERE ExternalId__c = 'AGE_001' LIMIT 1];
        List<NetworkUser__c> nets = UserUtils.getUserNetworksByAgency(acc.Id);
    }

    @IsTest
    static void testGetUserPermissionSets() {
        User u = [SELECT Id FROM User WHERE FederationIdentifier = '****************' LIMIT 1];
        List<String> ps = UserUtils.getUserPermissionSets(u.Id);
    }

    @IsTest
    static void testUserContextById() {
        User u = [SELECT Id FROM User WHERE FederationIdentifier = '****************' LIMIT 1];
        UserUtils.UserContext ctx = UserUtils.getUserContextById(u.Id);
    }

    @IsTest
    static void testUserContextByFiscalCode() {
        UserUtils.UserContext ctx = UserUtils.getUserContextByFiscalCode('****************');
    }

    @IsTest
    static void testUserContextByUserCode() {
        UserUtils.UserContext ctx = UserUtils.getUserContextByUserCode('ext-123');
    }

    @IsTest
    static void testUserContextByInput() {
        String inputJson = '{"userId": null, "fiscalCode":"****************"}';
        UserUtils.UserContext ctx = UserUtils.getUserContextByInput(inputJson);
    }

    @IsTest
    static void testGetUserContext() {
        UserUtils.UserContext ctx = UserUtils.getUserContext();
    }
}