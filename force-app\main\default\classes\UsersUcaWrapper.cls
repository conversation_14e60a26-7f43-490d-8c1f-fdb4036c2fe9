public class UsersUcaWrapper {

    @AuraEnabled
    public List<UserUcaWrapper> users;

    public class UserUcaWrapper {
        @AuraEnabled
        public String userId;

        @AuraEnabled
        public List<String> groups;

        @AuraEnabled
        public List<String> cin;
    }

    public static UsersUcaWrapper fromJson(String jsonString) {
        if(jsonString == null || String.isBlank(jsonString)) {
            return null;
        }
        
        UsersUcaWrapper usersUcaWrapper = (UsersUcaWrapper) JSON.deserialize(jsonString, UsersUcaWrapper.class);

        // Enrich groups: for each group starting with AA or AP, also add the version without the prefix
        if (usersUcaWrapper != null && usersUcaWrapper.users != null) {
            for (UserUcaWrapper userUcaWrapper : usersUcaWrapper.users) {
                if (userUcaWrapper == null) continue;
                if (userUcaWrapper.groups == null) continue;

                // Collect new entries to avoid concurrent modification issues
                List<String> groupNamesToAdd = new List<String>();
                for (String groupName : userUcaWrapper.groups) {
                    if (groupName == null) continue;
                    if (groupName.startsWith('AA') || groupName.startsWith('AP')) {
                        // Ensure we have characters after the prefix
                        if (groupName.length() > 2) {
                            String groupNameWithoutPrefix = groupName.substring(2);
                            // Avoid duplicates if already present
                            if (!userUcaWrapper.groups.contains(groupNameWithoutPrefix) && !groupNamesToAdd.contains(groupNameWithoutPrefix)) {
                                groupNamesToAdd.add(groupNameWithoutPrefix);
                            }
                        }
                    }
                }
                if (!groupNamesToAdd.isEmpty()) {
                    userUcaWrapper.groups.addAll(groupNamesToAdd);
                }
            }
        }

        return usersUcaWrapper;
    }

    public String toJson() {
        return JSON.serialize(this);
    }
}
