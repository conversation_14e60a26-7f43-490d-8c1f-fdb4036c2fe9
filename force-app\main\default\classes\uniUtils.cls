global without sharing class uniUtils implements System.Callable {

    public static final Map<String,String> MIME = new Map<String,String>{
        'aac' => 'data:audio/aac;base64,,',
        'abw' => 'data:application/x-abiword;base64,',
        'apng' => 'data:image/apng;base64,',
        'arc' => 'data:application/x-freearc;base64,',
        'avif' => 'data:image/avif;base64,',
        'avi' => 'data:video/x-msvideo;base64,',
        'azw' => 'data:application/vnd.amazon.ebook;base64,',
        'bin' => 'data:application/octet-stream;base64,',
        'bmp' => 'data:image/bmp;base64,',
        'bz' => 'data:application/x-bzip;base64,',
        'bz2' => 'data:application/x-bzip2;base64,',
        'cda' => 'data:application/x-cdf;base64,',
        'csh' => 'data:application/x-csh;base64,',
        'css' => 'data:text/css;base64,',
        'csv' => 'data:text/csv;base64,',
        'doc' => 'data:application/msword;base64,',
        'docx' => 'data:application/vnd.openxmlformats-officedocument.wordprocessingml.document;base64,',
        'eot' => 'data:application/vnd.ms-fontobject;base64,',
        'epub' => 'data:application/epub+zip;base64,',
        'gz' => 'data:application/gzip.;base64,',
        'gif' => 'data:image/gif;base64,',
        'html' => 'data:text/html;base64,',
        'htm' => 'data:text/html;base64,',
        'ico' => 'data:image/vnd.microsoft.icon;base64,',
        'ics' => 'data:text/calendar;base64,',
        'jar' => 'data:application/java-archive;base64,',
        'jpg' => 'data:;base64,',
        'jpeg' => 'data:image/jpeg;base64,',
        'js' => 'data:text/javascript;base64,',
        'json' => 'data:application/json;base64,',
        'jsonld' => 'data:application/ld+json;base64,',
        'md' => 'data:text/markdown;base64,',
        'midi' => 'data:audio/midi;base64,',
        'mid' => 'data:audio/midi;base64,',
        'mjs' => 'data:text/javascript;base64,',
        'mp3' => 'data:audio/mpeg;base64,',
        'mp4' => 'data:video/mp4;base64,',
        'mpeg' => 'data:video/mpeg;base64,',
        'mpkg' => 'data:application/vnd.apple.installer+xml;base64,',
        'odp' => 'data:application/vnd.oasis.opendocument.presentation;base64,',
        'ods' => 'data:application/vnd.oasis.opendocument.spreadsheet;base64,',
        'odt' => 'data:application/vnd.oasis.opendocument.text;base64,',
        'oga' => 'data:audio/ogg;base64,',
        'ogv' => 'data:video/ogg;base64,',
        'ogx' => 'data:application/ogg;base64,',
        'opus' => 'data:audio/ogg;base64,',
        'otf' => 'data:font/otf;base64,',
        'png' => 'data:image/png;base64,',
        'pdf' => 'data:application/pdf;base64,',
        'php' => 'data:application/x-httpd-php;base64,',
        'ppt' => 'data:application/vnd.ms-powerpoint;base64,',
        'pptx' => 'data:application/vnd.openxmlformats-officedocument.presentationml.presentation;base64,',
        'rar' => 'data:application/vnd.rar;base64,',
        'rtf' => 'data:application/rtf;base64,',
        'sh' => 'data:application/x-sh;base64,',
        'svg' => 'data:image/svg+xml;base64,',
        'tar' => 'data:application/x-tar;base64,',
        'tif' => 'data:image/tiff;base64,',
        'tiff' => 'data:image/tiff;base64,',
        'ts' => 'data:video/mp2t;base64,',
        'ttf' => 'data:font/ttf;base64,',
        'txt' => 'data:text/plain;base64,',
        'vsd' => 'data:application/vnd.visio;base64,',
        'wav' => 'data:audio/wav;base64,',
        'weba' => 'data:audio/webm;base64,',
        'webm' => 'data:video/webm;base64,',
        'webmanifest' => 'data:application/manifest+json;base64,',
        'webp' => 'data:image/webp;base64,',
        'woff' => 'data:font/woff;base64,',
        'woff2' => 'data:font/woff2;base64,',
        'xhtml' => 'data:application/xhtml+xml;base64,',
        'xls' => 'data:application/vnd.ms-excel;base64,',
        'xlsx' => 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,',
        'xml' => 'data:application/xml;base64,',
        'xul' => 'data:application/vnd.mozilla.xul+xml;base64,',
        'zip' => 'data:application/zip.;base64,',
        '3gp' => 'data:video/3gpp;base64,',
        '3g2' => 'data:video/3gpp2;base64,',
        '7z' => 'data:application/x-7z-compressed;base64,'
    };


    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');
        return invokeMethod(action, input, output, options);
    }

    public Boolean invokeMethod(String methodName, Map<String,Object> inputMap, Map<String,Object> outMap, Map<String,Object> options){

        try {
            if (methodName.equals('populatePicklistCompany')){
                populatePicklistCompany(inputMap, outMap, options);
            }
            if (methodName.equals('societyPicklist')) {
                societyPicklist(inputMap, outMap, options);
            }
            if (methodName.equals('uploadDocument')){
                uploadDocument(inputMap, outMap, options);
            }
            if (methodName.equals('getDocumentId')){
                getDocumentId(inputMap, outMap, options);
            }
            if (methodName.equals('filterListForArchivio')){
                filterListForArchivio(inputMap, outMap, options);
            }
            if (methodName.equals('setDocumentCountByCompany')){
                setDocumentCountByCompany(inputMap, outMap, options);
            }
            if (methodName.equals('checkAttachmentSize')) {
                checkAttachmentSize(inputMap, outMap, options);
            }
            if (methodName.equals('mergeListForVisualizzaStorico')) {
                mergeListForVisualizzaStorico(inputMap, outMap, options);
            }
            if (methodName.equals('parseResponseJson')) {
                parseResponseJson(inputMap, outMap, options);
            }
            if (methodName.equals('populatePicklistCompany2')) {
                populatePicklistCompany2(inputMap, outMap, options);
            }
        }catch(Exception e) {
            System.debug(LoggingLevel.ERROR, 'Exception: ' + e.getMessage() + ' ' + e.getStackTraceString());
        }
        return true;
    }

    private void populatePicklistCompany(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('%%inputMap: ' + inputMap);
        List<Map<String, Object>> optionList = new List<Map<String, Object>>();
        Set<String> addedValues = new Set<String>();

        Map<String, String> allowedMandates = new Map<String, String>{
            'MandatoUnipolSai' => 'SOC_1',
            'MandatoUniSalute' => 'SOC_4',
            'MandatoUnipolRental' => 'UnipolRental',
            'MandatoUnipolTech' => 'UnipolTech'
        };

        List<User> user = new List<User>();
        if (Schema.sObjectType.User.isAccessible()) {
            user = [SELECT Id, IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        }
        if (user.isEmpty() || user[0].IdAzienda__c == null) {
            outMap.put('options', optionList);
            return;
        }

        Set<String> userPermNames = new Set<String>();
        if (Schema.sObjectType.PermissionSetAssignment.isAccessible()) {
            for (PermissionSetAssignment psa : [
                SELECT PermissionSet.Name FROM PermissionSetAssignment
                WHERE AssigneeId = :UserInfo.getUserId() AND PermissionSet.Name IN :allowedMandates.keySet()
            ]) {
                userPermNames.add(psa.PermissionSet.Name);
            }
        }

        if (userPermNames.contains('MandatoUnipolRental')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolRental', 'value' => 'UnipolRental'});
            addedValues.add('UnipolRental');
        }

        if (userPermNames.contains('MandatoUnipolTech')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolTech', 'value' => 'UnipolTech'});
            addedValues.add('UnipolTech');
        }

        List<FinServ__AccountAccountRelation__c> accRelation = new List<FinServ__AccountAccountRelation__c>();
        if (Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible()) {
            accRelation = [
                SELECT FinServ__RelatedAccount__r.Name, FinServ__RelatedAccount__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c = :user[0].IdAzienda__c
                AND RecordType.DeveloperName = 'AgencySociety'
            ];
        }
        for (FinServ__AccountAccountRelation__c acc : accRelation) {
            String extId = acc.FinServ__RelatedAccount__r.ExternalId__c;
            String label = acc.FinServ__RelatedAccount__r.Name;
            for (String perm : userPermNames) {
                if (allowedMandates.get(perm) == extId && !addedValues.contains(label)) {
                    optionList.add(new Map<String, Object>{'name' => extId, 'value' => label});
                    addedValues.add(label);
                }
            }
        }
        system.debug('%%optionList: ' + optionList);
        outMap.put('options', optionList);
    }

    public void societyPicklist(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug(inputMap);
        List<Map<String, String>> optionsList = new List<Map<String, String>>();
        User u = [SELECT id, FiscalCode__c, IdAzienda__c, Profile.Name FROM User WHERE id =: UserInfo.getUserId()];
        System.debug('User Id: ' + u.id);
        if(u.Profile.Name == 'System Administrator') {
            Map<String, String> optMap1 = new Map<String, String>();
            optMap1.put('name', 'unipolsai');
            optMap1.put('value', 'Unipol');
            optionsList.add(optMap1);
            Map<String, String> optMap2 = new Map<String, String>();
            optMap2.put('name', 'unisalute');
            optMap2.put('value', 'Unisalute');
            optionsList.add(optMap2);
            outMap.put('options', optionsList);
        } else {
            Set<String> societySet = new Set<String>();
            for(NetworkUser__c nu : [select Id, Agency__c, ExternalId__c, FiscalCode__c, IsActive__c, NetworkUser__c, PermissionSets__c, Profile__c, Role__c, Society__c, User__c from NetworkUser__c where FiscalCode__c =: u.FiscalCode__c and IsActive__c = true and Agency__c =: u.IdAzienda__c ]) {
                societySet.add(nu.Society__c);
            }
            System.debug(societySet);    
            for(String society : societySet) {
                Map<String, String> optMap = new Map<String, String>();
                if(society == 'SOC_1') {
                    optMap.put('name', 'unipolsai');
                    optMap.put('value', 'Unipol');
                    optionsList.add(optMap);
                } else if(society == 'SOC_4'){
                    optMap.put('name', 'unisalute');
                    optMap.put('value', 'Unisalute');
                    optionsList.add(optMap);
                } else continue;
            }
            outMap.put('options', optionsList);
        }
    }

    private void uploadDocument(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Map<String,Object> body = inputMap.containsKey('bodyFinal') ? (Map<String,Object>)inputMap.get('bodyFinal') : new Map<String,Object>();
        String integrationId = inputMap.containsKey('integrationId') ? (String)inputMap.get('integrationId') : '';
        String documentId = inputMap.containsKey('documentId') ? (String)inputMap.get('documentId') : '';

        //Recupero il Base64
        String base64file = '';
        Blob versionData;
        String fileType = '';
        if(documentId != ''){
            ContentVersion version = [SELECT islatest, FileType, sourcesystemidentifier__c, contentdocumentid, id, versiondata FROM ContentVersion WHERE ContentDocumentId =: documentId AND IsLatest = true LIMIT 1]; 
            versionData = version.versiondata;
            fileType = version.FileType.toLowerCase();
        }
        if(versionData != null){
            base64file = MIME.containsKey(fileType) ? MIME.get(fileType) : '';
            base64file += EncodingUtil.base64Encode(versionData);
            //System.debug('%%RF base64file: ' + base64file);
            //Preparazione del body
            body.put('file',base64file);
            //System.debug('%%RF body: ' + body);
        }
    
        String finalBody = JSON.serialize(body);
        System.debug('%%RF finalBody: ' + finalBody);
        IntegrationSettings__mdt mdt = IntegrationSettings__mdt.getInstance(integrationId);
        ConnectApi.NamedCredential nc = ConnectApi.NamedCredentials.getNamedCredential(mdt.NamedCredential__c);

        //Richiamo del Servizio
        String action = '';
        Map<String,Object> inputMapForIntegration = new Map<String,Object>();
        Map<String,Object> outputMapForIntegration = new Map<String,Object>();

        inputMapForIntegration.put('body',finalBody);
        inputMapForIntegration.put('params','');
        inputMapForIntegration.put('integrationId',integrationId);
        System.debug('%%RF inputMapForIntegration: ' + inputMapForIntegration);

        Map<String,Object> args = new Map<String,Object>{
            'input' => inputMapForIntegration,
            'output' => outputMapForIntegration
        };
        try{
            IntegrationUtilityAction integrationAction = new IntegrationUtilityAction();
            Object response = integrationAction.call(action, args);
            System.debug('@Gp response: ' + response);
            outMap.put('response',JSON.deserializeUntyped(JSON.serialize(response)));
            outMap.put('request', body);
            outMap.put('endPoint', nc.calloutUrl + mdt.Endpoint__c);
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Exception during integration call: ' + e.getMessage());
            outMap.put('response','Exception during integration call: ' + e.getMessage());
            throw new AuraHandledException('Error during document upload: ' + e.getMessage());
        }
    }

    private void getDocumentId(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String ciu = inputMap.containsKey('ciu') ? (String) inputMap.get('ciu') : null;
        String nomeCompagnia = inputMap.containsKey('nomeCompagnia') ? (String) inputMap.get('nomeCompagnia') : null;
        String tipoDelDocumento = inputMap.containsKey('tipoDelDocumento') ? (String) inputMap.get('tipoDelDocumento') : null;
        
        //RECUPERO ID DEL DOCUMENTO LOGICA WORKAROUND - START
        String documentId = '-';
        String codiceDocumentaleAllegato = '-';
        try{
            Map<String, Object> ipInput = new Map<String, Object>{'ciu' => ciu, 'compagnia' => nomeCompagnia};
            Map<String, Object> ipOutput = (Map<String, Object>) omnistudio.IntegrationProcedureService.runIntegrationService('RestGet_FeaDocumenti', ipInput, null);   
            System.debug('@GP ipOutput: ' + ipOutput);
            List<Object> documentList = (ipOutput.containsKey('results')) ? (List<Object>)ipOutput.get('results') : new List<Object>();
            if(!documentList.isEmpty()){
                for(Object document : documentList){
                    Map<String,Object> docMap = (Map<String,Object>)document;
                    if(tipoDelDocumento.equalsIgnoreCase((String)docMap.get('tipoDelDocumento')) && ((String)docMap.get('statoDelDocumento')).equalsIgnoreCase('V')){
                        documentId = String.valueOf(docMap.get('id'));
                        codiceDocumentaleAllegato = String.valueOf(docMap.get('codiceDocumentaleAllegato'));    
                        break;
                    }
                    System.debug('@GP Non è stato trovato nessun documento valido');
                }
            }
            System.debug('@GP documentId: ' + documentId);
            outMap.put('documentId',documentId);
            Integer integerDocumentId =  (!String.isBlank(documentId) && documentId != '-') ? Integer.valueOf(documentId) : null;
            outMap.put('codiceDocumentaleAllegato', codiceDocumentaleAllegato);
            outMap.put('integerDocumentId', integerDocumentId);
            //RECUPERO ID DEL DOCUMENTO LOGICA WORKAROUND - START
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Exception during integration call: ' + e.getMessage());
            outMap.put('error','Exception during integration call: ' + e.getMessage());
            throw new AuraHandledException('Error during document upload: ' + e.getMessage());
        }
    }

    private void filterListForArchivio(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        try{
            List<Object> listToFilter = (List<Object>) inputMap.get('listToFilter');
            System.debug('### List to filter: ' + listToFilter);
            List<Object> filteredList = new List<Object>();
            if(listToFilter != null && !listToFilter.isEmpty() && listToFilter[0] != null){
                for(Integer i = 1; i < listToFilter.size(); i++){
                    Object item = listToFilter.get(i);
                    filteredList.add(item);
                }
            }
            System.debug('filteredList: ' + filteredList);
            outMap.put('filteredList', filteredList);
        } catch (Exception e) {
            String errorMsg = 'Exception during document filtering: ' + e.getTypeName() + ' - ' + e.getMessage();
            System.debug(LoggingLevel.ERROR, errorMsg);
            System.debug(LoggingLevel.ERROR, 'Stack Trace: ' + e.getStackTraceString());
            outMap.put('error', errorMsg);
            throw new AuraHandledException(errorMsg);
        }
    }

    private void setDocumentCountByCompany(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        List<Object> documentList = inputMap.containsKey('documentList') ? (List<Object>) inputMap.get('documentList') : new List<Map<String, String>>();
        Decimal limitRow = inputMap.containsKey('limitRow') ? Integer.ValueOf((Decimal) inputMap.get('limitRow')) : null;
        System.debug('%%RF documentList: ' + documentList);
        System.debug('%%RF limitRow: ' + limitRow);
        List<Map<String, Object>> documentListTyped = new List<Map<String, Object>>();
        if (documentList.isEmpty() || documentList[0] == null || documentList[0].toString() == '' || documentList.size() == 0) {
            outMap.put('documentList', documentListTyped);
            return;
        }
        for (Integer i = 0; i < documentList.size(); i++) {
            documentListTyped.add((Map<String, Object>) documentList[i]);
        }
        Set<String> companySet = new Set<String>();
        Map<String, Integer> companyCountMap = new Map<String, Integer>();
        try{
            if (!documentList.isEmpty()) {
                for (Map<String,Object> document : documentListTyped) {
                    companySet.add((String) document.get('codiceCompagnia'));
                }
                for (String company : companySet) {
                    Integer count = 0;
                    for (Map<String,Object> document : documentListTyped) {
                        String comp = (String) document.get('codiceCompagnia');
                        if (comp.equalsIgnoreCase(company)) {
                            count++;
                        }
                    }
                    companyCountMap.put(company, count);
                }
                for (Map<String,Object> document : documentListTyped) {
                    String company = (String) document.get('codiceCompagnia');
                    if (companyCountMap.containsKey(company)) {
                        document.put('documentCount', companyCountMap.get(company));
                    } else {
                        document.put('documentCount', 0);
                    }
                    String statoEsteso = [SELECT Name FROM Unita_Territoriali__c WHERE RecordType.Name = 'Nazione' AND Codice_belfiore__c =: ((String) document.get('stato')) LIMIT 1].Name;
                    document.put('statoEsteso', statoEsteso != null ? statoEsteso : null);
                    String comuneEsteso = [SELECT Name FROM Unita_Territoriali__c WHERE RecordType.Name = 'Comune' AND Codice_catasto__c =: ((String) document.get('comuneEmissione')) LIMIT 1].Name;
                    document.put('comuneEsteso', comuneEsteso != null ? comuneEsteso : null);
                    String companyNew = (company == 'SOC_1') ? 'Unipol' : (company == 'SOC_4') ? 'Unisalute' : company  ;
                    document.put('companyLabel', companyNew);
                }
            }
            if (limitRow == null) {
            outMap.put('documentList', documentListTyped);
            } else {
                List<Map<String, Object>> limitedList = new List<Map<String, Object>>();
                for (Integer i = 0; i < documentListTyped.size() && i < limitRow; i++) {
                    limitedList.add(documentListTyped[i]);
                }
                outMap.put('documentList', limitedList);
            }
            
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'Exception during document count retrieval: ' + e.getMessage());
            outMap.put('error','Exception during document count retrieval: ' + e.getMessage());
            throw new AuraHandledException('Error during document count retrieval: ' + e.getMessage());
        }
    }

    private void checkAttachmentSize(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        Integer maxSize = 5242880; // 5 MB
        Integer size = 0; 
        List<Object> files = (List<Object>) inputMap.get('CaricaDoc');
        for (Object fileObj : files) {
            System.debug(fileObj.toString());
            String serializedFileObj = JSON.Serialize(fileObj);
            System.debug('serializedFileObj: ' + serializedFileObj);
            Map<String, Object> file = (Map<String, Object>) JSON.deserializeUntyped(serializedFileObj);
            System.debug('File size: ' + String.valueOf(file.get('size')));
            size += (Integer) file.get('size');
        }
        if (size > maxSize) {
            System.debug(LoggingLevel.ERROR, 'Total file size exceeds the maximum limit of 5 MB.');
            outMap.put('error', 'Le dimensioni totali dei file superano i 5 MB.');
            throw new AuraHandledException('Total file size exceeds the maximum limit of 5 MB.');
        } else {
            System.debug('Total file size is within the limit: ' + size + ' bytes.');
            outMap.put('message', 'Le dimensioni totali dei file sono entro il limite di 5 MB.');
        }
    }

    private void mergeListForVisualizzaStorico(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        try {
            List<Map<String, Object>> documentList = inputMap.containsKey('documentList') && inputMap.get('documentList') != null ? (List<Map<String, Object>>) inputMap.get('documentList') : new List<Map<String, Object>>();
            List<Map<String, Object>> accountDetailsList = inputMap.containsKey('accountDetailsList') && inputMap.get('accountDetailsList') != null ? (List<Map<String, Object>>) inputMap.get('accountDetailsList') : new List<Map<String, Object>>();
            Map<Integer, String> ciuWithAccountDetailsIdMap = new Map<Integer, String>();
            for (Map<String, Object> accountDetails : accountDetailsList) {
                Object ciu = accountDetails.get('ciu');
                Object accountDetailsId = accountDetails.get('accountDetailsId');
                if (ciu != null && accountDetailsId != null) {
                    ciuWithAccountDetailsIdMap.put(Integer.valueOf(ciu), (String) accountDetailsId);
                }
            }
            List<Object> results = new List<Object>();
            Integer totalCount = 0;
            for (Map<String, Object> document : documentList) {
                if (document.containsKey('totalCount') && document.get('totalCount') != null) {
                    totalCount += (Integer) document.get('totalCount');
                }
                List<Object> documentResults = (List<Object>) document.get('results');
                if (documentResults != null) {
                    for (Object o : documentResults) {
                        Map<String, Object> documentResult = (Map<String, Object>) o;
                        if (documentResult.containsKey('datiStoricizzati') && documentResult.get('datiStoricizzati') != null) {
                            Map<String, Object> datiStoricizzati = (Map<String, Object>) documentResult.get('datiStoricizzati');
                            Object ciuObj = datiStoricizzati.get('ciu');
                            if (ciuObj != null) {
                                Integer ciu = (Integer) ciuObj;
                                if (ciuWithAccountDetailsIdMap.containsKey(ciu)) {
                                    documentResult.put('accountDetailsId', ciuWithAccountDetailsIdMap.get(ciu));
                                }
                            }
                        }
                    }
                    results.addAll(documentResults);
                }
            }
            outMap.put('results', results);
            outMap.put('totalCount', totalCount);
            System.debug(LoggingLevel.INFO, 'Merged ' + results.size() + ' results with totalCount ' + totalCount);
        } catch (Exception e) {
            System.debug(LoggingLevel.ERROR, 'mergeListForVisualizzaStorico failed: ' + e);
            outMap.put('error', e.getMessage());
            throw new AuraHandledException('Error during list merging: ' + e.getMessage());
        }
    }

    private void parseResponseJson(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        String jsonResponse = (String) inputMap.get('response');
        if (String.isNotBlank(jsonResponse)) {
            try {
                Map<String, Object> responseMap = (Map<String, Object>) JSON.deserializeUntyped(jsonResponse);
                JSONParser parser = JSON.createParser(jsonResponse);
                System.JSONToken currentToken;
                String currentValue;
                String messaggio;
                String codice;
                Boolean foundInformazioniAggiuntive = false;
                while (parser.nextToken() != null) {
                    currentToken = parser.getCurrentToken();
                    currentValue = (String)parser.readValueAs(String.class);
                    System.debug('Current token: ' + currentToken);
                    if (currentValue == 'informazioniAggiuntive') foundInformazioniAggiuntive = true;
                    if (currentToken == System.JSONToken.FIELD_NAME && currentValue == 'descrizione') {
                        parser.nextToken();
                        messaggio = (String)parser.readValueAs(String.class);
                        if (String.isNotBlank(codice)) break;
                    } else if (currentToken == System.JSONToken.FIELD_NAME && currentValue == 'codice') {
                        parser.nextToken();
                        codice = (String)parser.readValueAs(String.class);
                        if (String.isNotBlank(messaggio)) break;
                    }
                }
                System.debug('codice: ' + codice);
                System.debug('messaggio: ' + messaggio);
                Boolean isAntiLaundering = false;
                if (codice == 'INSERT_ANTILAUDERING_CHECK_KO') {
                    isAntiLaundering = true;
                }
                outMap.put('isAntiLaundering', isAntiLaundering);
            } catch (Exception e) {
                System.debug(LoggingLevel.ERROR, 'Error parsing JSON response: ' + e.getMessage());
                outMap.put('error', 'Error parsing JSON response: ' + e.getMessage());
                throw new AuraHandledException('Error parsing JSON response: ' + e.getMessage());
            }
        } else {
            System.debug(LoggingLevel.WARN, 'No response to parse.');
        }
    }

    private void populatePicklistCompany2(Map<String, Object> inputMap, Map<String, Object> outMap, Map<String, Object> options) {
        System.debug('%%inputMap: ' + inputMap);
        String acn = (String) inputMap.get('accountId');
        List<Map<String, Object>> optionList = new List<Map<String, Object>>();
        Set<String> aarSocieties = new Set<String>();
        Set<String> addedValues = new Set<String>();
        List<FinServ__AccountAccountRelation__c> aars = [SELECT Id, RecordType.Name, FinServ__RelatedAccount__r.ExternalId__c, FinServ__RelatedAccount__r.name FROM FinServ__AccountAccountRelation__c  WHERE FinServ__Account__c = :acn and RecordType.Name = 'AccountSociety' ];
        
        for (FinServ__AccountAccountRelation__c aar : aars) {
            aarSocieties.add(aar.FinServ__RelatedAccount__r.ExternalId__c);
        }
        
        Map<String, String> allowedMandates = new Map<String, String>{
            'MandatoUnipolSai' => 'SOC_1',
            'MandatoUniSalute' => 'SOC_4',
            'MandatoUnipolRental' => 'UnipolRental',
            'MandatoUnipolTech' => 'UnipolTech'
        };

        Map<String, String> societyLabel = new Map<String, String>{
            'SOC_1' => 'Unipol',
            'SOC_4' => 'UniSalute'
            //'UnipolRental' => 'UnipolRental'
            //'UnipolTech' => 'UnipolTech'
        };

        List<User> user = new List<User>();
        if (Schema.sObjectType.User.isAccessible()) {
            user = [SELECT Id, IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId() LIMIT 1];
        }
        if (user.isEmpty() || user[0].IdAzienda__c == null) {
            outMap.put('options', optionList);
            return;
        }

        Set<String> userPermNames = new Set<String>();
        if (Schema.sObjectType.PermissionSetAssignment.isAccessible()) {
            for (PermissionSetAssignment psa : [
                SELECT PermissionSet.Name FROM PermissionSetAssignment
                WHERE AssigneeId = :UserInfo.getUserId() AND PermissionSet.Name IN :allowedMandates.keySet()
            ]) {
                userPermNames.add(psa.PermissionSet.Name);
            }
        }

        /*if (userPermNames.contains('MandatoUnipolRental')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolRental', 'value' => 'UnipolRental'});
            addedValues.add('UnipolRental');
        }

        if (userPermNames.contains('MandatoUnipolTech')) {
            optionList.add(new Map<String, Object>{'name' => 'UnipolTech', 'value' => 'UnipolTech'});
            addedValues.add('UnipolTech');
        }*/

        List<FinServ__AccountAccountRelation__c> accRelation = new List<FinServ__AccountAccountRelation__c>();
        if (Schema.sObjectType.FinServ__AccountAccountRelation__c.isAccessible()) {
            accRelation = [
                SELECT FinServ__RelatedAccount__r.Name, FinServ__RelatedAccount__r.ExternalId__c
                FROM FinServ__AccountAccountRelation__c
                WHERE FinServ__Account__c = :user[0].IdAzienda__c
                AND RecordType.DeveloperName = 'AgencySociety'
            ];
        }
        for (FinServ__AccountAccountRelation__c acc : accRelation) {
            String extId = acc.FinServ__RelatedAccount__r.ExternalId__c;
            String label = acc.FinServ__RelatedAccount__r.Name;
            for (String perm : userPermNames) {
                if (allowedMandates.get(perm) == extId && !addedValues.contains(label) && aarSocieties.contains(extId)) {
                    optionList.add(new Map<String, Object>{'value' => extId, 'label' => societyLabel.get(extId)});
                    addedValues.add(label);
                }
            }
        }
        system.debug('%%optionList: ' + optionList);
        outMap.put('options', optionList);
    }
}