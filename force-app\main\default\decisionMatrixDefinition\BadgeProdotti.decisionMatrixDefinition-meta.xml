<?xml version="1.0" encoding="UTF-8"?>
<DecisionMatrixDefinition xmlns="http://soap.sforce.com/2006/04/metadata">
    <label>BadgeProdotti</label>
    <processType>Bre</processType>
    <type>Standard</type>
    <versions>
        <fullName>BadgeProdotti_V1</fullName>
        <columns>
            <columnType>Input</columnType>
            <dataType>Text</dataType>
            <displaySequence>1</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>RecordType</name>
        </columns>
        <columns>
            <columnType>Output</columnType>
            <dataType>Text</dataType>
            <displaySequence>2</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>TextDescription</name>
        </columns>
        <columns>
            <columnType>Output</columnType>
            <dataType>Text</dataType>
            <displaySequence>3</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>ResourceName</name>
        </columns>
        <decisionMatrixDefinition>BadgeProdotti</decisionMatrixDefinition>
        <label>BadgeProdotti V1</label>
        <rank>1</rank>
        <startDate>2025-09-02T08:22:51.000Z</startDate>
        <status>Draft</status>
        <versionNumber>1</versionNumber>
    </versions>
    <versions>
        <fullName>BadgeProdotti_V2</fullName>
        <columns>
            <columnType>Input</columnType>
            <dataType>Text</dataType>
            <displaySequence>1</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>RecordType</name>
        </columns>
        <columns>
            <columnType>Output</columnType>
            <dataType>Text</dataType>
            <displaySequence>2</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>TextDescription</name>
        </columns>
        <columns>
            <columnType>Output</columnType>
            <dataType>Text</dataType>
            <displaySequence>3</displaySequence>
            <isWildcardColumn>false</isWildcardColumn>
            <name>ResourceName</name>
        </columns>
        <decisionMatrixDefinition>BadgeProdotti</decisionMatrixDefinition>
        <label>BadgeProdotti V2</label>
        <startDate>2025-09-02T12:15:31.000Z</startDate>
        <status>Active</status>
        <versionNumber>2</versionNumber>
    </versions>
</DecisionMatrixDefinition>
