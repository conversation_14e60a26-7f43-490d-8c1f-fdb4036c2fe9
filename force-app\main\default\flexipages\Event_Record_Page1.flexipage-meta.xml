<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>actionNames</name>
                    <valueList>
                        <valueListItems>
                            <value>Event.Prendi_in_Carico_Event</value>
                            <visibilityRule>
                                <booleanFilter>1 AND 2 AND 3</booleanFilter>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Da_prendere_in_carico</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsOwnedByRunningUser__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsChild}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>false</rightValue>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Event.Edit_Event</value>
                            <visibilityRule>
                                <booleanFilter>((1 OR 2) AND 3) AND ((4 AND 5) OR NOT(5))</booleanFilter>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.UnipolResponsabileUserPS}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsOwnedByRunningUser__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsChild}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>false</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Preso_in_carico</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.CollaborativeEvent__c}</leftValue>
                                    <operator>NE</operator>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>Event.Delete_Event_ServiceAppointment</value>
                            <visibilityRule>
                                <booleanFilter>((1 OR 2) AND 3) AND ((4 AND 5) OR NOT(5))</booleanFilter>
                                <criteria>
                                    <leftValue>{!$Permission.CustomPermission.UnipolResponsabileUserPS}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsOwnedByRunningUser__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>true</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.IsChild}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>false</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.Status__c}</leftValue>
                                    <operator>EQUAL</operator>
                                    <rightValue>Preso_in_carico</rightValue>
                                </criteria>
                                <criteria>
                                    <leftValue>{!Record.CollaborativeEvent__c}</leftValue>
                                    <operator>NE</operator>
                                </criteria>
                            </visibilityRule>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.TextPost</value>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.ContentPost</value>
                        </valueListItems>
                        <valueListItems>
                            <value>FeedItem.PollPost</value>
                        </valueListItems>
                    </valueList>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>collapsed</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsConfiguration</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>enableActionsInNative</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideChatterActions</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>numVisibleActions</name>
                    <value>3</value>
                </componentInstanceProperties>
                <componentName>force:highlightsPanel</componentName>
                <identifier>force_highlightsPanel</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>header</name>
        <type>Region</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentName>force:detailPanel</componentName>
                <identifier>force_detailPanel</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>detailTabContent</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>active</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>detailTabContent</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>title</name>
                    <value>Standard.Tab.detail</value>
                </componentInstanceProperties>
                <componentName>flexipage:tab</componentName>
                <identifier>detailTab</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>tabs</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>label</name>
                    <value>Tabs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>tabs</name>
                    <value>tabs</value>
                </componentInstanceProperties>
                <componentName>flexipage:tabset</componentName>
                <identifier>flexipage_tabset</identifier>
            </componentInstance>
        </itemInstances>
        <mode>Replace</mode>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>Event Org Record Page</masterLabel>
    <parentFlexiPage>sfa__Event_rec_L</parentFlexiPage>
    <sobjectType>Event</sobjectType>
    <template>
        <name>flexipage:recordHomeSingleColTemplateDesktop</name>
    </template>
    <type>RecordPage</type>
</FlexiPage>
