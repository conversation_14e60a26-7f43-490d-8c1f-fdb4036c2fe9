<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assignEsitoESottoesito</name>
        <label>assignEsitoESottoesito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_Activity.Esito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EsitoPicklist</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_Activity.Sottoesito__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>SottoesitoPicklist</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_Activity</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>update_Activity</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>Acquisto_preventivo_altra_Compagnia</name>
        <choiceText>Acquisto/preventivo altra Compagnia</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Acquisto/preventivo altra Compagnia</stringValue>
        </value>
    </choices>
    <choices>
        <name>Altro</name>
        <choiceText>Altro</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro</stringValue>
        </value>
    </choices>
    <choices>
        <name>AltroVantaggio</name>
        <choiceText>Altro Vantaggio</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro Vantaggio</stringValue>
        </value>
    </choices>
    <choices>
        <name>Bene_non_assicurabile</name>
        <choiceText>Bene non assicurabile</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Bene non assicurabile</stringValue>
        </value>
    </choices>
    <choices>
        <name>Cliente_non_interessato_ad_assicurarsi_UnipolSai</name>
        <choiceText>Cliente non interessato ad assicurarsi UnipolSai</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Cliente non interessato ad assicurarsi UnipolSai</stringValue>
        </value>
    </choices>
    <choices>
        <name>Concorrenza</name>
        <choiceText>Concorrenza</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Concorrenza</stringValue>
        </value>
    </choices>
    <choices>
        <name>Optout</name>
        <choiceText>Optout</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Optout</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prezzo</name>
        <choiceText>Prezzo</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prezzo</stringValue>
        </value>
    </choices>
    <choices>
        <name>Problematiche_sinistri</name>
        <choiceText>Problematiche sinistri</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Problematiche sinistri</stringValue>
        </value>
    </choices>
    <choices>
        <name>Prodotto</name>
        <choiceText>Prodotto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Prodotto</stringValue>
        </value>
    </choices>
    <choices>
        <name>Revoca_Consensi_CC</name>
        <choiceText>Revoca Consensi CC</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Revoca Consensi CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>Richiesta_no_contatto_CC</name>
        <choiceText>Richiesta no contatto CC</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Richiesta no contatto CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>UnsubscribeCC</name>
        <choiceText>Unsubscribe CC</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Unsubscribe CC</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueAppuntamentoAgenzia</name>
        <choiceText>&lt;h5&gt;Appuntamento Agenzia&lt;/h5&gt;</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Appuntamento Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueDaRichiamareAgenzia</name>
        <choiceText>Da richiamare Agenzia</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Da richiamare Agenzia</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueInteresseGenerico</name>
        <choiceText>Interesse generico</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Interesse generico</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueNonInteressato</name>
        <choiceText>Non interessato</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Non interessato</stringValue>
        </value>
    </choices>
    <choices>
        <name>valueVendita</name>
        <choiceText>Vendita</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Vendita</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>checkSceltaButton</name>
        <label>checkSceltaButton</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>IsConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>footerButton.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>assignEsitoESottoesito</targetReference>
            </connector>
            <label>IsConferma</label>
        </rules>
    </decisions>
    <description>Flow che attraverso un action cambia l&apos;esito ed il sotto esito del case</description>
    <dynamicChoiceSets>
        <name>valueEsito</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Esito__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <dynamicChoiceSets>
        <name>valueSottoEsito</name>
        <dataType>Picklist</dataType>
        <displayField xsi:nil="true"/>
        <object xsi:nil="true"/>
        <picklistField>Sottoesito__c</picklistField>
        <picklistObject>Case</picklistObject>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <interviewLabel>CC-ChangeEsitoFinale {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-ChangeEsitoFinale</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_Activity</name>
        <label>get Activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Esito_Finale</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_Activity</name>
        <label>update Activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputReference>caseToUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>Esito_Finale</name>
        <label>Esito Finale</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkSceltaButton</targetReference>
        </connector>
        <fields>
            <name>titleChangeEsito</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Conferma gli esiti per questa attività &lt;a href=&quot;/lightning/r/Case/{!get_Activity.Id}/view&quot; rel=&quot;noopener noreferrer&quot; target=&quot;_blank&quot;&gt;{!get_Activity.CaseNumber}&lt;/a&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>dependencyPicklistEsito</name>
            <extensionName>flowruntime:dependentPicklists</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>dependencyWrapperApiName</name>
                <value>
                    <stringValue>case</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topPicklistApiName</name>
                <value>
                    <stringValue>Esito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middlePicklistApiName</name>
                <value>
                    <stringValue>Sottoesito__c</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topLabel</name>
                <value>
                    <stringValue>Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleLabel</name>
                <value>
                    <stringValue>Sotto Esito</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleRequired</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>topValue</name>
                <value>
                    <elementReference>get_Activity.Esito__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>middleValue</name>
                <value>
                    <elementReference>get_Activity.Sottoesito__c</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>disabled</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>SottoesitoPicklist</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <elementReference>recordId</elementReference>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>EsitoPicklist</name>
            <choiceReferences>valueInteresseGenerico</choiceReferences>
            <choiceReferences>valueAppuntamentoAgenzia</choiceReferences>
            <choiceReferences>valueDaRichiamareAgenzia</choiceReferences>
            <choiceReferences>valueVendita</choiceReferences>
            <choiceReferences>valueNonInteressato</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Esito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>SottoesitoPicklist</name>
            <choiceReferences>Optout</choiceReferences>
            <choiceReferences>Prezzo</choiceReferences>
            <choiceReferences>Prodotto</choiceReferences>
            <choiceReferences>AltroVantaggio</choiceReferences>
            <choiceReferences>Concorrenza</choiceReferences>
            <choiceReferences>UnsubscribeCC</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Sottoesito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>EsitoPicklist</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <stringValue>Non interessato</stringValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>footerButton</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>get_Activity</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>caseToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>collectionvalueEsito</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
