<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>UpdateVoiceCall</name>
        <label>UpdateVoiceCall</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>VoiceCallUpdater</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>update_case</targetReference>
        </connector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>CustomFieldApiName</name>
            <value>
                <stringValue>intermediate_outcome__c</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>CustomFieldValue</name>
            <value>
                <elementReference>voiceCallToUpdate.Intermediate_Outcome__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>voiceCallId</name>
            <value>
                <elementReference>voiceCallToUpdate.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>VoiceCallUpdater</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>assignEsitoIntermedio_Tentato_contatto_VC</name>
        <label>assignEsitoIntermedio Tentato contatto VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_activity_VC.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity_VC.UnansweredAttempts__c</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_voiceCall_flow_VC.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>voiceCallToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_voiceCall_flow_VC</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity_VC.ActualCalls__c</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_activity_VC</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkVoiceCall</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignEsitoIntermedioC</name>
        <label>assignEsitoIntermedio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_current_activity.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_current_activity.UnansweredAttempts__c</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_current_activity.ActualCalls__c</assignToReference>
            <operator>Add</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_current_activity</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkVoiceCall</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignValueCaseUpdate</name>
        <label>assignValueCaseUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_current_activity.AppointmentDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Data_Appuntamento</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_current_activity.Activity_Notes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Note</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_current_activity.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_current_activity.AppointmentTime__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>convertOraTextIntoTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_current_activity</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkVoiceCall</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignValueCaseUpdateVC</name>
        <label>assignValueCaseUpdate VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>get_activity_VC.AppointmentDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Data_AppuntamentoVc</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity_VC.Activity_Notes__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NoteVC</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity_VC.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_voiceCall_flow_VC.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>picklistEsitoIntermedio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>voiceCallToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_voiceCall_flow_VC</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>get_activity_VC.AppointmentTime__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>convertOraTextIntoTimeVC</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>get_activity_VC</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkVoiceCall</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>voiceCallSelectedToUpdate</name>
        <label>voiceCallSelectedToUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>dataTableVoiceCall.firstSelectedRow.Intermediate_Outcome__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Ripianificata</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>voiceCallToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>dataTableVoiceCall.firstSelectedRow</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>screenPianificazione</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>esitoIntermedioRipianificata</name>
        <choiceText>Ripianificata</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Ripianificata</stringValue>
        </value>
    </choices>
    <choices>
        <name>esitoIntermedioTentatoContatto</name>
        <choiceText>Tentato Contatto</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Tentato Contatto</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>checkButtonClicked</name>
        <label>checkButtonClicked</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isConferma</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>buttonConfermaAnnulla.renderNext</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>checkRecord</targetReference>
            </connector>
            <label>isConferma</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkChoice</name>
        <label>checkChoice</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assignEsitoIntermedioC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isRipianificata</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>picklistEsitoIntermedio</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ripianificata</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>existsVoiceCall</targetReference>
            </connector>
            <label>isRipianificata</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkChoice_VC</name>
        <label>checkChoice VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assignEsitoIntermedio_Tentato_contatto_VC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isRipianificata_VC</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>picklistEsitoIntermedio</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Ripianificata</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>screenPianificazioneVc</targetReference>
            </connector>
            <label>isRipianificata VC</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkRecord</name>
        <label>checkRecord</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>get_voiceCall_flow_VC</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isCase</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>typeObject</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>500</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>get_current_activity</targetReference>
            </connector>
            <label>isCase</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkVoiceCall</name>
        <label>checkVoiceCall</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>update_case</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>voiceCallIsNonEmpty</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>voiceCallToUpdate</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>voiceCallToUpdate</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>UpdateVoiceCall</targetReference>
            </connector>
            <label>voiceCallIsNonEmpty</label>
        </rules>
    </decisions>
    <decisions>
        <name>existsVoiceCall</name>
        <label>existsVoiceCall</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>screenPianificazione</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>get_voice_call</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>screen_select_voice_call</targetReference>
            </connector>
            <label>yes</label>
        </rules>
    </decisions>
    <description>Flow che attraverso un action cambia l&apos;esito intermedio del case (Versione con VC collegata al case attraverso il campo lookup RelatedCase__c)</description>
    <environments>Default</environments>
    <formulas>
        <name>convertOraTextIntoTime</name>
        <dataType>Time</dataType>
        <expression>IF(
  ISBLANK({!Ora_Appuntamento}),
  NULL,
  TIMEVALUE({!Ora_Appuntamento} &amp; &quot;:00.000&quot;)
)</expression>
    </formulas>
    <formulas>
        <name>convertOraTextIntoTimeVC</name>
        <dataType>Time</dataType>
        <expression>IF(
  ISBLANK({!Ora_AppuntamentoVc}),
  NULL,
 TIMEVALUE({!Ora_AppuntamentoVc} &amp; &quot;:00.000&quot;)
)</expression>
    </formulas>
    <formulas>
        <name>typeObject</name>
        <dataType>String</dataType>
        <expression>LEFT({!recordId}, 3)</expression>
    </formulas>
    <interviewLabel>CC-ChangeEsitoIntermedio {!$Flow.CurrentDateTime}</interviewLabel>
    <label>CC-ChangeEsitoIntermedio</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordLookups>
        <name>get_activity_VC</name>
        <label>get activity VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>checkChoice_VC</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>get_voiceCall_flow_VC.RelatedCase__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_current_activity</name>
        <label>get current activity</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_voice_call</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Case</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_voice_call</name>
        <label>get voice call</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>checkChoice</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>RelatedCase__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>get_voiceCall_flow_VC</name>
        <label>get voiceCall flow VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>get_activity_VC</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>VoiceCall</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordUpdates>
        <name>update_case</name>
        <label>update case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>screenSuccessHead</targetReference>
        </connector>
        <inputReference>caseToUpdate</inputReference>
    </recordUpdates>
    <screens>
        <name>screen_select_voice_call</name>
        <label>screen select voice call</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>voiceCallSelectedToUpdate</targetReference>
        </connector>
        <fields>
            <name>selectVoiceCallText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Seleziona la Voice Call da aggiornare&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>dataTableVoiceCall</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>VoiceCall</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Voice Call</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>0.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>get_voice_call</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-a1a3&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;CallStartDateTime&quot;,&quot;guid&quot;:&quot;column-2d88&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Call Started&quot;,&quot;type&quot;:&quot;customDateTime&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>screenChangeEsito</name>
        <label>screenChangeEsito</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>checkButtonClicked</targetReference>
        </connector>
        <fields>
            <name>titleChangeEsito</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 12px;&quot;&gt;Seleziona Esito Intermedio&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>picklistEsitoIntermedio</name>
            <choiceReferences>esitoIntermedioTentatoContatto</choiceReferences>
            <choiceReferences>esitoIntermedioRipianificata</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Esito</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>buttonConfermaAnnulla</name>
            <extensionName>runtime_industries_lending:flowFooter</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>buttonLabelCustom</name>
                <value>
                    <stringValue>Annulla</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>buttonLabelNext</name>
                <value>
                    <stringValue>Conferma</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>showPrevious</name>
                <value>
                    <booleanValue>false</booleanValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>false</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <screens>
        <name>screenPianificazione</name>
        <label>screenPianificazione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>assignValueCaseUpdate</targetReference>
        </connector>
        <fields>
            <name>assegnaDataOraAppuntamento</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Assegna una nuova Data e Ora per l&apos;appuntamento&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Data_Appuntamento</name>
            <dataType>Date</dataType>
            <fieldText>Data Appuntamento</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Ora_Appuntamento</name>
            <dataType>String</dataType>
            <fieldText>Ora Appuntamento</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Inserisci un orario valido nel formato HH:mm (es. 14:30)&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Ora_Appuntamento}, &quot;^([01][0-9]|2[0-3]):[0-5][0-9]$&quot;)</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>Note</name>
            <fieldText>Note</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>screenPianificazioneVc</name>
        <label>screenPianificazione VC</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <backButtonLabel>Annulla</backButtonLabel>
        <connector>
            <targetReference>assignValueCaseUpdateVC</targetReference>
        </connector>
        <fields>
            <name>Copy_1_of_assegnaDataOraAppuntamento</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;strong style=&quot;font-size: 14px;&quot;&gt;Assegna una nuova Data e Ora per l&apos;appuntamento&lt;/strong&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <fields>
            <name>Data_AppuntamentoVc</name>
            <dataType>Date</dataType>
            <fieldText>Data Appuntamento</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <fields>
            <name>Ora_AppuntamentoVc</name>
            <dataType>String</dataType>
            <fieldText>Ora Appuntamento</fieldText>
            <fieldType>InputField</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <validationRule>
                <errorMessage>&lt;p&gt;Inserisci un orario valido nel formato HH:mm (es. 14:30)&lt;/p&gt;</errorMessage>
                <formulaExpression>REGEX({!Ora_AppuntamentoVc}, &quot;^([01][0-9]|2[0-3]):[0-5][0-9]$&quot;)</formulaExpression>
            </validationRule>
        </fields>
        <fields>
            <name>NoteVC</name>
            <fieldText>Note</fieldText>
            <fieldType>LargeTextArea</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>false</isRequired>
        </fields>
        <nextOrFinishButtonLabel>Conferma</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>screenSuccessHead</name>
        <label>screenSuccess</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>MessaggeSuccess</name>
            <extensionName>c:cC_OpenToastEventIntoFlow</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>toastMessage</name>
                <value>
                    <stringValue>L&apos;attività &quot;{!caseToUpdate.CaseNumber}&quot; è stata esitata</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>toastVariant</name>
                <value>
                    <stringValue>success</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <nextOrFinishButtonLabel>Chiudi</nextOrFinishButtonLabel>
        <showFooter>true</showFooter>
        <showHeader>false</showHeader>
    </screens>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>screenChangeEsito</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>caseToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>voiceCallToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>VoiceCall</objectType>
    </variables>
</Flow>
