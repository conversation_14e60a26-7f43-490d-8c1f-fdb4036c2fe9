<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>62.0</apiVersion>
    <assignments>
        <name>Add_Container_for_Update</name>
        <label>Add Container for Update</label>
        <locationX>1882</locationX>
        <locationY>4562</locationY>
        <assignmentItems>
            <assignToReference>OpportunityNeedUpdateOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Assignment_Event_Creazione_Trattativa</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Product_To_List_Update</name>
        <label>Add Product To List Update</label>
        <locationX>2137</locationX>
        <locationY>5486</locationY>
        <assignmentItems>
            <assignToReference>ProductNeededUpdateOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assignVariable</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>add_value_to_picklist</name>
        <label>add value to picklist</label>
        <locationX>1474</locationX>
        <locationY>3578</locationY>
        <assignmentItems>
            <assignToReference>areaOfNeedPolizzaDiretta</assignToReference>
            <operator>Add</operator>
            <value>
                <stringValue>{!loop_over_areas};</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loop_over_areas</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Ass_Update_Parent_Opp</name>
        <label>Ass Update Parent Opp</label>
        <locationX>50</locationX>
        <locationY>3206</locationY>
        <assignmentItems>
            <assignToReference>ProductNeededUpdateOutput</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Id</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OppAmount</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assignVariable</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Agency</name>
        <label>Assign Agency</label>
        <locationX>1247</locationX>
        <locationY>758</locationY>
        <assignmentItems>
            <assignToReference>AgencyInput</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Agency</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Service_Territory</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_CIP_to_Container</name>
        <label>Assign CIP to Container</label>
        <locationX>50</locationX>
        <locationY>1706</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerUpdateNeeded</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_If_Product_Container_Rating_Should_Be_Updated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Container</name>
        <label>Assign Container</label>
        <locationX>1590</locationX>
        <locationY>2588</locationY>
        <assignmentItems>
            <assignToReference>emptyContainer</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Container_List</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Container_List</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Check_if_Empty_Container_Exist</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Container_Found</name>
        <label>Assign Container Found</label>
        <locationX>2838</locationX>
        <locationY>1598</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Open_Container</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_If_Rating_Should_Be_Updated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Customer</name>
        <label>Assign Customer</label>
        <locationX>1247</locationX>
        <locationY>350</locationY>
        <assignmentItems>
            <assignToReference>CustomerInput</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Customer</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>is_Valid_Agency</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Has_a_Child</name>
        <label>Assign Has a Child</label>
        <locationX>1810</locationX>
        <locationY>2138</locationY>
        <assignmentItems>
            <assignToReference>hasAChild</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Loop_Container_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Name_to_New_Container</name>
        <label>Assign Name to New Container</label>
        <locationX>1882</locationX>
        <locationY>4454</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Container_After_Insert</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Container_for_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Name_to_Product</name>
        <label>Assign Name to Product</label>
        <locationX>2137</locationX>
        <locationY>5378</locationY>
        <assignmentItems>
            <assignToReference>productRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product_after_Insert</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Product_To_List_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Container</name>
        <label>Assign Product Container</label>
        <locationX>182</locationX>
        <locationY>1490</locationY>
        <assignmentItems>
            <assignToReference>containerRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product_Container</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>startingContainerNeeds</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product_Container.OverallAreasOfNeed__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Opp_exist_check_channel</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Product_Found</name>
        <label>Assign Product Found</label>
        <locationX>182</locationX>
        <locationY>1274</locationY>
        <assignmentItems>
            <assignToReference>productRecord</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Product</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_Product_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignAgencySelectedByLocatorEmpty</name>
        <label>AssignAgencySelectedByLocatorEmpty</label>
        <locationX>2178</locationX>
        <locationY>3254</locationY>
        <assignmentItems>
            <assignToReference>agencySelectedByLocator</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultEmptyLocator</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_New_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignAgencySelectedByLocatorNo</name>
        <label>AssignAgencySelectedByLocatorNo</label>
        <locationX>1914</locationX>
        <locationY>3254</locationY>
        <assignmentItems>
            <assignToReference>agencySelectedByLocator</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>NO</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_New_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignAgencySelectedByLocatorSi</name>
        <label>AssignAgencySelectedByLocatorSi</label>
        <locationX>1650</locationX>
        <locationY>3254</locationY>
        <assignmentItems>
            <assignToReference>agencySelectedByLocator</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>SI</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_New_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assignVariable</name>
        <label>assignVariable</label>
        <locationX>1159</locationX>
        <locationY>5678</locationY>
        <assignmentItems>
            <assignToReference>existingContainer</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>isNullGetOpenConteiner</elementReference>
            </value>
        </assignmentItems>
    </assignments>
    <assignments>
        <name>Clear_Has_a_Child</name>
        <label>Clear Has a Child</label>
        <locationX>1722</locationX>
        <locationY>1814</locationY>
        <assignmentItems>
            <assignToReference>hasAChild</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Product_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_New_Container</name>
        <label>Initialize New Container</label>
        <locationX>1914</locationX>
        <locationY>3446</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Assegnato</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRatingFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Amount</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>intermediaryId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignedGroup__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.channel</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AgencySelectedByLocator__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencySelectedByLocator</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignmentCounter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Salespoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Service_Territory.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountAgencyStatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Container</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_New_Container_Agenziale</name>
        <label>Initialize New Container Agenziale</label>
        <locationX>1342</locationX>
        <locationY>3146</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>stageEmissioneDiretta</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>AgenzialeOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.WorkingSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeSLAExpiryDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRatingFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Amount</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>intermediaryId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignedGroup__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.TakenInChargeDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.channel</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AssignmentCounter__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <numberValue>1.0</numberValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.JourneyStep__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.Salespoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Service_Territory.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.OverallAreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AccountAgencyStatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>is_Polizza_Diretta</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Verificare se Channel__c campo da usare per mappare codice prodotto</description>
        <name>Initialize_Product_Record</name>
        <label>Initialize Product Record</label>
        <locationX>2137</locationX>
        <locationY>4946</locationY>
        <assignmentItems>
            <assignToReference>productRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultRecordName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.StageName</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.StageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Channel__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.channel</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Parent__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.TakenInChargeSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.WorkingSLAStartDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Agency__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRatingFormula</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PR_DomainType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.LeadSource</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.leadSource</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.HasCallMeBack__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.hasCallMeBack</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AssignedTo__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.AssignedTo__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AssignedGroup__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>containerRecord.AssignedGroup__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.AccountAgencyStatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>statoCliente</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>productRecord.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Product</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Sum_amount</name>
        <label>Sum amount</label>
        <locationX>138</locationX>
        <locationY>2930</locationY>
        <assignmentItems>
            <assignToReference>OppAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Quotes.QuoteAmount__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Aggiorna la CloseDate del container (Trattativa)</description>
        <name>Update_Container_CloseDate</name>
        <label>Update Container CloseDate</label>
        <locationX>2706</locationX>
        <locationY>2114</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_Product_Record</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Aggiorna il Rating sulla Trattativa</description>
        <name>Update_Container_Rating</name>
        <label>Update Container Rating</label>
        <locationX>2706</locationX>
        <locationY>1814</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRatingFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_If_CloseDate_Should_Be_Updated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Aggiornamento CloseDate su Product Container</description>
        <name>Update_Product_Container_CloseDate</name>
        <label>Update Product Container CloseDate</label>
        <locationX>50</locationX>
        <locationY>2306</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.CloseDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>defaultCloseDate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <description>Update Product Container Rating</description>
        <name>Update_Product_Container_Rating</name>
        <label>Update Product Container Rating</label>
        <locationX>50</locationX>
        <locationY>2006</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>productRatingFormula</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_If_Container_CloseDate_Should_Be_Updated</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_Quote_Status</name>
        <label>Update Quote Status</label>
        <locationX>402</locationX>
        <locationY>2930</locationY>
        <assignmentItems>
            <assignToReference>Loop_Quotes.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue></stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Quotes</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Update_to_Polizza_Diretta</name>
        <label>Update to Polizza Diretta</label>
        <locationX>1210</locationX>
        <locationY>3854</locationY>
        <assignmentItems>
            <assignToReference>containerRecord.Amount</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>input.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.ClosureSubstatus__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Polizza emessa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>containerRecord.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>areaOfNeedPolizzaDiretta</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Container</targetReference>
        </connector>
    </assignments>
    <collectionProcessors>
        <name>Container_List</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Container List</label>
        <locationX>1282</locationX>
        <locationY>1490</locationY>
        <assignNextValueToReference>currentItem_Container_List</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_All_Opp_MD</collectionReference>
        <conditionLogic>or</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Container_List.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </rightValue>
        </conditions>
        <conditions>
            <leftValueReference>currentItem_Container_List.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>AgenzialeOppRT.Id</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Product_List</targetReference>
        </connector>
    </collectionProcessors>
    <collectionProcessors>
        <name>Product_List</name>
        <elementSubtype>FilterCollectionProcessor</elementSubtype>
        <label>Product List</label>
        <locationX>1282</locationX>
        <locationY>1598</locationY>
        <assignNextValueToReference>currentItem_Product_List</assignNextValueToReference>
        <collectionProcessorType>FilterCollectionProcessor</collectionProcessorType>
        <collectionReference>Get_All_Opp_MD</collectionReference>
        <conditionLogic>and</conditionLogic>
        <conditions>
            <leftValueReference>currentItem_Product_List.RecordTypeId</leftValueReference>
            <operator>EqualTo</operator>
            <rightValue>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </rightValue>
        </conditions>
        <connector>
            <targetReference>Loop_Container_List</targetReference>
        </connector>
    </collectionProcessors>
    <constants>
        <name>defaultEmptyLocator</name>
        <dataType>String</dataType>
        <value>
            <stringValue>-</stringValue>
        </value>
    </constants>
    <constants>
        <name>defaultRecordName</name>
        <dataType>String</dataType>
        <value>
            <stringValue>TEMP</stringValue>
        </value>
    </constants>
    <decisions>
        <name>Agency_Selected_By_Locator</name>
        <label>Agency Selected By Locator</label>
        <locationX>1914</locationX>
        <locationY>3146</locationY>
        <defaultConnector>
            <targetReference>AssignAgencySelectedByLocatorEmpty</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default</defaultConnectorLabel>
        <rules>
            <name>Selected_By_Locator_True</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.selectedByLocator</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignAgencySelectedByLocatorSi</targetReference>
            </connector>
            <label>Selected By Locator True</label>
        </rules>
        <rules>
            <name>Selected_By_Locator_False</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>input.selectedByLocator</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignAgencySelectedByLocatorNo</targetReference>
            </connector>
            <label>Selected By Locator False</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_AON</name>
        <label>check AON</label>
        <locationX>1386</locationX>
        <locationY>3470</locationY>
        <defaultConnector>
            <targetReference>add_value_to_picklist</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>go_next</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>areaOfNeedPolizzaDiretta</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>loop_over_areas</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>loop_over_areas</targetReference>
            </connector>
            <label>go next</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_channel</name>
        <label>check channel</label>
        <locationX>1882</locationX>
        <locationY>3038</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Agenziale</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>oppConfig.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agenziale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Initialize_New_Container_Agenziale</targetReference>
            </connector>
            <label>Agenziale</label>
        </rules>
        <rules>
            <name>Omnicanale</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>oppConfig.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Omnicanale</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>oppConfig.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Preventivatore_Previdenza</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Agency_Selected_By_Locator</targetReference>
            </connector>
            <label>Omnicanale/Preventivatore_Previdenza</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_has_a_Container</name>
        <label>Check has a Container</label>
        <locationX>1722</locationX>
        <locationY>2480</locationY>
        <defaultConnector>
            <targetReference>Loop_Container_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>No_Container</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>hasAChild</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Container</targetReference>
            </connector>
            <label>No Container</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_CloseDate_Should_Be_Updated</name>
        <label>Check If CloseDate Should Be Updated</label>
        <locationX>2838</locationX>
        <locationY>2006</locationY>
        <defaultConnector>
            <targetReference>Initialize_Product_Record</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Keep Current Close Date</defaultConnectorLabel>
        <rules>
            <name>Update_Close_Date</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>defaultCloseDate</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>containerRecord.CloseDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container_CloseDate</targetReference>
            </connector>
            <label>Update CloseDate</label>
        </rules>
    </decisions>
    <decisions>
        <description>Se formula defaultCloseDate&gt; Container closeDate si aggiorna la closeDate con quella della formula</description>
        <name>Check_If_Container_CloseDate_Should_Be_Updated</name>
        <label>Check If Container CloseDate Should Be Updated</label>
        <locationX>182</locationX>
        <locationY>2198</locationY>
        <defaultConnector>
            <targetReference>Get_quotes</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Keep CloseDate</defaultConnectorLabel>
        <rules>
            <name>Update_CloseDate</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>defaultCloseDate</leftValueReference>
                <operator>GreaterThan</operator>
                <rightValue>
                    <elementReference>containerRecord.CloseDate</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Product_Container_CloseDate</targetReference>
            </connector>
            <label>Update CloseDate</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Container_Found</name>
        <label>Check if Container Found</label>
        <locationX>2992</locationX>
        <locationY>1490</locationY>
        <defaultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>check_channel</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Container_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Open_Container</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Container_Found</targetReference>
            </connector>
            <label>Container Found</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Empty_Container_Exist</name>
        <label>Check if Empty Container Exist</label>
        <locationX>1282</locationX>
        <locationY>2930</locationY>
        <defaultConnector>
            <targetReference>check_channel</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Use_Existing_Container</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>hasAChild</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>emptyContainer</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Check_If_Rating_Should_Be_Updated</targetReference>
            </connector>
            <label>Use Existing Container</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_has_a_Child</name>
        <label>Check if has a Child</label>
        <locationX>1942</locationX>
        <locationY>2030</locationY>
        <defaultConnector>
            <targetReference>Loop_Product_List</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Has_a_Product</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Container_List.Id</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Product_List.Parent__r.Id</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Has_a_Child</targetReference>
            </connector>
            <label>Has a Product</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_not_sold</name>
        <label>Check if not sold</label>
        <locationX>270</locationX>
        <locationY>2822</locationY>
        <defaultConnector>
            <targetReference>Update_Quote_Status</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_sold</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Quotes.CommercialStatus__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>ACTIVE</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Loop_Quotes.IsStored__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Sum_amount</targetReference>
            </connector>
            <label>Not sold</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_Parent_Opp_not_closed</name>
        <label>Check if Parent Opp not closed</label>
        <locationX>182</locationX>
        <locationY>2606</locationY>
        <defaultConnector>
            <targetReference>assignVariable</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Not_Closed</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Product.StageName</leftValueReference>
                <operator>NotEqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Quotes</targetReference>
            </connector>
            <label>Not Closed</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Product_Container_Rating_Should_Be_Updated</name>
        <label>Check If Product Container Rating Should Be Updated</label>
        <locationX>182</locationX>
        <locationY>1898</locationY>
        <defaultConnector>
            <targetReference>Check_If_Container_CloseDate_Should_Be_Updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Keep Rating</defaultConnectorLabel>
        <rules>
            <name>Update_Rating_Container</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ratingPriorityFormula</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Product_Container_Rating</targetReference>
            </connector>
            <label>Update Rating Container</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_If_Product_Found</name>
        <label>Check If Product Found</label>
        <locationX>1159</locationX>
        <locationY>1166</locationY>
        <defaultConnector>
            <targetReference>Check_Product_Matching_Setting</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Not Found</defaultConnectorLabel>
        <rules>
            <name>Product_Found</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Product</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Product_Found</targetReference>
            </connector>
            <label>Product Found</label>
        </rules>
    </decisions>
    <decisions>
        <description>Verifica se il rating del prodotto è superiore a quello del container(Trattativa)</description>
        <name>Check_If_Rating_Should_Be_Updated</name>
        <label>Check If Rating Should Be Updated</label>
        <locationX>2838</locationX>
        <locationY>1706</locationY>
        <defaultConnector>
            <targetReference>Check_If_CloseDate_Should_Be_Updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Keep Current Rating</defaultConnectorLabel>
        <rules>
            <name>Update_Rating</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ratingPriorityFormula</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Update_Container_Rating</targetReference>
            </connector>
            <label>Update Rating</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Product_Matching_Setting</name>
        <label>Check Product Matching Setting</label>
        <locationX>2137</locationX>
        <locationY>1274</locationY>
        <defaultConnector>
            <targetReference>Get_Open_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Matching Enabled</defaultConnectorLabel>
        <rules>
            <name>Matching_Disabled</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>oppConfig.AutomaticMatching__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_All_Opp_MD</targetReference>
            </connector>
            <label>Matching Disabled</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Polizza_Diretta</name>
        <label>is Polizza Diretta</label>
        <locationX>1342</locationX>
        <locationY>3254</locationY>
        <defaultConnector>
            <targetReference>Create_Container</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes_Polizza_Diretta</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>eventType</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>EMISSIONE_DIRETTA_POLIZZA</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>loop_over_areas</targetReference>
            </connector>
            <label>Yes Polizza Diretta</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Valid_Agency</name>
        <label>is Valid Agency</label>
        <locationX>1159</locationX>
        <locationY>542</locationY>
        <defaultConnector>
            <targetReference>Get_Agency</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_Agency</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>AgencyInput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Service_Territory</targetReference>
            </connector>
            <label>Valid Agency</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_Valid_Customer</name>
        <label>is Valid Customer</label>
        <locationX>1159</locationX>
        <locationY>134</locationY>
        <defaultConnector>
            <targetReference>Get_Customer</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Valid_Customer</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CustomerInput</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>is_Valid_Agency</targetReference>
            </connector>
            <label>Valid Customer</label>
        </rules>
    </decisions>
    <decisions>
        <name>Opp_exist_check_channel</name>
        <label>Opp exist check channel</label>
        <locationX>182</locationX>
        <locationY>1598</locationY>
        <defaultConnector>
            <targetReference>Check_If_Product_Container_Rating_Should_Be_Updated</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Opp_Exist_Agenziale</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>oppConfig.DeveloperName</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Agenziale</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_CIP_to_Container</targetReference>
            </connector>
            <label>Opp Exist Agenziale</label>
        </rules>
    </decisions>
    <description>vers prec 23
correlationId</description>
    <environments>Default</environments>
    <formulas>
        <name>containerNeedFormula</name>
        <dataType>String</dataType>
        <expression>IF(ISBLANK({!startingContainerNeeds}), {!containerNeeds}, {!startingContainerNeeds} + &apos;;&apos; + {!containerNeeds})</expression>
    </formulas>
    <formulas>
        <name>defaultCloseDate</name>
        <dataType>Date</dataType>
        <expression>IF({!eventType} = &quot;EMISSIONE_DIRETTA_POLIZZA&quot;,   TODAY(),   DATEVALUE({!input.quote.createdDate}) + 60 )</expression>
    </formulas>
    <formulas>
        <name>isNullGetOpenConteiner</name>
        <dataType>Boolean</dataType>
        <expression>IF(ISNULL({!Get_Open_Container.Id}), true, false)</expression>
    </formulas>
    <formulas>
        <name>PR_DomainType</name>
        <dataType>String</dataType>
        <expression>
IF(CONTAINS({!input.quote.domainType}, &quot;PREVIDENZA&quot;), &quot;ESSIG_VITA_PREVIDENZA&quot;, {!input.quote.domainType})</expression>
    </formulas>
    <formulas>
        <name>productRatingFormula</name>
        <dataType>String</dataType>
        <expression>IF(
{!input.hasCallMeBack}, &apos;Caldissima&apos;,
{!ratingFormula})</expression>
    </formulas>
    <formulas>
        <description>Aggiungere check Call Me Back</description>
        <name>ratingFormula</name>
        <dataType>String</dataType>
        <expression>CASE(
    {!input.quote.status},
    &quot;Show price&quot;, &quot;Tiepida&quot;,
    &quot;Salvataggio preventivo&quot;, &quot;Calda&quot;,
    &quot;Acquisto non concluso&quot;, &quot;Caldissima&quot;,
    &quot;Prospetto previdenziale&quot;, &quot;Tiepida&quot;,
    &quot;Scelta del prodotto&quot;, &quot;Calda&quot;,
    &quot;Offerta previdenziale&quot;, &quot;Caldissima&quot;,
    &quot;Download riepilogo&quot;, &quot;Caldissima&quot;,
    &quot;&quot;
)</expression>
    </formulas>
    <formulas>
        <description>Converte i valori testuali presenti nei campi Rating__c, della trattativa, e productRatingFormula in valori numerici, per consentire il confronto diretto tra i rating e determinare quale rappresenta un livello di interesse commerciale più elevato.</description>
        <name>ratingPriorityFormula</name>
        <dataType>Boolean</dataType>
        <expression>CASE( {!productRatingFormula},
    &quot;Fredda&quot;, 1,
    &quot;Tiepida&quot;, 2,
    &quot;Calda&quot;, 3,
    &quot;Caldissima&quot;, 4,
    0
) &gt; CASE(
    {!containerRecord.Rating__c},
    &quot;Fredda&quot;, 1,
    &quot;Tiepida&quot;, 2,
    &quot;Calda&quot;, 3,
    &quot;Caldissima&quot;, 4,
    0
)</expression>
    </formulas>
    <formulas>
        <name>stageEmissioneDiretta</name>
        <dataType>String</dataType>
        <expression>IF({!eventType} ==  &quot;EMISSIONE_DIRETTA_POLIZZA&quot;, &quot;Chiuso&quot;, &quot;In gestione&quot;)</expression>
    </formulas>
    <formulas>
        <name>subjectFormula</name>
        <dataType>String</dataType>
        <expression>&apos;Soggetto:&apos; + {!Get_Customer.Name}</expression>
    </formulas>
    <interviewLabel>Coverage Based Matching Trattativa Opt {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Coverage Based Matching Trattativa Opt</label>
    <loops>
        <name>Loop_Container_List</name>
        <label>Loop Container List</label>
        <locationX>1282</locationX>
        <locationY>1706</locationY>
        <collectionReference>Container_List</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Clear_Has_a_Child</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_if_Empty_Container_Exist</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loop_over_areas</name>
        <label>loop over areas</label>
        <locationX>1210</locationX>
        <locationY>3362</locationY>
        <collectionReference>input.areaOfNeed</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>check_AON</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_to_Polizza_Diretta</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Product_List</name>
        <label>Loop Product List</label>
        <locationX>1722</locationX>
        <locationY>1922</locationY>
        <collectionReference>Product_List</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_has_a_Child</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_has_a_Container</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Quotes</name>
        <label>Loop Quotes</label>
        <locationX>50</locationX>
        <locationY>2714</locationY>
        <collectionReference>Get_quotes</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Check_if_not_sold</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Ass_Update_Parent_Opp</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Create_Assignment_Event_Creazione_Trattativa</name>
        <label>Create Assignment Event - Creazione Trattativa</label>
        <locationX>1882</locationX>
        <locationY>4670</locationY>
        <connector>
            <targetReference>Initialize_Product_Record</targetReference>
        </connector>
        <inputAssignments>
            <field>Name</field>
            <value>
                <stringValue>Creazione Trattativa</stringValue>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>Opportunity__c</field>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <field>RecordTypeId</field>
            <value>
                <elementReference>ContactHistoryEventRT.Id</elementReference>
            </value>
        </inputAssignments>
        <object>ContactHistory__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordCreates>
    <recordCreates>
        <name>Create_Container</name>
        <label>Create Container</label>
        <locationX>1882</locationX>
        <locationY>4130</locationY>
        <connector>
            <targetReference>Retrieve_New_Container_Name</targetReference>
        </connector>
        <inputReference>containerRecord</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Create_Product</name>
        <label>Create Product</label>
        <locationX>2137</locationX>
        <locationY>5054</locationY>
        <connector>
            <targetReference>Get_Product_after_Insert</targetReference>
        </connector>
        <inputReference>productRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <name>Get_Agency</name>
        <label>Get Agency</label>
        <locationX>1247</locationX>
        <locationY>650</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Agency</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_All_Opp_MD</name>
        <label>Get All Opp MD</label>
        <locationX>1282</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Container_List</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND 3 AND (4 OR 5 OR 6 )</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgenzialeOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Container_After_Insert</name>
        <label>Get Container After Insert</label>
        <locationX>1882</locationX>
        <locationY>4346</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Name_to_New_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Customer</name>
        <label>Get Customer</label>
        <locationX>1247</locationX>
        <locationY>242</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Customer</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Account</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Open_Container</name>
        <label>Get Open Container</label>
        <locationX>2992</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_Container_Found</targetReference>
        </connector>
        <filterLogic>1 AND 2 AND 3 AND (4 OR 5)</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>OmnicanaleOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>AgenzialeOppRT.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <description>Verificare se Channel__c campo da usare per mappare codice prodotto</description>
        <name>Get_Product</name>
        <label>Get Product</label>
        <locationX>1159</locationX>
        <locationY>1058</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_If_Product_Found</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>customerId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ProdottoOppRT.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>StageName</field>
            <operator>NotEqualTo</operator>
            <value>
                <stringValue>Chiuso</stringValue>
            </value>
        </filters>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>PR_DomainType</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_after_Insert</name>
        <label>Get Product after Insert</label>
        <locationX>2137</locationX>
        <locationY>5162</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Retrieve_Product_Name</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Product_Container</name>
        <label>Get Product Container</label>
        <locationX>182</locationX>
        <locationY>1382</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_Product_Container</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Get_Product.Parent__c</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Opportunity</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_quotes</name>
        <label>Get quotes</label>
        <locationX>182</locationX>
        <locationY>2498</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_if_Parent_Opp_not_closed</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Service_Territory</name>
        <label>Get Service Territory</label>
        <locationX>1159</locationX>
        <locationY>950</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Get_Product</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Agency__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>agencyId</elementReference>
            </value>
        </filters>
        <filters>
            <field>Tipo__c</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Agenziale</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsActive</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>ServiceTerritory</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <runInMode>SystemModeWithoutSharing</runInMode>
    <start>
        <locationX>1033</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_Valid_Customer</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>Retrieve_New_Container_Name</name>
        <label>Retrieve New Container Name</label>
        <locationX>1882</locationX>
        <locationY>4238</locationY>
        <connector>
            <targetReference>Get_Container_After_Insert</targetReference>
        </connector>
        <flowName>ST_Opportunity_Name_Assignment</flowName>
        <inputAssignments>
            <name>opportunityId</name>
            <value>
                <elementReference>containerRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>containerName</assignToReference>
            <name>outputName</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>Retrieve_Product_Name</name>
        <label>Retrieve Product Name</label>
        <locationX>2137</locationX>
        <locationY>5270</locationY>
        <connector>
            <targetReference>Assign_Name_to_Product</targetReference>
        </connector>
        <flowName>ST_Opportunity_Name_Assignment</flowName>
        <inputAssignments>
            <name>opportunityId</name>
            <value>
                <elementReference>productRecord.Id</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>productName</assignToReference>
            <name>outputName</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>actualContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>agencyId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AgencyInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>agencySelectedByLocator</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>AgenzialeOppRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>allAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>areaOfNeedPolizzaDiretta</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ATTIVITA_CONTATTO</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <stringValue>ATTIVITA_CONTATTO</stringValue>
        </value>
    </variables>
    <variables>
        <name>ContactHistoryEventRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>containerAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>containerName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerOppJourneyStep</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>containerRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>containerUpdateNeeded</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>contractId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>coveragesToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>currentCoverageRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>currentItem_Container_List</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>currentItem_Product_List</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>customerId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CustomerInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Account</objectType>
    </variables>
    <variables>
        <name>emptyContainer</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>eventType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>existingContainer</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>hasAChild</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>input</name>
        <apexClass>OpportunityWSWrapper</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputActivity</name>
        <apexClass>RequestActivity</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>intermediaryId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>matchCount</name>
        <dataType>Number</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>0</scale>
    </variables>
    <variables>
        <name>objectType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OmnicanaleOppRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>OppAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>oppConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityConfiguration__mdt</objectType>
    </variables>
    <variables>
        <name>opportunityCoverageCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>OpportunityNeedUpdateOutput</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>prodConfig</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>ProductConfiguration__mdt</objectType>
    </variables>
    <variables>
        <name>ProdottoOppRT</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>productName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ProductNeededUpdateOutput</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>productRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>quoteAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>quoteExists</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>quoteExpiryDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quoteTICDate</name>
        <dataType>DateTime</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>startingContainerNeeds</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>statoCliente</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
