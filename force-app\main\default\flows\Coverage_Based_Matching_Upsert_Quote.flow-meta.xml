<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>Ass_Update_Opportunity_Coverage</name>
        <label>Ass Update Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.ExpectedYearlyGrowth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.expectedYearlyGrowth</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.NumberOfChildren__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.numberOfChildren</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.PrevidentialGap__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.previdentialGap</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.ProductOfInterest__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.productOfInterest</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.RAL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.ral</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.Sector__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.sector</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.StageName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.stageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.YearOfRetirement__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.yearOfRetirement</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>getOpportunityCoverage.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOppCov2.externalId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loopOppCov2</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Opportunity_Coverage</name>
        <label>Initialize Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ExpectedYearlyGrowth__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.expectedYearlyGrowth</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.RAL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.ral</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Sector__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.sector</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.NumberOfChildren__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.numberOfChildren</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.PrevidentialGap__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.previdentialGap</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ProductOfInterest__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.productOfInterest</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.StageName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.stageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Quote__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputQuote.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.YearOfRetirement__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.yearOfRetirement</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loopOPPCov</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Prepare_Insert_Quote</name>
        <label>Prepare Insert Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>inputQuote.MonthlyContribution__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.monthlyContribution</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExpirationDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.expirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.status</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>myDomainTypeSimulazione</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.IsStored__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>isStoredVar</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>QuoteInsuranceRTInput.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DocumentURL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.linkUnica</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.OpportunityId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OpportunityProductInput.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.accountId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Previdenza integrativa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Input_Quote_RecordType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Insert_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Prepare_Update_Quote</name>
        <label>Prepare Update Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>inputQuote.MonthlyContribution__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.monthlyContribution</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExpirationDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.expirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.status</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.domainType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.IsStored__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>isStoredVar</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DocumentURL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.linkUnica</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Previdenza integrativa</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Simulazione_Attiva</name>
        <label>Simulazione Attiva</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>isStoredVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkExistingQuote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Simulazione_Stored</name>
        <label>Simulazione Stored</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>isStoredVar</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>checkExistingQuote</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>checkExistingQuote</name>
        <label>checkExistingQuote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Prepare_Insert_Quote</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>YesQuote</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputQuote.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Prepare_Update_Quote</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>if_Opportunity_Coverage_Exists</name>
        <label>if Opportunity Coverage Exists</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>loopOPPCov</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Yes</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getOpportunityCoverage</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>loopOppCov2</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>is_External_Id</name>
        <label>is External Id</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>getPreventivoAttivo</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>External_Id</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputOpportunityWrapper.quote.externalId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>inputOpportunityWrapper.quote.externalId</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getQuoteFromWrapper</targetReference>
            </connector>
            <label>External Id</label>
        </rules>
    </decisions>
    <decisions>
        <name>isPreventivoAttivo</name>
        <label>isPreventivoAttivo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Simulazione_Attiva</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Preventivo_Attivo</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getPreventivoAttivo.Id</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>getPreventivoAttivo.Id</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Simulazione_Stored</targetReference>
            </connector>
            <label>Preventivo Attivo</label>
        </rules>
    </decisions>
    <description>vers prec 26
correlationId</description>
    <environments>Default</environments>
    <formulas>
        <name>myDomainTypePreventivo</name>
        <dataType>String</dataType>
        <expression>&apos;ESSIG_VITA_PREVIDENZA&apos;</expression>
    </formulas>
    <formulas>
        <name>myDomainTypeSimulazione</name>
        <dataType>String</dataType>
        <expression>&apos;ESSIG_VITA_PREVIDENZA_SIM&apos;</expression>
    </formulas>
    <interviewLabel>Coverage-Based Matching-Upsert Quote {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Coverage-Based Matching-Upsert Quote</label>
    <loops>
        <name>loopOPPCov</name>
        <label>loopOppCovInsert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Initialize_Opportunity_Coverage</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>InsertOpportunityCoverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loopOppCov2</name>
        <label>loopOppCovUpdate</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Ass_Update_Opportunity_Coverage</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Update_Existing_Opportunity_Coverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Insert_Quote</name>
        <label>Insert Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getOpportunityCoverage</targetReference>
        </connector>
        <inputReference>inputQuote</inputReference>
    </recordCreates>
    <recordCreates>
        <name>InsertOpportunityCoverage</name>
        <label>InsertOpportunityCoverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>updateStoredQuotes</targetReference>
        </connector>
        <inputReference>OpportunityCoverageToInsert</inputReference>
    </recordCreates>
    <recordLookups>
        <name>getOpportunityCoverage</name>
        <label>getOpportunityCoverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>if_Opportunity_Coverage_Exists</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Quote__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputQuote.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OpportunityCoverage__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getPreventivoAttivo</name>
        <label>getPreventivoAttivo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>isPreventivoAttivo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myDomainTypePreventivo</elementReference>
            </value>
        </filters>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>OpportunityProductInput.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsStored__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Quote</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getQuoteFromWrapper</name>
        <label>getQuoteFromWrapper</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getPreventivoAttivo</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.externalId</elementReference>
            </value>
        </filters>
        <object>Quote</object>
        <outputReference>inputQuote</outputReference>
        <queriedFields>AreasOfNeed__c</queriedFields>
        <queriedFields>CIP__c</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <queriedFields>OpportunityId</queriedFields>
        <queriedFields>MonthlyContribution__c</queriedFields>
        <queriedFields>CreatedDateTPD__c</queriedFields>
        <queriedFields>ExpirationDate</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>Status</queriedFields>
        <queriedFields>FolderId__c</queriedFields>
        <queriedFields>ExternalId__c</queriedFields>
        <queriedFields>IsStored__c</queriedFields>
        <queriedFields>DocumentURL__c</queriedFields>
        <queriedFields>DomainType__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>Update_Existing_Opportunity_Coverage</name>
        <label>Update Existing Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>updateStoredQuotes</targetReference>
        </connector>
        <inputReference>getOpportunityCoverage</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>Update_Quote</name>
        <label>Update Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getOpportunityCoverage</targetReference>
        </connector>
        <inputReference>inputQuote</inputReference>
    </recordUpdates>
    <recordUpdates>
        <name>updateStoredQuotes</name>
        <label>updateStoredQuotes</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <filterLogic>and</filterLogic>
        <filters>
            <field>OpportunityId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>OpportunityProductInput.Id</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsStored__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </filters>
        <filters>
            <field>DomainType__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>myDomainTypeSimulazione</elementReference>
            </value>
        </filters>
        <filters>
            <field>Id</field>
            <operator>NotEqualTo</operator>
            <value>
                <elementReference>inputQuote.Id</elementReference>
            </value>
        </filters>
        <inputAssignments>
            <field>IsStored__c</field>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputAssignments>
        <object>Quote</object>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>is_External_Id</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>Input_Quote_RecordType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputOpportunityWrapper</name>
        <apexClass>OpportunityWSWrapper</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputQuote</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>isStoredVar</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>OpportunityCoverageToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>OpportunityProductInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>QuoteInsuranceRTInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
</Flow>
