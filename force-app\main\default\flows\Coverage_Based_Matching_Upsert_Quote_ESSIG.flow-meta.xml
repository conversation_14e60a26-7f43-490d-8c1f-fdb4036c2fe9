<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Clean_Area_Of_Need</name>
        <label>Clean Area Of Need</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>SanitizeMultiPicklistField</actionName>
        <actionType>apex</actionType>
        <connector>
            <targetReference>Prepare_Insert_Quote</targetReference>
        </connector>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>input</name>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </inputParameters>
        <nameSegment>SanitizeMultiPicklistField</nameSegment>
        <offset>0</offset>
        <outputParameters>
            <assignToReference>allAreasOfNeed</assignToReference>
            <name>output</name>
        </outputParameters>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <assignments>
        <name>Add_Coverage_to_Collection</name>
        <label>Add Coverage to Collection</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoverageCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>OpportunityCoverageToInsert</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>loopOPPCov</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Coverage_To_Collection_To_Insert</name>
        <label>Add Coverage To Collection To Insert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoverageCollectionToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Coverage_Collection</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Coverage_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Ext_Id_Coverage</name>
        <label>Add Ext Id Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoveragesExtIDS</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverages.externalId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Input_Coverages</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_New_Coverage_To_List</name>
        <label>Add New Coverage To List</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoverageCollectionToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>OpportunityCoverageToInsert</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Input_Coverage_To_Insert</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Add_Opp_Coverage_To_List</name>
        <label>Add Opp Coverage To List</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoverageCollectionToInsert</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>OpportunityCoverageToUpdate</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Remove_Ext_Id_From_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Ass_Update_Opportunity_Coverage</name>
        <label>Ass Update Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.StageName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.stageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.description</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Asset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.asset</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.rating</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Conventions__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.conventionCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.BirthDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.birthDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.Fractionation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.fractionation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.ProductOfInterest__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Opp_Cov_Input_To_Update.productOfInterest</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Opp_Coverage_To_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Quote</name>
        <label>Assign Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>Loop_Over_Coverage_Collection.Quote__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputQuote.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Coverage_To_Collection_To_Insert</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Var_to_Update</name>
        <label>Assign Var to Update</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Founded_Coverages</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Opp_Cov_Input_To_Update</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_Evaluate_Aggregates</name>
        <label>Copy 2 of Evaluate Aggregates</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>allAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>areaOfNeedConcatenationUPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>Loop_Over_Coverages_for_Amount_and_AON.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Coverages_for_Amount_and_AON.stageName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Coverages_for_Amount_and_AON</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Copy_2_of_Initialize_Opportunity_Coverage</name>
        <label>Copy 2 of Initialize Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ProductOfInterest__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.previdentialGap</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.StageName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.stageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Quote__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputQuote.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.description</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Asset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.asset</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.rating</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Conventions__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.conventionCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.BirthDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.birthDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Fractionation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.fractionation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Loop_Over_Input_Coverage_To_Insert.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_New_Coverage_To_List</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Evaluate_Aggregates</name>
        <label>Evaluate Aggregates</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>allAreasOfNeed</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>areaOfNeedConcatenation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteAmount</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>loopOPPCov.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>quoteStatus</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.stageName</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Initialize_Opportunity_Coverage</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Initialize_Opportunity_Coverage</name>
        <label>Initialize Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.AreaOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.areaOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ProductOfInterest__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.productOfInterest</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.StageName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.stageName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Email__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.email</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FirstName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.firstName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.LastName__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.lastName</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.FiscalCode__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.fiscalCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.MobilePhone__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.mobilePhone</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Quote__c</assignToReference>
            <operator>Assign</operator>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Description__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.description</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Asset__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.asset</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Amount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.amount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Rating__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.rating</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Conventions__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.conventionCode</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToUpdate.BirthDate__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.birthDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.Fractionation__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.fractionation</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>loopOPPCov.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>OpportunityCoverageToInsert.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Add_Coverage_to_Collection</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Prepare_Insert_Quote</name>
        <label>Prepare Insert Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>inputQuote.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExpirationDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.expirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.status</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.domainType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.IsStored__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>QuoteInsuranceRTInput.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DocumentURL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.linkUnica</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.OpportunityId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>OpportunityProductInput.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.accountId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Input_Quote_RecordType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExternalId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.externalId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.QuoteAmount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteStatus</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CorrelationId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Insert_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Prepare_Update_Quote</name>
        <label>Prepare Update Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>inputQuote.MonthlyContribution__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.monthlyContribution</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.ExpirationDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.expirationDate</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Name</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.status</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CIP__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.cip</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DomainType__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.domainType</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.DocumentURL__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.linkUnica</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.AreasOfNeed__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>allAreasOfNeed</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.IsStored__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.QuoteAmount__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>quoteAmount</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.EngagementPoint__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.engagementPoint</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.CreatedDateTPD__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.createdDateTPD</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>inputQuote.FolderId__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.folderId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Update_Quote</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Remove_Ext_Id_From_List</name>
        <label>Remove Ext Id From List</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CoveragesExtIDS</assignToReference>
            <operator>RemoveAll</operator>
            <value>
                <elementReference>OpportunityCoverageToUpdate.ExternalId__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Loop_Over_Opp_Cov_Input_To_Update</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>check_Existing_Opp_Coverage</name>
        <label>check Existing Opp Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_Founded_Coverages</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>New Coverage</defaultConnectorLabel>
        <rules>
            <name>Existing_Coverage</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CoveragesExtIDS</leftValueReference>
                <operator>Contains</operator>
                <rightValue>
                    <elementReference>Loop_Over_Founded_Coverages.ExternalId__c</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Var_to_Update</targetReference>
            </connector>
            <label>Existing Coverage</label>
        </rules>
    </decisions>
    <decisions>
        <name>check_External_Id</name>
        <label>check External Id</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_Input_Coverage_To_Insert</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>External_Id_Match</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Loop_Over_Input_Coverage_To_Insert.externalId</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Ids</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Copy_2_of_Initialize_Opportunity_Coverage</targetReference>
            </connector>
            <label>External Id Match</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_if_New_Opportunity_Coverage</name>
        <label>Check if New Opportunity Coverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Upsert_Opp_COverage</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Opp_Cov_To_Create</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>CoveragesExtIDS</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Over_Ids</targetReference>
            </connector>
            <label>Opp Cov To Create</label>
        </rules>
    </decisions>
    <decisions>
        <name>checkExistingQuote</name>
        <label>checkExistingQuote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>loopOPPCov</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>YesQuote</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>inputQuote</leftValueReference>
                <operator>IsBlank</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>inputQuote</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Loop_Over_Coverages_for_Amount_and_AON</targetReference>
            </connector>
            <label>Yes</label>
        </rules>
    </decisions>
    <decisions>
        <name>Decision_4</name>
        <label>Check External Id To Update</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Loop_Over_Opp_Cov_Input_To_Update</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_4</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>OpportunityCoverageToUpdate.ExternalId__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>Loop_Over_Opp_Cov_Input_To_Update.externalId</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Ass_Update_Opportunity_Coverage</targetReference>
            </connector>
            <label>Ext Id Found</label>
        </rules>
    </decisions>
    <description>vers prec 11
correlationId</description>
    <environments>Default</environments>
    <formulas>
        <name>areaOfNeedConcatenation</name>
        <dataType>String</dataType>
        <expression>{!allAreasOfNeed} + {!loopOPPCov.areaOfNeed} + &apos;;&apos;</expression>
    </formulas>
    <formulas>
        <name>areaOfNeedConcatenationUPD</name>
        <dataType>String</dataType>
        <expression>{!allAreasOfNeed} + {!Loop_Over_Coverages_for_Amount_and_AON.areaOfNeed} + &apos;;&apos;</expression>
    </formulas>
    <formulas>
        <name>myDomainTypePreventivo</name>
        <dataType>String</dataType>
        <expression>&apos;ESSIG_VITA_PREVIDENZA&apos;</expression>
    </formulas>
    <formulas>
        <name>myDomainTypeSimulazione</name>
        <dataType>String</dataType>
        <expression>&apos;ESSIG_VITA_PREVIDENZA_SIM&apos;</expression>
    </formulas>
    <interviewLabel>Coverage-Based Matching-Upsert Quote- ESSIG {!$Flow.CurrentDateTime}</interviewLabel>
    <label>Coverage-Based Matching-Upsert Quote- ESSIG</label>
    <loops>
        <name>Loop_Over_Coverage_Collection</name>
        <label>Loop Over Coverage Collection</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>CoverageCollection</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Assign_Quote</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>InsertOpportunityCoverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Coverages_for_Amount_and_AON</name>
        <label>Loop Over Coverages for Amount and AON</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Copy_2_of_Evaluate_Aggregates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Prepare_Update_Quote</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Founded_Coverages</name>
        <label>Loop Over Founded Coverages</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>getOpportunityCoverage</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>check_Existing_Opp_Coverage</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Check_if_New_Opportunity_Coverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Ids</name>
        <label>Loop Over Ids</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>CoveragesExtIDS</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Loop_Over_Input_Coverage_To_Insert</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Upsert_Opp_COverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Input_Coverage_To_Insert</name>
        <label>Loop Over Input Coverage To Insert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>check_External_Id</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Over_Ids</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Input_Coverages</name>
        <label>Loop Over Input Coverages</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Add_Ext_Id_Coverage</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>getOpportunityCoverage</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>Loop_Over_Opp_Cov_Input_To_Update</name>
        <label>Loop Over Opp Cov Input To Update</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Decision_4</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Loop_Over_Founded_Coverages</targetReference>
        </noMoreValuesConnector>
    </loops>
    <loops>
        <name>loopOPPCov</name>
        <label>loopOppCovInsert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <collectionReference>inputOpportunityWrapper.quote.coverages</collectionReference>
        <iterationOrder>Asc</iterationOrder>
        <nextValueConnector>
            <targetReference>Evaluate_Aggregates</targetReference>
        </nextValueConnector>
        <noMoreValuesConnector>
            <targetReference>Clean_Area_Of_Need</targetReference>
        </noMoreValuesConnector>
    </loops>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordCreates>
        <name>Insert_Quote</name>
        <label>Insert Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Loop_Over_Coverage_Collection</targetReference>
        </connector>
        <inputReference>inputQuote</inputReference>
    </recordCreates>
    <recordCreates>
        <name>InsertOpportunityCoverage</name>
        <label>InsertOpportunityCoverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputReference>CoverageCollectionToInsert</inputReference>
    </recordCreates>
    <recordCreates>
        <name>Upsert_Opp_COverage</name>
        <label>Upsert Opp COverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <inputReference>CoverageCollectionToInsert</inputReference>
    </recordCreates>
    <recordLookups>
        <name>getOpportunityCoverage</name>
        <label>getOpportunityCoverage</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Loop_Over_Founded_Coverages</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>In</operator>
            <value>
                <elementReference>CoveragesExtIDS</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>OpportunityCoverage__c</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getQuoteFromWrapper</name>
        <label>getQuoteFromWrapper</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>checkExistingQuote</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>ExternalId__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>inputOpportunityWrapper.quote.externalId</elementReference>
            </value>
        </filters>
        <filters>
            <field>DomainType__c</field>
            <operator>Contains</operator>
            <value>
                <stringValue>ESSIG</stringValue>
            </value>
        </filters>
        <object>Quote</object>
        <outputReference>inputQuote</outputReference>
        <queriedFields>AreasOfNeed__c</queriedFields>
        <queriedFields>CIP__c</queriedFields>
        <queriedFields>AccountId</queriedFields>
        <queriedFields>OpportunityId</queriedFields>
        <queriedFields>MonthlyContribution__c</queriedFields>
        <queriedFields>CreatedDateTPD__c</queriedFields>
        <queriedFields>ExpirationDate</queriedFields>
        <queriedFields>Name</queriedFields>
        <queriedFields>Status</queriedFields>
        <queriedFields>FolderId__c</queriedFields>
        <queriedFields>ExternalId__c</queriedFields>
        <queriedFields>IsStored__c</queriedFields>
        <queriedFields>DocumentURL__c</queriedFields>
        <queriedFields>DomainType__c</queriedFields>
    </recordLookups>
    <recordUpdates>
        <name>Update_Quote</name>
        <label>Update Quote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Loop_Over_Input_Coverages</targetReference>
        </connector>
        <inputReference>inputQuote</inputReference>
    </recordUpdates>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>getQuoteFromWrapper</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>allAreasOfNeed</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>CoverageCollection</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>CoverageCollectionToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>CoveragesExtIDS</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>Input_Quote_RecordType</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputOpportunityWrapper</name>
        <apexClass>OpportunityWSWrapper</apexClass>
        <dataType>Apex</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>inputQuote</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Quote</objectType>
    </variables>
    <variables>
        <name>OpportunityCoverageToInsert</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>OpportunityCoverageToUpdate</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>OpportunityCoverage__c</objectType>
    </variables>
    <variables>
        <name>OpportunityProductInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>Opportunity</objectType>
    </variables>
    <variables>
        <name>quoteAmount</name>
        <dataType>Currency</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <scale>2</scale>
    </variables>
    <variables>
        <name>QuoteInsuranceRTInput</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>RecordType</objectType>
    </variables>
    <variables>
        <name>quoteStatus</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
</Flow>
