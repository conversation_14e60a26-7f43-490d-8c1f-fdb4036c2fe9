<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Custom_Notification_withoutTargetID</name>
        <label>Send Custom Notification withoutTargetID - Delete</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotificationType_Creation.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIDCollection</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>NotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>NotificationBodyFormula</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetPageRef</name>
            <value>
                <elementReference>TargetPageReference</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <actionCalls>
        <name>Send_Custom_Notification_withTargetID</name>
        <label>Send Custom Notification with TargetID</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>customNotificationAction</actionName>
        <actionType>customNotificationAction</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>customNotifTypeId</name>
            <value>
                <elementReference>CustomNotificationType_Creation.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientIds</name>
            <value>
                <elementReference>RecipientIDCollection</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>title</name>
            <value>
                <elementReference>NotificationTitle</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>body</name>
            <value>
                <elementReference>NotificationBodyFormula</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>targetId</name>
            <value>
                <elementReference>TargetRecordID</elementReference>
            </value>
        </inputParameters>
        <nameSegment>customNotificationAction</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Recipient_ID_Single_AND_Send_Variable</name>
        <label>Assign Recipient ID Single AND Send Variable = true</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>RecipientIDCollection</assignToReference>
            <operator>Add</operator>
            <value>
                <elementReference>RecipientID</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>SendNotification</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Send_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Send_Variable</name>
        <label>Assign Send Variable = true</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>SendNotification</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Send_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_Send_Variable_False</name>
        <label>Assign Send Variable False</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>SendNotification</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Send_Notification</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Custom_Notification_Type_Variable</name>
        <label>Set Custom Notification Type Variable</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>CustomNotificationType_Creation</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_Custom_Notification_Creation</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Get_globalNotificationSchema</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Set_Notification_Body_Title</name>
        <label>Set Notification Body and Title</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>NotificationBody</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_globalNotificationSchema.NotificationBody__c</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>NotificationTitle</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Get_globalNotificationSchema.NotificationTitle__c</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Recipient_ID</targetReference>
        </connector>
    </assignments>
    <decisions>
        <name>Check_Global_Notification_Schema</name>
        <label>Check Global Notification Schema</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>NOT Notify</defaultConnectorLabel>
        <rules>
            <name>Notify</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_globalNotificationSchema</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Set_Notification_Body_Title</targetReference>
            </connector>
            <label>Notify</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Recipient_ID</name>
        <label>Check Recipient ID</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_Send_Variable_False</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Recipient Not Valid</defaultConnectorLabel>
        <rules>
            <name>Recipient_ID_Single</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>RecipientIDCollection</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>RecipientID</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Recipient_ID_Single_AND_Send_Variable</targetReference>
            </connector>
            <label>Recipient ID Single</label>
        </rules>
        <rules>
            <name>Recipient_ID_Collection</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>RecipientIDCollection</leftValueReference>
                <operator>IsEmpty</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_Send_Variable</targetReference>
            </connector>
            <label>Recipient ID Collection</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Send_Notification</name>
        <label>Check Send Notification</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Send Notification = false</defaultConnectorLabel>
        <rules>
            <name>Send_Notification_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>SendNotification</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Notification_Delete</targetReference>
            </connector>
            <label>Send Notification = true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Notification_Delete</name>
        <label>Notification Delete</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Send_Custom_Notification_withTargetID</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>NO</defaultConnectorLabel>
        <rules>
            <name>YES</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_globalNotificationSchema.Notification_Name__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Delete</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Custom_Notification_withoutTargetID</targetReference>
            </connector>
            <label>YES</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>NotificationBodyFormula</name>
        <dataType>String</dataType>
        <expression>SUBSTITUTE(
    SUBSTITUTE({!NotificationBody}, &quot;[DETAILS]&quot;, {!NotificationBodyDetails}),
    &quot;[ADDDETAILS]&quot;, {!NotificationBodyAdditionalDetails}
)</expression>
    </formulas>
    <interviewLabel>NotificationSystemOrchestratorNew {!$Flow.CurrentDateTime}</interviewLabel>
    <label>NotificationSystemOrchestratorNew</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_Custom_Notification_Creation</name>
        <label>Get Custom Notification - Creation</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Set_Custom_Notification_Type_Variable</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>CustomNotifTypeName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Creation</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>CustomNotificationType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_globalNotificationSchema</name>
        <label>Get GlobalNotificationSchema</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Global_Notification_Schema</targetReference>
        </connector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Notification_Name__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>NotificationName</elementReference>
            </value>
        </filters>
        <filters>
            <field>Object__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Object</elementReference>
            </value>
        </filters>
        <filters>
            <field>To_Whom__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>ToWhom</elementReference>
            </value>
        </filters>
        <filters>
            <field>Channel__c</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>Channel</elementReference>
            </value>
        </filters>
        <filters>
            <field>IsActive__c</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>globalNotificationSchema__mdt</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Get_Custom_Notification_Creation</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <variables>
        <name>Channel</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>CustomNotificationType_Creation</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <objectType>CustomNotificationType</objectType>
    </variables>
    <variables>
        <name>NotificationBody</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NotificationBodyAdditionalDetails</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NotificationBodyDetails</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NotificationName</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>NotificationTitle</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>Object</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>RecipientID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>RecipientIDCollection</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>SendNotification</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>TargetPageReference</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>TargetRecordID</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>ToWhom</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
</Flow>
