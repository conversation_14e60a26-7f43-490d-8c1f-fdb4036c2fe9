<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <emailDefault>false</emailDefault>
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Dettaglio</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Area__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Activity__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Detail__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AreaOfNeed__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Esito__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Requestfromclient__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>CCEngaged__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Outcome__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>SubOutcome__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ActualCalls__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>true</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Correlato a</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Insurance_Policy__c</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Opportunity__c</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>AssetId</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Incident__c</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Origin</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Description Information</label>
        <layoutColumns/>
        <style>OneColumn</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Case Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>true</editHeading>
        <label>Web Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>System Information</label>
        <layoutColumns/>
        <layoutColumns/>
        <style>TwoColumnsTopToBottom</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Edit</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Delete</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>CloseCase</actionName>
            <actionType>StandardButton</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Esito_Finale</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>3</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>4</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.OPT_OUT</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>5</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewTask</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>6</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEvent</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>7</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentNote</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>8</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.TextPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>9</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>FeedItem.ContentPost</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>10</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Chiudi</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>11</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>NewEventOpportunity</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>12</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Da_Richiamare_Agenzia</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>13</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Esito_Intermedio</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>14</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Revoca_Consensi_CC</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>15</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.Riassegna</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>16</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ContactId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>CallType</fields>
        <fields>FromPhoneNumber</fields>
        <fields>CallResolution</fields>
        <fields>CallStartDateTime</fields>
        <fields>EndUser</fields>
        <fields>Owner</fields>
        <fields>CallOrigin</fields>
        <fields>QualityScore</fields>
        <relatedList>VoiceCalls</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>Name</fields>
        <fields>CallType</fields>
        <fields>FromPhoneNumber</fields>
        <fields>CallResolution</fields>
        <fields>CallStartDateTime</fields>
        <fields>EndUser</fields>
        <fields>Owner</fields>
        <fields>CallOrigin</fields>
        <fields>QualityScore</fields>
        <relatedList>VoiceCall.RelatedCase__c</relatedList>
    </relatedLists>
    <relatedObjects>ContactId</relatedObjects>
    <relatedObjects>AccountId</relatedObjects>
    <runAssignmentRulesDefault>false</runAssignmentRulesDefault>
    <showEmailCheckbox>true</showEmailCheckbox>
    <showHighlightsPanel>false</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showKnowledgeComponent>false</showKnowledgeComponent>
    <showRunAssignmentRulesCheckbox>true</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>true</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9O00000BvQ5u</masterLabel>
        <sizeX>1</sizeX>
        <sizeY>0</sizeY>
        <summaryLayoutStyle>Default</summaryLayoutStyle>
    </summaryLayout>
</Layout>
