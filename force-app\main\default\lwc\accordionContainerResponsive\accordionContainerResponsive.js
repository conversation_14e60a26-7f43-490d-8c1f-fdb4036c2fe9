import { LightningElement, api, track } from 'lwc'; // Import necessary modules from LWC
import { ShowToastEvent } from 'lightning/platformShowToastEvent'; // Import toast event for displaying notifications
import { OmniscriptBaseMixin } from "omnistudio/omniscriptBaseMixin";

import getStoredQuotesInfo from '@salesforce/apex/AccordionController.getStoredQuotesInfo'; // Import Apex method for fetching quotes

import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need'; // Import static resource for area of need images
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';

// Constants for image URLs
const PET_IMG = areas_of_need_images + '/areas_of_need/on/pet.png'; 
const HOUSE_IMG = areas_of_need_images + '/areas_of_need/on/casa.png';
const FAMILY_IMG = areas_of_need_images + '/areas_of_need/on/famiglia.png';
const INJURIES_IMG = areas_of_need_images + '/areas_of_need/on/infortuni.png';
const MOTORCYCLE_IMG = areas_of_need_images + '/areas_of_need/on/mobilita.png';
const HEALTH_IMG = areas_of_need_images + '/areas_of_need/on/salute.png';
const CAR_IMG = areas_of_need_images + '/areas_of_need/on/veicoli.png';
const TRAVEL_IMG = areas_of_need_images + '/areas_of_need/on/viaggi.png';
const PREVIDENCE_IMG = areas_of_need_images + '/areas_of_need/on/previdenza.png';
const VITA_IMG = areas_of_need_images + '/areas_of_need/on/<EMAIL>';

const MIN_SCREEN_SIZE_FOR_PC = 1200; // Minimum screen width for PC view

export default class AccordionContainerResponsive extends OmniscriptBaseMixin(LightningElement) {
    @api recordId; // API property to hold the record ID
    @api isStored = false; // NEW, signals if the component is being displayed on the Quote history tab
    @api source; // Opportunity source channel

    @track isLoading = true;

    @api
    get targetId()
    {
        return this.recordId;
    }
    set targetId(val)
    {
        this.recordId = val;
        this.checkInput();
    }

    @api
    get quoteStored()
    {
        return this.isStored;
    }
    set quoteStored(val)
    {
        this.isStored = val;
        this.checkInput();
    }

    @api
    get productChannel()
    {
        return this.source;
    }
    set productChannel(val)
    {
        this.source = val;
        this.checkInput();
    }
    
    @track quotes = []; // Trackable property to hold the list of quotes
    @track firstQuote = []; // Trackable property to hold the first quote
    @track showAll; // Trackable property to manage the display of all quotes
    @track showAllText; // Trackable property to manage the display text
    @track hasQuote = false; // Trackable property to check if there are any quotes
    @track hasMultipleQuotes = false; // Trackable property to check if there are multiple quotes
    @track isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC; // Trackable property to check if the device is a tablet

    checkInput()
    {
        if(this.recordId && this.isStored && this.source)
        {
            this.fetchQuotes();
        }
    }

    // Method to handle the show/hide functionality of quotes
    handleClick() {
        this.showAll = !this.showAll; // Toggle the showAll property
        this.showAllText = (this.showAll === false) ? 'Mostra tutti' : 'Nascondi'; // Update the display text based on the toggle state
    }

    // Lifecycle hook that gets called when the component is inserted into the DOM
    connectedCallback() {
        // Add an event listener to handle window resize events
        window.addEventListener('resize', () => {
            this.isTablet = window.innerWidth <= MIN_SCREEN_SIZE_FOR_PC; // Update the isTablet property based on the window width
        });
        this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }

    // Method to fetch quotes from the server
    fetchQuotes() {
        if(typeof this.isStored !== "boolean")
        {
            this.isStored = this.isStored === "true";
        }
        getStoredQuotesInfo({ recordId: this.recordId, isStored: this.isStored }) // Call Apex method with the current record ID and specifying if the Quote history is needed
        .then(data => { // Handle successful response
            // Process each quote received from the server
            const updatedQuotes = []; // Crea un nuovo array per i dati aggiornati
            const updatedFirstQuote = []; // Crea un nuovo array per la prima quote
            for (let i = 0; i < data.length; i++) {
                let _opportunityCoverages = [];
                // Process each coverage within the quote
                for (let j = 0; j < data[i].opportunityCoverages.length; j++) {
                    let opportunityCoverage = {
                        isFirst: j === 0 ? true : false, // Check if it's the first coverage
                        areaOfNeedImage: this.convertAreaOfNeedToImage(data[i].opportunityCoverages[j].areaOfNeed), // Get the image for the area of need
                        amount: data[i].opportunityCoverages[j].amount, // Coverage amount
                        assetItems: data[i].opportunityCoverages[j].areaOfNeed == 'Famiglia' ? ['Famiglia'] : data[i].opportunityCoverages[j].assets, // List of assets
                        descriptionItems: data[i].opportunityCoverages[j].description, // Description items
                        conventions: data[i].opportunityCoverages[j].conventions, // List of conventions
                        fullName: data[i].opportunityCoverages[j].fullName, // Full name
                        fractionation: data[i].opportunityCoverages[j].fractionation, // Fractionation details
                        stage: data[i].opportunityCoverages[j].stage, // Stage of the coverage
                        monthlyContribution: data[i].opportunityCoverages[j].monthlyContribution,
                        ral: data[i].opportunityCoverages[j].ral,
                        yearlyGrowth: data[i].opportunityCoverages[j].yearlyGrowth,
                        previdentialGap: data[i].opportunityCoverages[j].previdentialGap,
                        retirementYear: data[i].opportunityCoverages[j].retirementYear,
                        targetProduct: data[i].opportunityCoverages[j].targetProduct,
                        numOfChildren: data[i].opportunityCoverages[j].numOfChildren,
                        sector: data[i].opportunityCoverages[j].sector
                    };

                    _opportunityCoverages.push(opportunityCoverage); // Add coverage to the list
                }
                let quote = {
                    isOpportunityClosed: (data[i].opportunityStage === 'In gestione') ? false : true, // Check if the opportunity is closed
                    cip: data[i].cip || '---', //SC 09-04-25 OCT 1289109
                    name: data[i].name, // Quote name
                    recordId: data[i].recordId, // Quote record ID
                    isFirst: i === 0 ? true : false, // Check if it's the first quote
                    areasOfNeedImages: this.convertAreasOfNeedToImages(data[i].areasOfNeed), // Get the images for areas of need
                    digitalStep: data[i].status || '-', // Quote fase di abbandono //SC 09-04-25 OCT 1289109
                    source: data[i].source == 'Canale digitale' ? 'Digitale': data[i].source, // Quote source //SC 09-04-25 OCT 1289109
                    totalAmount: data[i].totalAmount, // Total amount of the quote
                    creationDate: data[i].creationDate, // Creation date of the quote
                    expirationDate: data[i].expirationDate, // Expiration date of the quote
                    domainType: data[i].domainType,
                    unicaLink: data[i].unicaLink, // Unica link for the quote
                    opportunityCoverages: _opportunityCoverages, // List of coverages in the quote
                    monthlyContribution: data[i].monthlyContribution,
                    documentUrl: data[i].documentUrl,
                    status: data[i].commercialStatus, // verificare corretta logica this.isStored === false ? 'Attivo' : 'Disattivo'
                    isSold: data[i].commercialStatus === 'Venduto', //Guy De Roquefeuil: 19-05-25 // SC: 26-05-25
                    isExpired: data[i].commercialStatus === 'Scaduto', // SC: 26-05-25
                    isOpportunityAssignedOrBefore: data[i].opportunityStage != 'In gestione' && data[i].opportunityStage != 'Chiuso'
                }

                console.log('Opportunity Status: ' + data[i].opportunityStatus + ' -> ' + quote.isOpportunityClosed); // Log the opportunity status
                console.log('Quote info: ' + JSON.stringify(quote)); // Log the opportunity status
               
                

                updatedQuotes.push(quote); // Aggiungi la quote al nuovo array
                if (i === 0) // Check if it's the first quote
                    updatedFirstQuote.push(quote); // Add the first quote to the firstQuote list


                // this.hasQuote = true; // Set hasQuote to true as we have quotes now
                // this.quotes.push(quote); // Add the quote to the list of quotes

                // if (i === 0) // Check if it's the first quote
                //     this.firstQuote.push(quote); // Add the first quote to the firstQuote list
            }
            // this.showAll = false; // Set showAll to false initially
            // this.showAllText = 'Mostra tutti'; // Set the initial showAllText

            // this.hasMultipleQuotes = this.quotes.length > 1; // Check if there are multiple quotes
            // this.isLoading = false;


            // Aggiorna le proprietà tracciate con nuovi riferimenti
            this.quotes = [...updatedQuotes];
            this.firstQuote = [...updatedFirstQuote];
            this.hasQuote = updatedQuotes.length > 0;// Set hasQuote to true as we have quotes now
            this.hasMultipleQuotes = updatedQuotes.length > 1; // Check if there are multiple quotes
            this.showAll = false; // Set showAll to false initially
            this.showAllText = 'Mostra tutti';  // Set the initial showAllText
            this.isLoading = false;


        }).catch(error => { // Handle any errors during the fetch operation
            console.log('Data retrieval error', error.body.message);
            this.isLoading = false;
            this.showToast('An error occurred, please contact System Admin', error.body.message, 'error'); // Show a toast with the error message
        });
    }

    // Method to convert an array of areas of need to their respective images
    convertAreasOfNeedToImages(areasOfNeed) {
        let areasOfNeedImages = [];
        // Iterate over each area of need and convert to image
        for (let i = 0; i < areasOfNeed.length; i++) {
            areasOfNeedImages.push(this.convertAreaOfNeedToImage(areasOfNeed[i])); // Convert each area of need to an image
        }
        return areasOfNeedImages; // Return the list of images
    }

    // Method to convert a single area of need to its respective image
    convertAreaOfNeedToImage(areaOfNeed) {
        switch (areaOfNeed) { // Check the area of need and return the corresponding image
            case 'Cane e Gatto':
                return PET_IMG;
            case 'Casa':
                return HOUSE_IMG;
            case 'Famiglia':
                return FAMILY_IMG;
            case 'Infortuni':
                return INJURIES_IMG;
            case 'Mobilità':
                return MOTORCYCLE_IMG;
            case 'Salute':
                return HEALTH_IMG;
            case 'Veicoli':
                return CAR_IMG;
            case 'Viaggio':
                return TRAVEL_IMG;
            case 'Previdenza integrativa':
                return PREVIDENCE_IMG;
            case 'Vita':
                return VITA_IMG;
                case 'Previdenza integrativa': return VITA_IMG;
    case 'Persona':return VITA_IMG;
    case 'Casa e Famiglia': return FAMILY_IMG;
            default:
                console.log('There was an error retrieving the image for the Area Of Need'); // Log an error if the area of need does not match any case
                return null; // Return null if no match is found
        }
    }
    
    // Method to show a toast notification
    showToast(title, message, variant) { 
        const event = new ShowToastEvent({ 
            title: title, // Title of the toast
            message: message, // Message of the toast
            variant: variant // Variant of the toast (e.g., success, error)
        }); 
        this.dispatchEvent(event); // Dispatch the toast event
    } 

    // Methos to convert base64 to blob
    convertToBlob(base64Data) {
        try {
            const base64Pdf = base64Data;
            const byteCharacters = atob(base64Pdf);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'application/pdf' });
            return URL.createObjectURL(blob);
        } catch(e) {
            console.log('Errore PDF: ' + e.message);
            return null;
        }
    }


    refreshContainer(refreshPromise) {
        console.log("refreshing of accordionContainerResponsive");
        this.fetchQuotes();
        return refreshPromise.then((status) => {
            if (status === REFRESH_COMPLETE) {
                console.log("refresh of accordionContainerResponsive Done!");
            } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
                console.warn("refresh of accordionContainerResponsive Done, with issues refreshing some components");
            } else if (status === REFRESH_ERROR) {
                console.error("refresh of accordionContainerResponsive Major error with refresh.");
            }
        });
    }


}