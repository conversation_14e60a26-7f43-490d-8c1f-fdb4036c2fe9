import { LightningElement, api, track } from 'lwc'; // Import necessary modules from LWC
import { ShowToastEvent } from 'lightning/platformShowToastEvent'; // Import toast event for displaying notifications

import getQuotesInfo from '@salesforce/apex/AccordionController.getQuotesInfo'; // Import Apex method for fetching quotes

import areas_of_need_images from '@salesforce/resourceUrl/areas_of_need'; // Import static resource for area of need images

// Constants for image URLs
const PET_IMG = areas_of_need_images + '/on/pet.png';
const HOUSE_IMG = areas_of_need_images + '/on/casa.png';
const FAMILY_IMG = areas_of_need_images + '/on/famiglia.png';
const INJURIES_IMG = areas_of_need_images + '/on/infortuni.png';
const MOTORCYCLE_IMG = areas_of_need_images + '/on/mobilita.png';
const HEALTH_IMG = areas_of_need_images + '/on/salute.png';
const CAR_IMG = areas_of_need_images + '/on/veicoli.png';
const TRAVEL_IMG = areas_of_need_images + '/on/viaggi.png';
const VITA_IMG = areas_of_need_images + '/on/<EMAIL>';

export default class AccordionContainer extends LightningElement {
    @api recordId; // API property to hold the record ID
    
    @track quotes = []; // Trackable property to hold the list of quotes
    @track firstQuote = []; // Trackable property to hold the first quote
    @track showAll; // Trackable property to manage the display of all quotes
    @track showAllText; // Trackable property to manage the display text
    @track hasQuote = false; // Trackable property to check if there are any quotes

    // Method to handle the show/hide functionality of quotes
    handleClick() {
        this.showAll = !this.showAll; // Toggle the showAll property
        this.showAllText = (this.showAll === false) ? 'Mostra tutti' : 'Nascondi'; // Update the display text based on the toggle state
    }

    // Lifecycle hook that gets called when the component is inserted into the DOM
    connectedCallback() {
        this.fetchQuotes(); // Fetch the quotes when the component is initialized
    }

    // Method to fetch quotes from the server
    fetchQuotes() {
        getQuotesInfo({ recordId: this.recordId }) // Call Apex method with the current record ID
        .then(data => { // Handle successful response
            console.log('data '+JSON.stringify(data));
            // Process each quote received from the server
            for (let i = 0; i < data.length; i++) { 
                let _opportunityCoverages = [];
                // Process each coverage within the quote
                for (let j = 0; j < data[i].opportunityCoverages.length; j++) {
                    let opportunityCoverage = {
                        isFirst: j === 0 ? true : false, // Check if it's the first coverage
                        areaOfNeedImage: this.convertAreaOfNeedToImage(data[i].opportunityCoverages[j].areaOfNeed), // Get the image for the area of need
                        amount: data[i].opportunityCoverages[j].amount, // Coverage amount
                        assetItems: data[i].opportunityCoverages[j].assets, // List of assets
                        descriptionItems: data[i].opportunityCoverages[j].description, // Description items
                        conventions: data[i].opportunityCoverages[j].conventions, // List of conventions
                        fullName: data[i].opportunityCoverages[j].fullName, // Full name
                        splitting: data[i].opportunityCoverages[j].splitting, // Splitting details
                        stage: data[i].opportunityCoverages[j].stage // Stage of the coverage
                    }

                    _opportunityCoverages.push(opportunityCoverage); // Add coverage to the list
                }
                    
                let quote = {
                    name: data[i].name, // Quote name
                    recordId: data[i].recordId, // Quote record ID
                    isFirst: i === 0 ? true : false, // Check if it's the first quote
                    areasOfNeedImages: this.convertAreasOfNeedToImages(data[i].areasOfNeed), // Get the images for areas of need
                    status: data[i].status, // Quote status
                    totalAmount: data[i].totalAmount, // Total amount of the quote
                    creationDate: data[i].creationDate, // Creation date of the quote
                    expirationDate: data[i].expirationDate, // Expiration date of the quote
                    unicaLink: data[i].unicaLink, // Unica link for the quote
                    opportunityCoverages: _opportunityCoverages, // List of coverages in the quote
                    domainType: data[i].domainType
                }

                this.hasQuote = true; // Set hasQuote to true as we have quotes now
                this.quotes.push(quote); // Add the quote to the list of quotes

                if (i === 0) // Check if it's the first quote
                    this.firstQuote.push(quote); // Add the first quote to the firstQuote list
            }
            this.showAll = false; // Set showAll to false initially
            this.showAllText = 'Mostra tutti'; // Set the initial showAllText

        }).catch(error => { // Handle any errors during the fetch operation
            console.log(error.body.message); // Log the error message
            this.showToast('An error occurred, please contact System Admin', error.body.message, 'error'); // Show a toast with the error message
        });
    }

    // Method to convert an array of areas of need to their respective images
    convertAreasOfNeedToImages(areasOfNeed) {
        let areasOfNeedImages = [];
        // Iterate over each area of need and convert to image
        for (let i = 0; i < areasOfNeed.length; i++) {
            areasOfNeedImages.push(this.convertAreaOfNeedToImage(areasOfNeed[i])); // Convert each area of need to an image
        }
        return areasOfNeedImages; // Return the list of images
    }

    // Method to convert a single area of need to its respective image
    convertAreaOfNeedToImage(areaOfNeed) {
        switch (areaOfNeed) { // Check the area of need and return the corresponding image
            case 'Cane e Gatto':
                return PET_IMG;
            case 'Casa':
                return HOUSE_IMG;
            case 'Famiglia':
                return FAMILY_IMG;
            case 'Infortuni':
                return INJURIES_IMG;
            case 'Mobilita':
                return MOTORCYCLE_IMG;
            case 'Salute':
                return HEALTH_IMG;
            case 'Veicoli':
                return CAR_IMG;
            case 'Viaggio':
                return TRAVEL_IMG;
            case 'Vita':
                return VITA_IMG;
            case 'Previdenza integrativa':
                return VITA_IMG;
            case 'Persona':
                return VITA_IMG;
            case 'Casa e Famiglia':
                return FAMILY_IMG;
            default:
                console.log('There was an error retrieving the image for the Area Of Need'); // Log an error if the area of need does not match any case
                return null; // Return null if no match is found
        }
    }

    // Method to show a toast notification
    showToast(title, message, variant) { 
        const event = new ShowToastEvent({ 
            title: title, // Title of the toast
            message: message, // Message of the toast
            variant: variant // Variant of the toast (e.g., success, error)
        }); 
        this.dispatchEvent(event); // Dispatch the toast event
    } 
}