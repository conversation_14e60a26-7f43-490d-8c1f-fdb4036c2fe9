import { LightningElement, api } from 'lwc';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { FlowNavigationFinishEvent } from 'lightning/flowSupport';

export default class CC_OpenToastEventIntoFlow extends LightningElement {
    @api toastTitle;
    @api toastMessage;
    @api toastVariant;
    @api autoCloseDelay = 100;

    connectedCallback() {
        this.dispatchEvent(
            new ShowToastEvent({
                title: this.toastTitle,
                message: this.toastMessage,
                variant: this.toastVariant
            })
        );

        setTimeout(() => {
            this.dispatchEvent(new FlowNavigationFinishEvent());
        }, this.autoCloseDelay);
    }
}
