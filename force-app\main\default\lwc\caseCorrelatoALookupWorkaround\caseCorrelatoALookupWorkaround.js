import { LightningElement, api, wire, track } from 'lwc';
import { getRecord, getFieldValue, updateRecord } from 'lightning/uiRecordApi';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import { refreshApex } from '@salesforce/apex';
import { NavigationMixin } from 'lightning/navigation';

// Import custom labels
import labelInsurancePolicy from '@salesforce/label/c.InsurancePolicy';
import labelAsset from '@salesforce/label/c.Asset';
import labelOpportunity from '@salesforce/label/c.Opportunity';
import labelSuccess from '@salesforce/label/c.Success';
import labelError from '@salesforce/label/c.Error';
import labelRelatedRecordsUpdated from '@salesforce/label/c.RelatedRecordsUpdated';
import labelNoRecordId from '@salesforce/label/c.NoRecordId';
import labelErrorUpdatingRecord from '@salesforce/label/c.ErrorUpdatingRecord';
import labelNoAccountWarning from '@salesforce/label/c.noAccountWarning';
import labelCancel from '@salesforce/label/c.Cancel';
import labelSave from '@salesforce/label/c.Save';
import labelSearch from '@salesforce/label/c.Search';

// Field references for Case
import OPPORTUNITY_FIELD from '@salesforce/schema/Case.Opportunity__c';
import OPPORTUNITY_NAME_FIELD from '@salesforce/schema/Case.Opportunity__r.Name';
import ACCOUNT_ID_FIELD from '@salesforce/schema/Case.AccountId';

// Insurance Policy fields
import INSURANCE_POLICY_FIELD from '@salesforce/schema/Case.Insurance_Policy__c';
import INSURANCE_POLICY_NAME_FIELD from '@salesforce/schema/Case.Insurance_Policy__r.Name';

// Asset fields
import ASSET_FIELD from '@salesforce/schema/Case.AssetId';
import ASSET_NAME_FIELD from '@salesforce/schema/Case.Asset.Name';

//import BUSINESS_KEY_FIELD from '@salesforce/schema/Case.BusinessKey__c';

import getFilteredPolicyIds from '@salesforce/apex/InsurancePolicyController.getFilteredPolicyIds';
import hasEditPermission from '@salesforce/apex/NoteController.hasPermissionSet';

import { getFocusedTabInfo, openSubtab } from 'lightning/platformWorkspaceApi';
import { registerRefreshContainer, REFRESH_ERROR, REFRESH_COMPLETE, REFRESH_COMPLETE_WITH_ERRORS } from 'lightning/refresh';
export default class CaseCorrelatoALookupWorkaround extends NavigationMixin(LightningElement) {
    @api recordId; // Current Case record Id
    
    // Configurable field properties
    @api incidentFieldApiName = 'Incident__c'; // Default, can be overridden in page builder
    @api incidentLabel = 'Incident'; // Default label, can be overridden
    
    // Custom labels
    labels = {
        insurancePolicy: labelInsurancePolicy,
        asset: labelAsset,
        opportunity: labelOpportunity,
        success: labelSuccess,
        error: labelError,
        relatedRecordsUpdated: labelRelatedRecordsUpdated,
        noRecordId: labelNoRecordId,
        errorUpdatingRecord: labelErrorUpdatingRecord,
        noAccountWarning: labelNoAccountWarning,
        cancel: labelCancel,
        save: labelSave,
        Search : labelSearch
    };
    
    // Section collapsible state
    @track isExpanded = true;
    
    // Record data
    @track opportunityId;
    @track opportunityName;
    @track accountId;
    @track insurancePolicyId;
    @track insurancePolicyName;
    @track assetId;
    @track assetName;
    @track incidentValue;
    //@track businessKey;
    
    // UI state
    @track isEditing = false;
    @track isSaving = false;
    
    // Filters
    @track opportunityFilter = {};
    @track insurancePolicyFilter = {};
    @track assetFilter = {};
    
    // Selected values during editing
    @track selectedOpportunityId;
    @track selectedInsurancePolicyId;
    @track selectedAssetId;
    @track selectedIncidentValue;
    
    // Flow inputs
    connectedCallback(){
        this.refreshContainerID = registerRefreshContainer(this, this.refreshContainer);
    }
    // Wire results for refreshing
    @track wiredCaseResult;

    insurancePolicyInfo = {
        primaryField: 'Name',
        additionalFields: ['AreasOfNeed__c'],
    };

    showEditButton = false;

    @wire(hasEditPermission)
    wiredPermission({ error, data }) {
        if (data) {
            this.showEditButton = data;
        } else {
            this.showEditButton = false;
        }
    }

    isFlowModalOpened = false;
    
    // Computed property for the flow URL
    get flowUrl() {
        return `/flow/FEIQuickActionByCase?recordId=${this.recordId}&FEIID=SXCC.INTERROGAZIONE`;
    }
    
    // Get Case record data with dynamic field reference
    @wire(getRecord, { 
        recordId: '$recordId',
        fields: '$allFields'
    })
    wiredCase(result) {
        this.wiredCaseResult = result;
        
        if (result.data) {
            this.opportunityId = getFieldValue(result.data, OPPORTUNITY_FIELD);
            this.opportunityName = getFieldValue(result.data, OPPORTUNITY_NAME_FIELD);
            this.accountId = getFieldValue(result.data, ACCOUNT_ID_FIELD);
            this.insurancePolicyId = getFieldValue(result.data, INSURANCE_POLICY_FIELD);
            this.insurancePolicyName = getFieldValue(result.data, INSURANCE_POLICY_NAME_FIELD);
            this.assetId = getFieldValue(result.data, ASSET_FIELD);
            this.assetName = getFieldValue(result.data, ASSET_NAME_FIELD);
            //this.businessKey = getFieldValue(result.data, BUSINESS_KEY_FIELD);
            
            // Get the incident field value dynamically
            const incidentField = { fieldApiName: this.incidentFieldApiName, objectApiName: 'Case' };
            this.incidentValue = getFieldValue(result.data, incidentField);
            
            // Set up filters for lookups with matching criteria
            this.setupFilters();
            this.getFilteredPolicies();
        } else if (result.error) {
            this.showToast(this.labels.error, this.labels.errorUpdatingRecord + result.error.body.message, 'error');
        }
    }
    
    // Dynamically construct fields array based on configurable field API name
    get allFields() {
        return [
            OPPORTUNITY_FIELD,
            OPPORTUNITY_NAME_FIELD,
            ACCOUNT_ID_FIELD,
            INSURANCE_POLICY_FIELD,
            INSURANCE_POLICY_NAME_FIELD,
            ASSET_FIELD,
            ASSET_NAME_FIELD,
            //BUSINESS_KEY_FIELD,
            { fieldApiName: this.incidentFieldApiName, objectApiName: 'Case' }
        ];
    }

    setupFilters() {
        // Filter for opportunities with matching accountId
        if (this.accountId) {
            this.opportunityFilter = {
                criteria: [
                    {
                        fieldPath: 'AccountId',
                        operator: 'eq',
                        value: this.accountId
                    }
                ]
            };
            
            // Filter for assets with matching accountId
            this.assetFilter = {
                criteria: [
                    {
                        fieldPath: 'AccountId',
                        operator: 'eq',
                        value: this.accountId
                    }
                ]
            };
        } else {
            // No filter if no account
            this.opportunityFilter = {};
            this.assetFilter = {};
        }
    }

    getFilteredPolicies() {
        getFilteredPolicyIds({ accountId: this.accountId })
        .then(ids => {
            this.insurancePolicyFilter = {
                criteria: [
                    {
                        fieldPath: 'Id',
                        operator: 'in',
                        value: ids
                    }
                ]
            };
        })
        .catch(error => {
            console.error('Errore nel recupero degli ID delle polizze:', error);
        });
    }

    // Computed properties
    get showAccountWarning() {
        return !this.accountId && this.isEditing;
    }
    
    get sectionIcon() {
        // When expanded (displayed): down arrow (utility:chevrondown)
        // When collapsed (not displayed): right arrow (utility:chevronright)
        return this.isExpanded ? 'utility:chevrondown' : 'utility:chevronright';
    }

    // UI Handlers
    toggleSection() {
        this.isExpanded = !this.isExpanded;
    }
    
    handleEdit() {
        this.isEditing = true;
        // Initialize selected values with current values
        this.selectedOpportunityId = this.opportunityId;
        this.selectedInsurancePolicyId = this.insurancePolicyId;
        this.selectedAssetId = this.assetId;
        this.selectedIncidentValue = this.incidentValue;
    }

    handleCancel() {
        this.isEditing = false;
        // Reset selected values
        this.selectedOpportunityId = null;
        this.selectedInsurancePolicyId = null;
        this.selectedAssetId = null;
        this.selectedIncidentValue = null;
    }

    // Picker selection handlers
    handleOpportunitySelected(event) {
        this.selectedOpportunityId = event.detail.recordId;
    }
    
    handleInsurancePolicySelected(event) {
        this.selectedInsurancePolicyId = event.detail.recordId;
    }
    
    handleAssetSelected(event) {
        this.selectedAssetId = event.detail.recordId;
    }
    
    // Handle incident text input change
    handleIncidentValueChange(event) {
        this.selectedIncidentValue = event.detail.value;
    }
    
    // Save handler
    handleSave() {
        if (!this.recordId) {
            this.showToast(this.labels.error, this.labels.noRecordId, 'error');
            return;
        }

        this.isSaving = true;
        
        const fields = {};
        fields['Id'] = this.recordId;
        fields[OPPORTUNITY_FIELD.fieldApiName] = this.selectedOpportunityId || null;
        fields[INSURANCE_POLICY_FIELD.fieldApiName] = this.selectedInsurancePolicyId || null;
        fields[ASSET_FIELD.fieldApiName] = this.selectedAssetId || null;
        
        // Add the dynamic incident field
        fields[this.incidentFieldApiName] = this.selectedIncidentValue || null;
        
        const recordInput = { fields };
        
        updateRecord(recordInput)
            .then(() => {
                this.showToast(this.labels.success, this.labels.relatedRecordsUpdated, 'success');
                this.isEditing = false;
                this.isSaving = false;
                return refreshApex(this.wiredCaseResult);
            })
            .catch(error => {
                this.isSaving = false;
                console.error('Update error:', error);
                const errorMessage = error.body && error.body.message ? 
                    error.body.message : 
                    'An unknown error occurred';
                this.showToast(this.labels.error, this.labels.errorUpdatingRecord + errorMessage, 'error');
            });
    }
    
    // Navigation handlers
    handleOpportunityClick(event) {
        event.preventDefault();
        if (this.opportunityId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: this.opportunityId,
                    objectApiName: 'Opportunity',
                    actionName: 'view'
                }
            });
        }
    }
    
    handleInsurancePolicyClick(event) {
        event.preventDefault();
        if (this.insurancePolicyId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: this.insurancePolicyId,
                    objectApiName: 'InsurancePolicy',
                    actionName: 'view'
                }
            });
        }
    }
    
    handleAssetClick(event) {
        event.preventDefault();
        if (this.assetId) {
            this[NavigationMixin.Navigate]({
                type: 'standard__recordPage',
                attributes: {
                    recordId: this.assetId,
                    objectApiName: 'Asset',
                    actionName: 'view'
                }
            });
        }
    }
    
    showToast(title, message, variant) {
        this.dispatchEvent(
            new ShowToastEvent({
                title,
                message,
                variant
            })
        );
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }
    
    handleFlowClick(event) {
        let feiId = 'SXCC.INTERROGAZIONE';
        const recordId = this.recordId;

        this.flowInputs = [
            { name: 'recordId', type: 'String', value: recordId },
            { name: 'FEIID', type: 'String', value: feiId },
            { name: 'society', type: 'String', value: 'SOC_1' }
        ];
        this.toggleFlowModal();
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR'){
            console.log('flow finished/error: ' + event.detail.status);
            console.log()
            this.toggleFlowModal();
        }
    }

    refreshContainer(refreshPromise) {
        console.log("refreshing of caseCorrelatoALookupWorkaround");
        //location.reload(); 
        console.log('pageRef: ' + JSON.stringify(this.currentPageRef));
        console.log('recordID: ' + this.recordId);

        // Ricarica i dati del wire
        const refreshPromiseChain = refreshApex(this.wiredCaseResult);
        

    return refreshPromise.then((status) => {
        if (status === REFRESH_COMPLETE) {
            console.log("refresh of caseCorrelatoALookupWorkaround Done!");
        } else if (status === REFRESH_COMPLETE_WITH_ERRORS) {
            console.warn("refresh of caseCorrelatoALookupWorkaround Done, with issues refreshing some components");
        } else if (status === REFRESH_ERROR) {
            console.error("refresh of caseCorrelatoALookupWorkaround Major error with refresh.");
        }
    });
    }
}