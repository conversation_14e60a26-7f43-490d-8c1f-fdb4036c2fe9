import { LightningElement, api, track } from "lwc";

export default class CcDisambiguationCore extends LightningElement {
  static FIELD_EFFECTIVE_DATE = {
    name: "effectiveDate",
    value: "",
    label: "Data effetto",
    type: "date",
    path: "Ambito.DataDecorrenza",
    format: (value) => {
      //Target DD/MM/YYYY
      const options = { year: "numeric", month: "2-digit", day: "2-digit" };
      return value ? new Date(value).toLocaleDateString("it-IT", options) : "";
    },
  };

  static FIELD_ADDRESS = {
    name: "address",
    value: "",
    label: "Indirizzo di residenza",
    type: "text",
    path: "Ambito.Proprietario.Residenza",
  };

  static FIELD_BIRTHDATE = {
    name: "birthdate",
    value: "",
    label: "Data di nascita",
    type: "date",
    path: "Ambito.Proprietario.DataDiNascita",
    format: (value) => {
      //Target DD/MM/YYYY
      const options = { year: "numeric", month: "2-digit", day: "2-digit" };
      return value ? new Date(value).toLocaleDateString("it-IT", options) : "";
    },
  };

  @api entityDomains = {};
  @api showSteps = {};

  @track setData = {};

  get formData() {
    return {
      effectiveDate: CcDisambiguationCore.FIELD_EFFECTIVE_DATE,
      address: CcDisambiguationCore.FIELD_ADDRESS,
    };
  }

  get mergeData() {
    return Object.entries(this.formData).reduce((acc, [key, field]) => {
      acc[key] = {
        ...field,
        value: this.setData[field.name] || field.value,
      };
      return acc;
    }, {});
  }

  handleChildChange(event) {
    const { field, value, state, path } = event.detail;
    console.log("** Child field change:", { field, value, state, path });

    this.setData = {
      ...this.setData,
      [field]: state || value,
    };

    this.dispatchEvent(
      new CustomEvent("fieldchange", {
        detail: {
          field,
          value: this.formData[field].format
            ? this.formData[field].format(value)
            : value,
          path,
        },
      })
    );

    console.log("Updated mergeData:", this.mergeData);
  }

  handleChange(event) {
    const {
      name,
      value,
      type,
      dataset: { path },
    } = event.target;
    console.log("Field change:", { name, value, path });

    const valueCandidate = type === "checkbox" ? event.target.checked : value;

    this.setData = {
      ...this.setData,
      [name]: valueCandidate,
    };

    this.dispatchEvent(
      new CustomEvent("fieldchange", {
        detail: {
          field: name,
          value: this.formData[name].format
            ? this.formData[name].format(valueCandidate)
            : valueCandidate,
          path: this.formData[name].path,
        },
      })
    );

    console.log("Updated mergeData:", this.mergeData);
  }
}
