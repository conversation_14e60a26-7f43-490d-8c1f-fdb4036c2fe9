import CcDisambiguationCoreChild from "c/ccDisambiguationCoreChild";

export default class CcDisambiguationEffectiveDate extends CcDisambiguationCoreChild {
  get overrideData() {
    return {
      ...this.formData,
      effectiveDate: {
        ...this.formData.effectiveDate,
        validation: (event) => this.validateDate(event),
      },
    };
  }

  validateDate(event) {
    const value = event.target.value;
    let message = "";

    if (!value) {
      message = "Data Decorrenza non può essere vuota.";
    } else {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const inputDate = new Date(value);
      inputDate.setHours(0, 0, 0, 0);

      if (inputDate < today) {
        message = "La data effetto non può essere precedente alla data odierna";
      }

      const maxDate = new Date(today)
      maxDate.setDate(maxDate.getDate() + 60);

      if (inputDate > maxDate) {
        message = 'La data effetto non può superare i 60 giorni dalla data odierna';
      }
    }

    event.target.setCustomValidity(message);
    event.target.reportValidity();
    this.handleChange(event);
  }
}
