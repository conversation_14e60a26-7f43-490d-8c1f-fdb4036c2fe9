import CcDisambiguationCore from "c/ccDisambiguationCore";

export default class CcDisambiguationVehicle extends CcDisambiguationCore {
  get formData() {
    return {
      tipology: {
        name: "tipology",
        value: "",
        label: "Tipologia di veicolo",
        options: this.entityDomains?._tipoVeicolo || [],
        path: "Ambito.Bene.Auto.TipoVeicolo",
      },
      plate: {
        name: "plate",
        value: "",
        label: "Targa",
        path: "Ambito.Bene.Auto.Targa",
        validation: (event) => {
          const value = event.target.value;
          const regex = /^[A-Z]{2}[0-9]{3}[A-Z]{2}$/;

          if (!regex.test(value)) {
            // Handle invalid plate format
            event.target.setCustomValidity(
              "Formato targa non valido. Deve essere di tipo XX123XX."
            );
            event.target.reportValidity();
          } else {
            event.target.setCustomValidity(""); // Clear custom validity if valid
            event.target.reportValidity();
            this.handlePlateChange(event);
          }
        },
      },
      notPlate: {
        name: "notPlate",
        value: false,
        label: "Non ho la targa",
        type: "checkbox",
        path: "Ambito.Bene.Auto.SenzaTarga",
      },
      birthdate: CcDisambiguationCore.FIELD_BIRTHDATE,
      transferClassMerit: {
        name: "transferClassMerit",
        value: false,
        label: "Voglio trasferire la classe di merito da un altro veicolo",
        type: "checkbox",
        path: "Ambito.Bene.Auto.RecuperoClasseAltroVeicolo",
      },
      effectiveDate: CcDisambiguationCore.FIELD_EFFECTIVE_DATE,
      address: CcDisambiguationCore.FIELD_ADDRESS,
    };
  }

  // When a valid plate is entered, uncheck "Non ho la targa" and propagate both changes
  handlePlateChange(event) {
    const { value } = event.target;

    // Update plate value via base logic
    super.handleChange(event);

    // If user enters a plate, ensure notPlate is false and notify parent
    if (this.setData.notPlate) {
      this.setData = {
        ...this.setData,
        notPlate: false,
      };

      this.dispatchEvent(
        new CustomEvent("fieldchange", {
          detail: {
            field: "notPlate",
            value: false,
            path: this.formData.notPlate.path,
          },
        })
      );
    }

    // No further action needed; disabling is bound in template to notPlate
  }

  // When toggling "Non ho la targa", clear plate when checked and re-enable when unchecked
  handleNotPlateChange(event) {
    const checked = event.target.checked;

    // Update notPlate via base logic
    super.handleChange(event);

    if (checked) {
      // Clear plate value in state
      this.setData = {
        ...this.setData,
        plate: "",
        transferClassMerit: "",
      };

      const customEvent = {
        target: {
          name: "transferClassMerit",
          value: "",
          type: "checkbox",
          dataset: { path: this.formData.transferClassMerit.path },
        },
      };
      super.handleChange(customEvent);

      // Clear input UI validity and value if rendered
      const plateInput = this.template.querySelector('lightning-input[name="plate"]');
      if (plateInput) {
        plateInput.value = "";
        plateInput.setCustomValidity("");
        plateInput.reportValidity();
      }

      // Notify parent about cleared plate value
      this.dispatchEvent(
        new CustomEvent("fieldchange", {
          detail: {
            field: "plate",
            value: "",
            path: this.formData.plate.path,
          },
        })
      );
    }
  }

  get showTransferClassMerit() {
    return !this.mergeData.notPlate.value;
  }
}
