<template>
  <article class="slds-card slds-card_narrow">
    <div class="slds-card__body_inner">
      <article class="slds-box slds-box_x-small">
        <div class="slds-page-header slds-page-header_record-home">
          <div class="slds-grid slds-grid_vertical-align-center slds-p-around_xxx-small slds-theme_shade">
            <div class="slds-media">
              <div class="slds-media__figure">
                <lightning-icon icon-name="standard:opportunity" size="small" class="slds-m-right_small">
                </lightning-icon>
              </div>

              <div class="slds-media__body">
                <h2 class="slds-text-heading_small" title="Preventivi">
                  {headerTitle}
                </h2>
              </div>
            </div>

            <div class="slds-col_bump-left">
              <template if:false={hasItems}>
                <lightning-button label="Nuovo" variant="neutral" onclick={handleCreateNew}>
                </lightning-button>
              </template>
            </div>
          </div>
        </div>

        <template if:true={loading}>
          <div class="slds-p-around_medium">
            <lightning-spinner alternative-text="Caricamento..." size="small"></lightning-spinner>
          </div>
        </template>

        <template if:true={hasItems}>
          <div class="slds-p-around_medium">
            <template for:each={visibleItems} for:item="it">
              <article key={it.id} class="slds-m-bottom_x-small">

                <div class="slds-grid slds-grid_align-spread slds-m-bottom_xxx-small">
                  <div>
                    <a class="link" style="font-size: 14px;" href="javascript:void(0);" data-quote-id={it.quoteId}
                      onclick={handleOpenQuote}>
                      ID Preventivo {it.quoteNumber}
                    </a>
                  </div>
                  <div class="slds-p-right_xx-large">
                    <lightning-button-menu alternative-text="Azioni" icon-name="utility:down" menu-alignment="right"
                      onselect={handleMenuSelect} data-quote-id={it.quoteId}>
                      <lightning-menu-item value="pdf" label="Visualizza PDF"
                        disabled={it.noDocument}></lightning-menu-item>
                      <lightning-menu-item value="add_to_cart" label="Aggiungi al carrello"></lightning-menu-item>
                    </lightning-button-menu>
                  </div>
                </div>

                <div class="slds-p-bottom_small" style="font-size: 16px;">
                  <div class="slds-grid slds-gutters_xxx-small">
                    <div class="slds-col slds-size_1-of-2 slds-text-title">Data Scadenza:</div>
                    <div class="slds-col slds-size_1-of-2">{it.expiration}</div>
                  </div>
                  <div class="slds-grid slds-gutters_xxx-small">
                    <div class="slds-col slds-size_1-of-2 slds-text-title">Prodotto:</div>
                    <div class="slds-col slds-size_1-of-2">{it.product}</div>
                  </div>
                  <div class="slds-grid slds-gutters_xxx-small">
                    <div class="slds-col slds-size_1-of-2 slds-text-title">Ambito:</div>
                    <div class="slds-col slds-size_1-of-2">{it.scope}</div>
                  </div>
                  <div class="slds-grid slds-gutters_xxx-small">
                    <div class="slds-col slds-size_1-of-2 slds-text-title">Premio:</div>
                    <div class="slds-col slds-size_1-of-2">{it.amount}</div>
                  </div>
                  <div class="slds-grid slds-gutters_xxx-small">
                    <div class="slds-col slds-size_1-of-2 slds-text-title">Step Digitale:</div>
                    <div class="slds-col slds-size_1-of-2">{it.journeyStep}</div>
                  </div>
                </div>
              </article>
            </template>
          </div>
        </template>

        <template if:false={loading}>
          <template if:false={hasItems}>
            <div class="slds-p-around_small slds-text-color_weak">
              Attualmente non è presente nessun preventivo, crea un nuovo preventivo.
            </div>
          </template>
        </template>

        <template if:true={hasItems}>
          <div slot="footer" class="slds-text-align_center slds-p-vertical_xxx-small">
            <a class="link" href="javascript:void(0);" onclick={handleViewAll}>Visualizza tutto</a>
          </div>
        </template>
      </article>
    </div>
  </article>

  <c-pdf-view-modal url={documentUrl} show-modal={showPreview} onclose={closePDFPreview}></c-pdf-view-modal>
  <template if:true={showDisambiguation}>
    <section role="dialog" tabindex="-1" aria-modal="true" aria-labelledby="disambModalHeading"
      class="slds-modal slds-fade-in-open">
      <div class="slds-modal__container">
        <header class="slds-modal__header">
          <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi"
            onclick={closeDisambiguation}>
            <lightning-icon icon-name="utility:close" alternative-text="Chiudi" size="small"></lightning-icon>
            <span class="slds-assistive-text">Chiudi</span>
          </button>
          <h2 id="disambModalHeading" class="slds-text-heading_medium">Nuovo preventivo</h2>
        </header>

        <div class="slds-modal__content slds-p-around_medium" id="disambModalContent" tabindex="0">
          <c-cc-disambiguation-root case-id={recordId}></c-cc-disambiguation-root>
        </div>

        <footer class="slds-modal__footer">
          <lightning-button label="Chiudi" onclick={closeDisambiguation}></lightning-button>
        </footer>
      </div>
    </section>
    <div class="slds-backdrop slds-backdrop_open"></div>
  </template>
</template>