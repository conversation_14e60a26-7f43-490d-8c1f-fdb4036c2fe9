import { getFocusedTabInfo, openSubtab } from "lightning/platformWorkspaceApi";
import { LightningElement, api, track } from 'lwc';


export default class CcEstimateWidget extends LightningElement {
  @api recordId;
  // @api customerData;         
  @track items = [];
  loading = true;
  @track showPreview
  documentUrl = '';
  showDisambiguation = false;

  _customerData;

  @api
  get customerData() {
    return this._customerData;
  }
  set customerData(value) {
    this._customerData = value;
    if (value) {
      Promise.resolve().then(() => this.loadData());
    }
  }


  connectedCallback() {
    console.log('ccEstimateWidget connectedCallback ', this.customerData);
    if (this.customerData) {
      this.loadData();
    }
  }

  get hasItems() {
    return (this.items && this.items.length > 0);
  }

  get visibleItems() {
    return Array.isArray(this.items) ? this.items.slice(0, 2) : [];
  }

  get headerTitle() {
    return this.items.length > 0
      ? `Preventivi (${this.items.length})`
      : 'Preventivi';
  }


  async loadData() {
    this.loading = true;
    try {
      // const data = await getCustomerByCaseId({ caseId: this.recordId });
      const data = this._customerData;

      const allQuotes = [
        ...(data?.quote ?? []),
        ...((data?.opportunitiesData ?? []).flatMap(od => od.quotes ?? []))
      ];
      const quoteById = new Map(allQuotes.map(q => [q.Id, q]));

      const allOpps = [
        ...(data?.opportunity ?? []),
        ...((data?.opportunitiesData ?? []).map(od => od.opportunity).filter(Boolean))
      ];
      const oppById = new Map(allOpps.map(o => [o.Id, o]));

      const coverages = data?.opportunityCoverages ?? [];

      this.items = coverages
        .map(cov => {
          const q = cov?.Quote__c ? quoteById.get(cov.Quote__c) : null;
          if (!q) return null;

          const opp = q.OpportunityId ? oppById.get(q.OpportunityId) : null;

          return {
            id: cov.Id,
            quoteId: q.Id,
            quoteNumber: q.QuoteNumber || q.Name,
            expiration: q.ExpirationDateFormula__c || q.ExpirationDate || '',
            product: 'Unica',
            scope: q.AreasOfNeed__c || '',
            amount: this.formatEUR(q.QuoteAmount__c),
            journeyStep: opp?.JourneyStep__c || '',
            // **NUOVI PER PDF**
            documentUrl: q.DocumentURL__c || '',
            noDocument: !(q.DocumentURL__c)
          };
        })
        .filter(Boolean);

    } catch (e) {
      console.error('ccEstimateWidget.loadData error', e);
      this.items = [];
    } finally {
      this.loading = false;
    }
  }

  formatEUR(value) {
    const n = Number(value);
    if (Number.isFinite(n)) {
      return new Intl.NumberFormat('it-IT', { style: 'currency', currency: 'EUR' }).format(n);
    }
    return '';
  }

  handleMenuSelect(event) {
    const action = event.detail.value;
    const quoteId = event.currentTarget.dataset.quoteId;
    if (!quoteId) return;

    if (action === 'pdf') {
      const row = this.items.find(r => r.quoteId === quoteId);
      console.log('row => ', JSON.stringify(row));
      const url = row && row.documentUrl;
      // if (url) {
      this.openPDFPreview(url);
      // }
      return;
    }

    if (action === 'add_to_cart') {
      this.dispatchEvent(new CustomEvent('addtocart', { detail: { quoteId } }));
    }
  }

  openPDFPreview() {
    this.showPreview = true;
  }

  closePDFPreview() {
    this.showPreview = false;
  }


  handleCreateNew() {
    this.openDisambiguation();
  }

  closeDisambiguation() {
    this.showDisambiguation = false;
  }

  async openDisambiguation() {
    try {
      this.loading = true;
      const focused = await getFocusedTabInfo();
      const parentId = focused.isSubtab ? focused.parentTabId : focused.tabId;

      const pageRef = {
        type: 'standard__component',
        attributes: { componentName: 'c__ccDisambiguationRootWrapper' },
        state: { c__recordId: this.recordId }
      };

      await openSubtab(parentId, {
        recordId: this.recordId,
        pageReference: pageRef,
        focus: true
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Errore apertura subtab:', e);
    } finally {
      this.loading = false;
    }
  }

  async openListViewSubTab() {
    try {
      this.loading = true;
      const focused = await getFocusedTabInfo();
      const parentId = focused?.tabId;
      const pageRef = {
        type: 'standard__component',
        attributes: { componentName: 'c__cC_quoteListDetail' },
        state: { c__recordId: this.recordId, c__customerData: JSON.stringify(this._customerData) }
      };

      await openSubtab(parentId, {
        recordId: this.recordId,
        pageReference: pageRef,
        focus: true
      });
    } catch (e) {
      // eslint-disable-next-line no-console
      console.error('Errore apertura subtab:', e);
    } finally {
      this.loading = false;
    }
  }


  async handleViewAll() {
    this.openListViewSubTab();
  }
}