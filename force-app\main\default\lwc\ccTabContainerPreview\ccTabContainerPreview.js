import getCustomerByCaseId from '@salesforce/apex/CustomerUtils.getCustomerByCaseId';
import { NavigationMixin } from 'lightning/navigation';
import { LightningElement, api, track } from 'lwc';

export default class CcTabContainerPreview extends NavigationMixin(LightningElement) {
  @api recordId;
  @track customerData

  get preventiviLabel() {
    return this.customerData?.quote ?
      `Preventivi (${this.customerData.quote.length})` :
      `Preventivi`
  }

  get accountId() {
    return this.customerData && this.customerData.account
      ? this.customerData.account.Id
      : null;
  }

  get fiscalCode() {
    return this.customerData && this.customerData.account
      ? this.customerData.account.ExternalId__c
      : null;
  }

  connectedCallback() {
    this.loadCustomerData();
  }

  async loadCustomerData() {
    try {
      const data = await getCustomerByCaseId({ caseId: this.recordId });
      this.customerData = data ? { ...data } : null;
    } catch (error) {
      console.error('Error loading customer data:', error);
    }
  }

  handleViewAllQuotes() {

    this.dispatchEvent(new CustomEvent('viewallquotes', { detail: { caseId: this.recordId } }));
  }

  handleAddToCart(event) {
    const { quoteId } = event.detail || {};
    this.dispatchEvent(new CustomEvent('addtocart', { detail: { quoteId } }));
  }

  handleViewPdf(event) {
    const { quoteId } = event.detail || {};
    this.dispatchEvent(new CustomEvent('viewpdf', { detail: { quoteId } }));
  }
}