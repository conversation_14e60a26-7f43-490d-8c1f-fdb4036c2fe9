import { LightningElement, api, wire, track} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';

import apexGetSinistriFromIntegrationProcedure from '@salesforce/apex/SinistriController.getSinistriFromIntegrationProcedure';


const sinistriColumn = [
    {
        type: "button",
        label: 'ID Sinistro',
        typeAttributes: {
            label: {fieldName: 'numeroSinistroExt'},
            name: 'visualizzaSinistro',
            title: 'Visualizza Sinistro',
            variant:'base'
        }
    },
    { label: 'N. Polizza/Posizione', fieldName: 'numeroPolizzaExt' },
    { label: 'Stato tecnico', fieldName: 'statoSinistroDescrizione' },
    { label: 'Data apertura', fieldName: 'dataSinistro', type: 'date-local'},
    { label: 'Anno Accadimento', fieldName: 'esercizioSinistro' },
    { label: 'Valore importo liquidato', fieldName: 'pagato', type: 'currency'}
];

export default class dataTablesinistri extends OmniscriptBaseMixin(LightningElement) {

    accountId;
    
    sinistriColumn = sinistriColumn;
    sinistri = [];

    isFlowModalOpened = false;
    isLWCModalOpened = false;

    params = {
        feiId: "feiId",
        numeroSinistro: "CF",
    }

    @wire(CurrentPageReference)
    currentPageReference;

    connectedCallback() { 
        console.log(JSON.stringify(this.currentPageReference.state));
        this.accountId = this.currentPageReference.state.c__recordId;
        console.log('connectedCallback');
        console.log('this.accountId: ' + this.accountId);
        this.getSinistriData();
    }

    getSinistriData(){
        apexGetSinistriFromIntegrationProcedure({accountId : this.accountId})
        .then((result) => {
            console.log('data: ' + JSON.stringify(result));
            let sinistriTest = result['Lista'];
            //this.sinistri = result['Lista'];
            let sinistriList = [];

            sinistriTest.forEach(sinistro => {

                //formattazione numSinistro
                if (sinistro.numeroSinistro.length < 6) {
                    for (let index = sinistro.numeroSinistro.length; index < 6; index++) {
                        sinistro.numeroSinistro = '0' + sinistro.numeroSinistro;
                    }
                }
                sinistro.numeroSinistroExt = sinistro.compagniaSinistro + '-' + sinistro.agenziaSinistro + '-' + sinistro.esercizioSinistro + '-' + sinistro.numeroSinistro;

                //formattazione numPolizza
                let ramo = sinistro.ramoPolizza;
                if (sinistro.ramoPolizza.length < 3) {
                    for (let index = sinistro.ramoPolizza.length; index < 3; index++) {
                        ramo = '0' + sinistro.ramoPolizza;
                    }
                }
                sinistro.numeroPolizzaExt = `N. ${ramo} / ${sinistro.numeroPolizza}`;

                sinistriList.push(sinistro);
            });
            console.log('sinistri post: ' + JSON.stringify(sinistriList));

            this.sinistri = sinistriList;
        })
        .catch((error) => {
            console.log('errore nel getSinistri: ' + JSON.stringify(error));
            //TODO: insert show toast
        });
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }

    toggleLWCModal(){
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR'){
            console.log('flow finished/error: ' + event.detail.status);
            console.log()
            this.toggleFlowModal();
        }
    }

    handleRowAction(event){
        const row = event.detail.row;
        let feiId = 'SXCC.INTERROGAZIONE';
        console.log(JSON.stringify('row data: ' + row));
        /*
        this.params = {
            feiId: feiId,
            feiRequestPayload: JSON.stringify({contractid: row.contId}),
        };
        console.log("params: "+JSON.stringify(this.params));
        */
        //this.toggleLWCModal();

        let numeroSinistroExt = row.numeroSinistroExt
        console.log('numeroSinistroExt: ' + numeroSinistroExt);
        this.flowInputs = [
            { name: 'FEIID', type: 'String', value: feiId},
            { name: 'numeroSinistro', type: 'String', value: numeroSinistroExt},
            { name: 'society', type: 'String', value: 'SOC_1'} //Inserito SOC_1 poichè usata solo per Unipolsai
        ];
        this.toggleFlowModal();
    }
}