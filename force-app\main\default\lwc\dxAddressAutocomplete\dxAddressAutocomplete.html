<template>
  <template if:true={showSubmitBlocker}>
    <c-dx-submit-blocker></c-dx-submit-blocker>
  </template>
  <div class="AddressComponentContainer">
    <div class="ContainerInserimentoCivico">
      <div class={inputPrincipaleContainerClass}>
        <c-dx-custom-text-styles content={labelAutocomplete} text-css={stileLabels}></c-dx-custom-text-styles>
        <div class="input-wrapper">
          <input type="text" class={inputPrincipaleClass} placeholder={placeholder} value={inputPrincipaleUtente}
            oninput={handleInputPrincipale} onfocus={handleInputFocus} onblur={handleInputBlur}
            data-id="inputPrincipale" required={isRequired} />

          <template if:true={shouldShowDropdown}>
            <div class="dropdown-container">
              <div if:true={isSearchingPredictions} class="loading-indicator">
                <div class="spinner"></div>
                <span>Ricerca in corso...</span>
              </div>
              <template if:false={isSearchingPredictions}>
                <template for:each={predictions} for:item="prediction">
                  <div key={prediction.placeId} class="dropdown-item" data-place-id={prediction.placeId}
                    onclick={handlePredictionSelect}>
                    <div class="prediction-main">{prediction.mainText}</div>
                    <div class="prediction-secondary">{prediction.secondaryText}</div>
                  </div>
                </template>
                <div class="powered-by-google">
                  Powered by
                  <img src="https://developers.google.com/static/maps/documentation/images/google_on_white.png"
                    alt="Google" />
                </div>
              </template>
            </div>
          </template>
        </div>
      </div>

      <template if:true={isIndirizzoState}>
        <div class="InputContainer" style="width: 30%">
          <c-dx-custom-text-styles content="Civico" text-css={stileLabels}></c-dx-custom-text-styles>
          <input type="text" class={inputSecondarioClass} placeholder="es: 21" value={inputSecondarioUtente}
            oninput={handleInputSecondario} data-id="inputSecondario" />
        </div>
        
       <template if:true={showErrorMessageInIndirizzoState}>
        <c-dx-error-message message={missingHouseNumberError}></c-dx-error-message>
      </template>
    </template>
    </div>

    <template if:true={showButtonConfermaInserimentoCivico}>
      <div onclick={handleConfermaCivico}>
        <c-dx-field-button field={buttonConfermaInserimentoCivico}></c-dx-field-button>
      </div>
    </template>

    <template if:false={isManualState}>
      <div class="ContainerCambioInserimento">
        <template if:true={nonTroviIlTuoIndirizzoLabel}>
          <c-dx-custom-text-styles content={nonTroviIlTuoIndirizzoLabel} text-css={stileLabels}></c-dx-custom-text-styles>
        </template>
        <div onclick={handlePassaggioIndirizzoManuale}>
          <c-dx-field-button field={nonTroviIlTuoIndirizzoCta}></c-dx-field-button>
        </div>
      </div>
    </template>

    <template if:true={showErrorMessageInAutocomplete}>
      <span class="testo-non-valido">
        <c-dx-field-px-icon class="icon-Attenzione-pieno bd-icona"></c-dx-field-px-icon>
        {errorMessage}
      </span>
    </template>

    <template if:true={isManualState}>
      <div class="ContainerInserimentoManuale">
        <c-dx-view view={viewInserimentoManuale}></c-dx-view>
        <template if:true={showErrorMessage}>
          <span class="testo-non-valido">
            <c-dx-field-px-icon class="icon-Attenzione-pieno bd-icona"></c-dx-field-px-icon>
            {errorMessage}
          </span>
        </template>
        
        <div onclick={handleConfermaManuale}>
          <c-dx-field-button field={buttonConfermaIndirizzoManuale}></c-dx-field-button>
        </div>

        <div onclick={handlePassaggioAutocompletamento}>
          <c-dx-field-button field={buttonAnnullaInserimentoManuale}></c-dx-field-button>
        </div>
      </div>
    </template>
  </div>
  
  <template if:true={errorStatus}>
    <div class="error-message">
      <span class="testo-non-valido">Campo obbligatorio</span>
    </div>
  </template>
</template>