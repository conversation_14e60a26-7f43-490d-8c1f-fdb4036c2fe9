import { LightningElement, api, track } from 'lwc';
import { utils } from 'c/dxUtils';
import { addressAutocompleteService } from './dxAddressAutocompleteService';
import getPlacesAutocomplete from '@salesforce/apex/GoogleMapsProxy.getPlacesAutocomplete';
import getPlacesDetails from '@salesforce/apex/GoogleMapsProxy.getPlaceDetails';
import { fireEvent, registerListener, unregisterListener } from 'c/pubsub';
import BffInterprete from 'c/dxBffInterprete';
import { getFormState } from 'c/dxFormState';
import { errors } from 'c/dxErrorMessage';

const AddresModeType = {
  MANUALE: 'MANUALE',
  INDIRIZZO: 'INDIRIZZO',
  AUTOCOMPLETAMENTO: 'AUTOCOMPLETAMENTO'
};

export default class AddressAutocomplete extends LightningElement {
  _currentAddressInfo = {};
  _referenceBoxManuale = {};

  _field;
  _groups;

  @track showValidationError = false;
  @track currentState = AddresModeType.AUTOCOMPLETAMENTO;
  @track errorStatus = false;
  @track errorMessage = '';
  @track inputPrincipaleUtente = '';
  @track inputSecondarioUtente = '';
  @track processoDiInvioDati = false;
  @track showDropdown = false;
  @track predictions = [];
  @track isSearchingPredictions = false;
  @track buttonConfermaIndirizzoManuale;
  @track buttonAnnullaInserimentoManuale;
  @track _viewInserimentoManuale;
  @track hideAutocompleteInput = false;

  @api blockSubmit = false;
  @api tipoAutocomplete = 'residenza';
  @api required = false;

  @api
  set field(value) {
    this._field = value;
    this._init();
  }
  get field() { return this._field; }

  @api
  set groups(value) {
    this._groups = value;
    this._init();
  }
  get groups() { return this._groups; }

  get isAutocompleteState() {
    return this.currentState === AddresModeType.AUTOCOMPLETAMENTO;
  }

  get isManualState() {
    return this.currentState === AddresModeType.MANUALE;
  }

  get isIndirizzoState() {
    return this.currentState === AddresModeType.INDIRIZZO;
  }

  get showErrorMessageAutocomplete() {
    return this.errorStatus && this.errorMessage && this.errorMessage.length > 0 && !this.isManualState;
  }

  get showErrorMessageInIndirizzoState() {
    return this.errorStatus && this.errorMessage && this.errorMessage.length > 0 && this.isIndirizzoState;
  }

  get missingHouseNumberError() {
    return errors.missingHouseNumber;
  }

  get showSubmitBlocker() {
    const isRequiredAndEmptyForManual = this.required && this.currentState === AddresModeType.MANUALE && this._fullData.length === 0;
    const shouldBlockForIndirizzoState = this.required && this.currentState === AddresModeType.INDIRIZZO;
    const shouldBlockForAutocomplete =  this.currentState === AddresModeType.AUTOCOMPLETAMENTO && this._fullData.length === 0;
    const shouldBlock = this.blockSubmit || isRequiredAndEmptyForManual || shouldBlockForIndirizzoState || shouldBlockForAutocomplete;
    console.log(' ShowSubmitBlocker:', shouldBlock, {
      required: this.required,
      currentState: this.currentState,
      fullDataLength: this._fullData.length,
      blockSubmit: this.blockSubmit
    });
    
    return shouldBlock;
  }

  get _fullData() {
    let esito = "";
    for (const key in this._currentAddressInfo) {
      esito += this._currentAddressInfo[key] || "";
    }
    return esito.trim();
  }

  get labelAutocomplete() {
    return this._field?.customAttributes?.labelAutocomplete || '';
  }

  get stileLabels() {
    return this._field?.customAttributes?.styleLabelAutocomplete || this._field?.customAttributes?.stileLabels || '';
  }

  get placeholder() {
    return this._field?.customAttributes?.placeholder || 'Via Roma 21, Milano 20156';
  }

  get inputPrincipaleClass() {
    return `input-dati-utente ${this.errorStatus ? 'error' : ''}`;
  }

  get inputPrincipaleContainerClass() {
   return `InputContainer ${this.hideAutocompleteInput ? 'hidden' : ''}`;
  }

  get inputSecondarioClass() {
    return `input-dati-utente ${this.errorStatus ? 'error' : ''}`;
  }

  get IndirizzoCaption() {
    return this._field?.customAttributes?.IndirizzoCaption || '';
  }

  get showCambioInserimento() {
    return !!(this.nonTroviIlTuoIndirizzoLabel && this.nonTroviIlTuoIndirizzoCta && !this.isManualState);
  }

  get showErrorMessage() {
    return this.errorStatus && this.errorMessage?.length > 0;
  }

  get showInserimentoManuale() {
    return !!(this.viewInserimentoManuale && this.isManualState);
  }

  get showMainInput() {
    return !this.isManualState;
  }

  get nonTroviIlTuoIndirizzoLabel() {
    return this._field?.customAttributes?.nonTroviIlTuoIndirizzoLabel || 'Non trovi il tuo indirizzo?';
  }

  get showButtonConfermaInserimentoCivico() {
    const result = !!(this.buttonConfermaInserimentoCivico && this.isIndirizzoState);
    return result;
  }

  get showNonTroviIlTuoIndirizzo() {
    const result = !!(this.nonTroviIlTuoIndirizzoCta && !this.isManualState);
    return result;
  }

  get shouldShowDropdown() {
    return this.showDropdown && this.predictions.length > 0;
  }

  get buttonConfermaInserimentoCivico() {
    const field = utils.getFirstFieldInGroupsByComponentId(this._groups, 'Conferma civico');
    if (field) {
      field.control.actionSets = [];
    }
    return field;
  }

  get nonTroviIlTuoIndirizzoCta() {
    const field = utils.getFirstFieldInGroupsByComponentId(this._groups, 'manualAddressButton');
    if (field) {
      field.control.actionSets = [];
    }
    return field;
  }

  get viewInserimentoManuale() {
    return this._viewInserimentoManuale;
  }

  setCurrentState(state) {
    if (!Object.values(AddresModeType).includes(state)) {
      console.warn('Invalid state transition attempt:', state);
      return;
    }
    
    this.currentState = state;
    this.errorStatus = false;
    this.errorMessage = '';
    
    try {
      localStorage.setItem('currentStateAddressAutocomplete', state);
    } catch (e) {
      console.warn('localStorage not available');
    }
  }

  handleInputPrincipale = (event) => {
    this.inputPrincipaleUtente = event.target.value;

    if (this.inputPrincipaleUtente.length === 0) {
      this.showDropdown = false;
      this.predictions = [];
      this.errorStatus = false;
      this.errorMessage = '';
      return;
    }
    this._searchPredictions();
  }

  handleInputSecondario = (event) => {
    this.inputSecondarioUtente = event.target.value;
  }

  handleInputFocus = () => {
    if (this.predictions.length > 0 && this.inputPrincipaleUtente.length >= 3) {
      this.showDropdown = true;
    }
  }

  handleInputBlur = () => {
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

   handlePassaggioAutocompletamento = () => {
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
    this.inputPrincipaleUtente = '';
    this.inputSecondarioUtente = '';
    this.hideAutocompleteInput = false;
  }

  handlePassaggioIndirizzoManuale = (evt) => {
    this.setCurrentState(AddresModeType.MANUALE);
    this.hideAutocompleteInput = true;
  }

  handleConfermaManuale = (evt) => {
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);
    this.errorStatus = false;
    this.errorMessage = '';

    const state = getFormState();
    let newAddress = {};

    for (const key in this._referenceBoxManuale) {
      const control = state.values[this._referenceBoxManuale[key]];
      newAddress[key] = control?.value || '';
    }
    this._currentAddressInfo = newAddress;

    this._normalizzaIndirizzo();
  }

  _init() {
    if (!this._field || !this._groups) return;

    try {
      const savedStato = localStorage.getItem('currentStateAddressAutocomplete');
      if (savedStato && Object.values(AddresModeType).includes(savedStato)) {
        this.currentState = savedStato;
      }
    } catch (e) {
      console.warn('localStorage not available');
    }

    if (this._field?.customAttributes?.['GestioneProcesso.MostraBoxDomicilio']) {
      this.tipoAutocomplete = 'domicilio';
    }
    if (this._field?.customAttributes?.['GestioneProcesso.MostraBoxResidenza']) {
      this.tipoAutocomplete = 'residenza';
    }

    let view = utils.getFirstViewInGroupsByViewId(this._groups, 'InserimentoManualeIndirizzo');
    if (!view) {
      view = utils.getFirstViewInGroupsByViewId(this._groups, 'InserimentoManualeIndirizzoDomicilio');
    }
    this._viewInserimentoManuale = view ? JSON.parse(JSON.stringify(view)) : undefined;

    this._setupManualViewButtons();
    this._setupManualViewFields();
  }

  _setupManualViewButtons() {
    if (!this._viewInserimentoManuale) return;

    const fields = utils.getAllFieldInGroupsNotCloned(this._viewInserimentoManuale.groups);

    for (const field of fields) {
      if (field.control?.type === 'pxButton') {
        if (field.customAttributes?.componentID === 'manualAddressButton') {
          field.control.actionSets = [];
        }
      }
    }
  }

  _setupManualViewFields() {
    if (!this._viewInserimentoManuale) return;

    const fields = utils.getAllFieldInGroupsNotCloned(this._viewInserimentoManuale.groups);

    for (const field of fields) {
      if (field.control?.type === 'pxButton') { 
        if (field.customAttributes?.componentID === 'cancelAddressButton') {
          field.control.actionSets = [];
          this.buttonAnnullaInserimentoManuale = JSON.parse(JSON.stringify(field));
          field.visible = false;
        } else if (field.customAttributes?.componentID === 'confirmAddressButton') {
          field.control.actionSets = [];
          this.buttonConfermaIndirizzoManuale = JSON.parse(JSON.stringify(field));
          field.visible = false;
        }
      } else {
        if (field?.reference?.toLowerCase().includes('nomestrada')) {
          this._referenceBoxManuale["nomeStrada"] = field.reference;
        }
        if (field?.reference?.toLowerCase().includes('numerocivico')) {
          this._referenceBoxManuale['civico'] = field.reference;
        }
        if (field?.reference?.toLowerCase().includes('provincia')) {
          this._referenceBoxManuale["provincia"] = field.reference;
          field.disabled = false;
        }
        if (field?.reference?.toLowerCase().includes('comune')) {
          this._referenceBoxManuale["comune"] = field.reference;
        }
        if (field?.reference?.toLowerCase().includes('cap')) {
          this._referenceBoxManuale['cap'] = field.reference;
        }
        if (field?.reference?.toLowerCase().includes('stato')) {
          this._referenceBoxManuale['stato'] = field.reference;
        }
      }
    }
  }

  async _searchPredictions() {
    this.isSearchingPredictions = true;
    this.showDropdown = true;

    try {
      const resultString = await getPlacesAutocomplete({
        input: this.inputPrincipaleUtente
      });
      const result = JSON.parse(resultString);

      if (result.error) {
        this.errorStatus = true;
        this.errorMessage = 'Errore nella ricerca dell\'indirizzo: ' + result.error;
        this.predictions = [];
      } else if (result.predictions && Array.isArray(result.predictions)) {
        this.predictions = result.predictions.map(pred => ({
          placeId: pred.place_id,
          description: pred.description,
          mainText: pred.structured_formatting?.main_text || pred.description,
          secondaryText: pred.structured_formatting?.secondary_text || ''
        }));
        this.showDropdown = this.predictions.length > 0;
      } else {
        this.predictions = [];
        this.showDropdown = false;
      }
    } catch (error) {
      console.error('Error in _searchPredictions:', error);
      this.errorStatus = true;
      this.errorMessage = 'Errore nella ricerca dell\'indirizzo: ' + (error.message || error);
      this.predictions = [];
      this.showDropdown = false;
    } finally {
      this.isSearchingPredictions = false;
    }
  }

  async _normalizzaIndirizzo() {
    if (!this._currentAddressInfo || !this._currentAddressInfo.nomeStrada || !this._currentAddressInfo.comune) {
      this.errorStatus = true;
      this.errorMessage = 'Errore: dati indirizzo incompleti per la normalizzazione.';
      this.processoDiInvioDati = false;
      this.blockSubmit = false;
      return;
    }

    this.processoDiInvioDati = true;
    this.blockSubmit = true;

    try {
      const bff = new BffInterprete({ mockUrls: false });

      let codiceProvincia = this._currentAddressInfo.provincia;
      const provinceResponse = await bff.listProvince({});
      console.log("CC_ListProvince response:", provinceResponse);
      
      if (provinceResponse?.esito?.stato === 'OK') {
        const targetProvincia = this._normalize(this._currentAddressInfo.provincia || "");
        const selectedProvince = (provinceResponse?.province || []).find(
          (p) => this._normalize(p?.nome || "") === targetProvincia || 
                 this._normalize(p?.codice || "") === targetProvincia
        );
        codiceProvincia = selectedProvince?.codice || this._currentAddressInfo.provincia;
      }

      const townResponse = await bff.listTown({ 
          codiceProvincia: codiceProvincia 
      });
      console.log("CC_ListTown response:", townResponse);

      let codiceBelfioreComune = null;
      if (townResponse?.esito?.stato === 'OK') {

        const targetComune = this._normalize(this._currentAddressInfo.comune || "");
        const selectedTown = (townResponse?.comuni || []).find(
          (t) => this._normalize(t?.nome || "") === targetComune
        );
        codiceBelfioreComune = selectedTown?.codiceCatastale || null;
      }

      const normalizeRequest = {
        civico: this._currentAddressInfo.civico || "",
        codiceBelfioreComune,
        comune: (this._currentAddressInfo.comune || "").toUpperCase(),
        indirizzo: this._currentAddressInfo.nomeStrada || "",
        siglaProvincia: (codiceProvincia || "").toUpperCase(),
      };
      

      const normalizzaResponse = await bff.normalize(normalizeRequest);
      console.log("CC_Normalizza response:", normalizzaResponse);

      const normalizzazioneOk = normalizzaResponse?.esito?.stato === 'OK';
      
      if (normalizzazioneOk || true) { 
        this._currentAddressInfo.codiceCatastaleComune = codiceBelfioreComune;
        this._currentAddressInfo.provincia = codiceProvincia;

        const referencesToUpdate = {};
        let basePathPega = '';
        
        if (this._field?.reference) {
          const refParts = this._field.reference.split('.');
          if (refParts.length > 0) {
            refParts.pop();
            basePathPega = refParts.join('.');
          }
        }

        if (!basePathPega) {
          basePathPega = this._field?.customAttributes?.referencePath ||
            (this.tipoAutocomplete === 'residenza' ? 'Contraente.Residenza' : 'Contraente.Domicilio');
          console.warn(`[addressAutocomplete] basePathPega derivato dal fallback: ${basePathPega}. Reference del campo: ${this._field?.reference}`);
        }

        referencesToUpdate[`${basePathPega}.NomeStrada`] = this._currentAddressInfo.nomeStrada || '';
        referencesToUpdate[`${basePathPega}.NumeroCivico`] = this._currentAddressInfo.civico || '';
        referencesToUpdate[`${basePathPega}.Comune`] = this._currentAddressInfo.comune || '';
        referencesToUpdate[`${basePathPega}.Provincia`] = this._currentAddressInfo.provincia || '';
        referencesToUpdate[`${basePathPega}.Cap`] = this._currentAddressInfo.cap || '';
        referencesToUpdate[`${basePathPega}.Stato`] = this._currentAddressInfo.stato || 'Italia';
        referencesToUpdate[`${basePathPega}.CodiceCatastaleComune`] = this._currentAddressInfo.codiceCatastaleComune || '';

        // Gestire lo step successivo se configurato per il finishAssignment
        const actionSetForFieldChange = this._field?.control?.actionSets?.find(as =>
          as.events && as.events.some(ev => ev.event === 'change' || ev.event === 'customAddressConfirmAction')
        );

        if (actionSetForFieldChange?.actions?.some(a => a.action === 'finishAssignment')) {
          const stepSuccessivoKey = 'GestioneProcesso.StepSuccessivo';
          const stepSuccessivoChangeVal = this._field?.customAttributes?.['GestioneProcesso.StepSuccessivo_change'];
          if (stepSuccessivoChangeVal) {
            referencesToUpdate[stepSuccessivoKey] = stepSuccessivoChangeVal; 
          }
        }

        if (Object.keys(referencesToUpdate).length > 0 && actionSetForFieldChange) {
          const evtPayload = {
            target: {
              value: this._getFieldValue(),
              reference: this._field.reference
            },
            preventDefault: () => { }
          };
          
          const fieldPayload = {
            ...this._field,
            referencesToUpdate: referencesToUpdate,
            control: {
              ...this._field.control,
              actionSets: [actionSetForFieldChange]
            }
          };

          fireEvent('handleFieldChanged', {
            evt: evtPayload,
            field: fieldPayload,
            parentLayout: this.parentLayout || null,
          });
        } else {
          if (Object.keys(referencesToUpdate).length === 0) {
            console.warn('[AddressAutocomplete] Nessun referenceToUpdate da inviare a Pega.');
          }
          if (!actionSetForFieldChange) {
            console.warn('[AddressAutocomplete] ActionSet di change/conferma non trovato per inviare i dati normalizzati a Pega.');
          }
        }

      } else {
        // Questo ramo non dovrebbe mai essere raggiunto a causa del bypass sopra
        console.error('[AddressAutocomplete] Errore logico nella normalizzazione:', normalizzaResponse?.esito);
        this.errorStatus = true;
        this.errorMessage = normalizzaResponse?.esito?.messaggi?.[0]?.testo || 
                           normalizzaResponse?.esito?.messaggio || 
                           'Errore nella normalizzazione dell\'indirizzo';
      }

    } catch (error) {
      console.error('[AddressAutocomplete] Errore durante la chiamata di normalizzazione:', error);
      this.errorStatus = true;
      this.errorMessage = error.message || 'Errore di rete o imprevisto nella normalizzazione';
    } finally {
      this.processoDiInvioDati = false;
      this.blockSubmit = false;
    }
  }

  async handlePredictionSelect(event) {
    const placeId = event.currentTarget.dataset.placeId;
    this.showDropdown = false;
    
    if (!placeId) {
      console.error('PlaceId not found');
      return;
    }

    await this._getPlaceDetails(placeId);
  }

  async _getPlaceDetails(placeId) {
    if (!placeId) {
      this.errorStatus = true;
      this.errorMessage = 'Errore: identificativo del luogo mancante';
      return;
    }

    this.processoDiInvioDati = true;

    try {
      const resultString = await getPlacesDetails({ placeId });
      const result = JSON.parse(resultString);

      if (result.error) {
        this.errorStatus = true;
        this.errorMessage = 'Errore nel recupero dei dettagli dell\'indirizzo: ' + result.error;
        return;
      }

      if (!result.result) {
        this.errorStatus = true;
        this.errorMessage = 'Errore nel recupero dei dettagli dell\'indirizzo';
        return;
      }

      const addressInfo = addressAutocompleteService.recuperaInformazioniDalPlace(result.result);
      this._currentAddressInfo = addressInfo;

      if (!addressInfo.civico) {
        this.inputPrincipaleUtente = addressInfo.nomeStrada || '';
        this.setCurrentState(AddresModeType.INDIRIZZO);
        this.errorStatus = true;
        this.errorMessage = errors.missingHouseNumber;
      } else {
        this.errorStatus = false;
        this.errorMessage = '';

        await this._normalizzaIndirizzo();
      }
    } catch (error) {
      console.error('Error in _getPlaceDetails:', error);
      this.errorStatus = true;
      this.errorMessage = 'Errore nell\'elaborazione della risposta';
    } finally {
      this.processoDiInvioDati = false;
    }
  }

  handleConfermaCivico = () => {
    if (!this.inputSecondarioUtente || this.inputSecondarioUtente.trim().length === 0) {
      this.errorStatus = true;
      this.errorMessage = 'Il numero civico è obbligatorio.';
      console.warn('[addressAutocomplete] handleConfermaCivico - Civico mancante.');
      return;
    }

    this._currentAddressInfo = {
      ...this._currentAddressInfo,
      civico: this.inputSecondarioUtente.trim()
    };

    this.errorStatus = false;
    this.errorMessage = '';
    this.setCurrentState(AddresModeType.AUTOCOMPLETAMENTO);

    this._normalizzaIndirizzo();
  }

  _getFieldValue() {
    if (!this._currentAddressInfo) return '';

    const parts = [];
    if (this._currentAddressInfo.nomeStrada) parts.push(this._currentAddressInfo.nomeStrada);
    if (this._currentAddressInfo.civico) parts.push(this._currentAddressInfo.civico);
    if (this._currentAddressInfo.comune) parts.push(this._currentAddressInfo.comune);
    if (this._currentAddressInfo.nomeProvincia) parts.push(this._currentAddressInfo.nomeProvincia);
    if (this._currentAddressInfo.cap) parts.push(this._currentAddressInfo.cap);

    return parts.join(', ');
  }

  _normalize(str) {
    if (!str) return "";
    try {
      return String(str)
        .toLowerCase()
        .normalize("NFD")
        .replace(/\p{Diacritic}/gu, "");
    } catch (e) {
      return String(str)
        .toLowerCase()
        .normalize("NFD")
        .replace(/[\u0300-\u036f]/g, "");
    }
  }

  handleExternalValidation = () => {
    if (this.showSubmitBlocker) {
      this.errorStatus = true;
      this.errorMessage = 'Campo obbligatorio';
    }
  }
  
  connectedCallback() {
    registerListener('triggerValidation', this.handleExternalValidation, this);
  }
  
  disconnectedCallback() {
    unregisterListener('triggerValidation', this.handleExternalValidation, this);
  }
}