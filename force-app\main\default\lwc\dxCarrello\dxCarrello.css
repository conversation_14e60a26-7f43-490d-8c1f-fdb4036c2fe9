.PuCarrelloContainer {
  display: flex;
  flex-direction: column;
  padding: 24px 16px;
  border: 1px solid #cccccc;
  border-radius: 24px;

  max-width: 100%;
  width: 528px;
  margin: 0 auto;
}

@media (max-width: 768px) { /* sostituisci con il valore reale di $bkp_mobile_only */
  .PuCarrelloContainer {
    width: 100%;
  }
}

.PuCarrelloContainer > .PuCarrelloHeader {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  margin: 0 -16px;
}

.PuCarrelloContainer > .PuCarrelloHeader > .UnicaIcon {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 16px;

  background-color: var(--blue-primary, #0033cc);
  padding: 10px 0 10px 25px;

  position: relative;
}

.PuCarrelloContainer > .PuCarrelloHeader > .UnicaIcon::before {
  content: "";
  aspect-ratio: 1 / 1;
  display: block;
  height: 100%;
  background-color: inherit;
  position: absolute;
  top: 0;
  left: 100%;
  transform: translateX(-50%);
  border-radius: 50%;
}

.PuCarrelloContainer > .PuCarrelloHeader > .Ribbon {
  display: flex;
  flex-direction: row;
  background-color: #6bd1ff;
  padding: 2px 8px;
  padding-left: 0;

  position: relative;
}

.PuCarrelloContainer > .PuCarrelloHeader > .Ribbon::before {
  content: "";
  aspect-ratio: 1 / 1;
  display: block;
  height: 100%;
  background-color: inherit;
  position: absolute;
  top: 0;
  left: 0;
  transform: translateX(-50%);
  border-radius: 50%;
  z-index: -10;
}

.PuCarrelloGroups{
  padding: 16px 0 0;
}

.CarrelloPuFooterBody {
  background-color: #193a56;
  width: calc(100% + 32px);
  padding: 16px;
  position: relative;
  margin: 0 -16px;
  border-bottom-right-radius: 24px;
  border-bottom-left-radius: 24px;
}

.CarrelloPuFooterBody::before {
  content: "";
  background-color: inherit;
  width: 100%;
  height: 50px;
  position: absolute;
  left: 0;
  top: 0;
  transform: translateY(-100%);
}

.CarrelloPuFooterHeader {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  width: calc(100% + 32px);
  gap: 16px;
  padding: 27px 0;
  margin: 0 -16px;
  z-index: 100;
  background-color: #fff;
  cursor: pointer;
  border-bottom: 1px solid #cccccc;
  border-bottom-left-radius: 24px;
  border-bottom-right-radius: 24px;
  position: relative;
}

.CarrelloPuFooterHeader::after {
  content: ">";
  font-family: Unipol Bold;
  color: #193a56;
  font-size: 20px;
  transform: rotate(90deg) scaleY(1.6) scaleX(.6);
}

.CarrelloPuFooterHeader.statoAperto::after {
  transform: rotateZ(270deg) scaleY(1.6) scaleX(0.6);
}