<template>
  <!-- CARRELLO SECTION -->
  <template if:false={isFooter}>
    <div class="PuCarrelloContainer">
      <span class="PuCarrelloHeader">
        <span class="UnicaIcon">
          <template if:true={headerUnicaIcon}>
            <c-dx-field field={headerUnicaIcon}></c-dx-field>
          </template>
        </span>
        <template if:true={headerRibbonValue}>
          <span class="Ribbon">
            <c-dx-field field={headerRibbonValue} field-type="caption"></c-dx-field>
          </span>
        </template>
      </span>

      <template if:true={renderGroups}>
        <template for:each={renderGroups} for:item="group">
          <div key={group.id} class="PuCarrelloGroups">
            <c-dx-groups groups={group}></c-dx-groups>
          </div>
        </template>
      </template>
    </div>
  </template>

  <!-- FOOTER SECTION -->
  <template if:true={isFooter}>
    <div class="CarrelloPuFooterContainer">
      <span onclick={handleCarrelloFooterClicked} class={footerHeaderClass}>
        <c-dx-custom-text-styles content={footerAccordionLabel} text-css={footerStyle}>
        </c-dx-custom-text-styles>
      </span>

      <template if:true={isFooterOpen}>
        <div class="CarrelloPuFooterBody">
          <template if:true={contentFooter}>
            <c-dx-layout layout={contentFooter}></c-dx-layout>
          </template>
        </div>
      </template>
    </div>
  </template>
</template>