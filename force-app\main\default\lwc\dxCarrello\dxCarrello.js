import { utils } from 'c/dxUtils';
import { LightningElement, api } from 'lwc';

export default class <PERSON><PERSON> extends LightningElement {
    _field;
    _groups;

    headerLayout;

    headerUnicaIcon;
    headerRibbonValue;
    renderGroups;
    contentFooter;

    isFooterOpen = true

    @api
    set field(value) {
        this._field = value;
    }

    get field() {
        return this._field;
    }

    @api
    set groups(value) {
        this._groups = value;
        this.headerLayout = utils.getFirstLayoutInGroupsByGroupFormat(value, 'CarrelloPUHeader');

        const type = this.field?.customAttributes?.customType;
        if (type === 'CarrelloPUFooter') {
            this.contentFooter = value[1]?.layout;
        } else {
            this.setupCarrelloPu();
        }
    }

    get groups() {
        return this._groups;
    }

    get carrelloType() {
        return this.field?.customAttributes?.customType;
    }

    setupCarrelloPu() {
        this.setupHeader();
        this.setupRibbon();
        this.filterRenderGroups(this.groups);
    }

    setupHeader() {
        if (!this.headerLayout) return;

        this.headerUnicaIcon = utils.getFirstIconInGroupsByResource(this.headerLayout.groups, 'unicoLogo');
        if (this.headerUnicaIcon?.customAttributes) {
            this.headerUnicaIcon.customAttributes.webResponsiveSize = '40L 40L 40L';
        }
    }

    setupRibbon() {
        const ribbonLayout = utils.getFirstLayoutInGroupsByGroupFormat(this.groups, 'CarrelloPURibbon');
        if (!ribbonLayout) return;

        this.headerRibbonValue = utils.getFirstCaptionInGroupsId(ribbonLayout.groups);
        if (this.headerRibbonValue) {
            this.headerRibbonValue.value = `-${this.headerRibbonValue.value}%`;
        }
    }

    filterRenderGroups(groups) {
        if (!groups) {
            this.renderGroups = [];
            return;
        }

        this.renderGroups = groups.filter(group => {
            const isHidden = group?.field?.control?.type === 'pxHidden';
            const hasHeaderView = group?.view &&
                utils.getFirstLayoutInGroupsByGroupFormat(group.view.groups, 'CarrelloPUHeader');
            const isHeaderLayout = group?.layout?.groupFormat === 'CarrelloPUHeader';
            return !(isHidden || hasHeaderView || isHeaderLayout);
        });
    }

    get isFooter() {
        return this.carrelloType === 'CarrelloPUFooter';
    }

    get footerAccordionLabel() {
        if (this.carrelloType !== 'CarrelloPUFooter') return '';
        return this.isFooterOpen ? this.field.customAttributes.textShowLess : this.field.customAttributes.textShowMore;
    }

    get footerHeaderClass() {
        return `CarrelloPuFooterHeader ${this.isFooterOpen ? 'statoAperto' : ''}`;
    }

    get footerStyle() {
        return this.field.customAttributes.textShowStyle;
    }

    handleCarrelloFooterClicked() {
        this.isFooterOpen = !this.isFooterOpen;
    }
}