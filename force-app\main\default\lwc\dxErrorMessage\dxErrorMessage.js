export const errors = {
  number: 'Il CAP non è formalmente valido',
  houseNumber: 'Il numero civico non è formalmente valido',
  email: "L'indirizzo email non è formalmente valido",
  mobilephone: 'Il numero di telefono non è formalmente valido',
  minChars: 'Formato non valido. Inserire un numero di caratteri non inferiore a',
  maxChars: 'Formato non valido. Inserire un numero di caratteri non superiore a',
  errorSurnameCF: 'Il cognome non corrisponde al codice fiscale',
  errorNameCF: 'Il nome non corrisponde al codice fiscale',
  errorTimeOut: 'Tempo scaduto',
  maxFailError: 'Nessun tentativo rimasto, si prega di riprovare',
  outcomeValidationKO: 'Il codice OTP è errato, si prega di controllare',
  taxCode: 'Il codice fiscale non è formalmente valido',
  overAge: 'Non puoi sottoscrivere questa polizza per un viaggiatore con età superiore a',
  underAge: 'Non puoi sottoscrivere questa polizza per una persona con età inferiore a',
  DoubletCF: 'Non puoi inserire più volte lo stesso viaggiatore.',
  taxCodeName: 'Il codice fiscale non corrisponde al nome',
  taxCodeSurname: 'Il codice fiscale non corrisponde al cognome',
  microchip: 'Formato non valido.',
  required: 'Campo obbligatorio',
  checkCFWithBirthDate: 'Il codice fiscale non corrisponde alla data di nascita',
  professioneNonVendibile: 'La professione selezionata non risulta vendibile',
  taxcodeAlreadyInserted: 'Il codice fiscale è stato già inserito',
  missingHouseNumber: 'Inserisci il numero civico',
  noResults: 'Nessun risultato'
};
