import { utils } from 'c/dxUtils';
import { utilsPegaText } from 'c/dxUtilsPegaText';

/////////////////////////////////////////////////////////////////////
////////////////////////////// FIELDS ///////////////////////////////
/////////////////////////////////////////////////////////////////////

/** @type {FIELD_TYPES} */
const FIELD_TYPES = {
  caption: 'caption',
  paragraph: 'paragraph',
  link: 'pxLink',
  pxHidden: 'pxHidden',
  pxInteger: 'pxInteger',
  pxButton: 'pxButton',
  pxTextInput: 'pxTextInput',
  pxCheckbox: 'pxCheckbox',
  pxRadioButtons: 'pxRadioButtons',
  pxDateTime: 'pxDateTime',
  pxCurrency: 'pxCurrency',
  pxAutoComplete: 'pxAutoComplete',
  pxDropdown: 'pxDropdown',
  pxIcon: 'pxIcon',
};

/////////////////////////////////////////////////////////////////////
///////////////////////// PX HIDDEN FIELDS //////////////////////////
/////////////////////////////////////////////////////////////////////

/** @type {PX_HIDDEN_TYPES} */
const PX_HIDDEN_TYPES = {
  analyticsPageFields: 'analyticsPageFields',
  carrello: 'carrelloPU',
  carrelloFooter: 'carrelloFooter',
  unicoProtezione: 'unicoProtezione',
  stepper: 'stepper',
  addressAutocomplete: 'addressAutocomplete',
  separator: 'separator',
  multiStepper: 'multiStepper',
  // cardGaranzie = 'cardGaranzie',
  cardGaranzieHeader: 'cardGaranzieHeader',
  // cardProtezione = 'cardProtezione',
  header: 'header',
  // verticalSpace = 'verticalSpace',
  // footerSticky = 'footerSticky',
  boxIndirizzo: 'boxIndirizzo',
  circularStepper: 'circularStepper',
  // feedbackPopup = 'feedbackPopup',
  // paymentPage = 'paymentPage',
  agencyLocator: 'agencyLocator',
  // modalUnderwriting = 'modalUnderwriting',
  stickyFooter: 'stickyFooterPU',
  // //modalLoader = "modalLoader"
  carouselWeb: 'carouselWeb',
  carouselCard: 'carouselCard',
  cardProtezione: 'cardProtezione',
  assurancePackage: 'assurancePackage',
  cardSezione: 'cardSezione',
  cardTelematica: 'cardTelematica',
  cardProtezioneDettaglioGaranzia: 'cardProtezioneDettaglioGaranzia',
  toastCard: 'toastCard',
  boxPagamento: 'boxPagamento',
  radioCardFrazionamento: 'radioCardFrazionamento',
};

/**
 * Maps the value of field.customAttributes to its corresponding value in the PX_HIDDEN_TYPES_MAPPER object.
 *
 * @param {string} type - The type to be mapped.
 * @returns {PxHiddenTypes | undefined} - The corresponding value from the PX_HIDDEN_TYPES_MAPPER object.
 */
const mapPxHiddenType = (type) => {
  if (!type) return undefined;
  switch (type) {
    case 'analyticsPageFields':
      return PX_HIDDEN_TYPES.analyticsPageFields;
    case 'Header':
      return PX_HIDDEN_TYPES.header;
    case 'StickyFooterPU':
      return PX_HIDDEN_TYPES.stickyFooter;
    case 'Separator':
      return PX_HIDDEN_TYPES.separator;
    case 'Stepper':
      return PX_HIDDEN_TYPES.stepper;
    case 'MultiStepper':
      return PX_HIDDEN_TYPES.multiStepper;
    case 'CircularStepper':
      return PX_HIDDEN_TYPES.circularStepper;
    case 'ToastCard':
      return PX_HIDDEN_TYPES.toastCard;
    case 'CardGaranzieHeader':
      return PX_HIDDEN_TYPES.cardGaranzieHeader;
    case 'CardProtezioneDettaglioGaranzia':
      return PX_HIDDEN_TYPES.cardProtezioneDettaglioGaranzia;
    case 'Assurance Package':
      return PX_HIDDEN_TYPES.assurancePackage;
    case 'CardSezione':
      return PX_HIDDEN_TYPES.cardSezione;
    case 'CarrelloPU':
      return PX_HIDDEN_TYPES.carrello;
    case 'CarrelloPUFooter':
      return PX_HIDDEN_TYPES.carrelloFooter;
    case 'UnicoProtezione':
      return PX_HIDDEN_TYPES.unicoProtezione;
    case 'CarouselCard':
      return PX_HIDDEN_TYPES.carouselCard;
    case 'BoxPagamento':
      return PX_HIDDEN_TYPES.boxPagamento;
    case 'CardTelematica':
      return PX_HIDDEN_TYPES.cardTelematica;
    case 'CarouselWeb':
      return PX_HIDDEN_TYPES.carouselWeb;
    case 'CardProtezione':
      return PX_HIDDEN_TYPES.cardProtezione;
    case 'AgencyLocator':
      return PX_HIDDEN_TYPES.agencyLocator;
    case 'BoxIndirizzo':
      return PX_HIDDEN_TYPES.boxIndirizzo;
    case 'Address Autocomplete':
      return PX_HIDDEN_TYPES.addressAutocomplete;
    case 'RadioCardFrazionamento':
      return PX_HIDDEN_TYPES.radioCardFrazionamento;
    default:
      return undefined;
  }
};

/////////////////////////////////////////////////////////////////////
///////////////////////// CUSTOM ATTRIBUTES /////////////////////////
/////////////////////////////////////////////////////////////////////

/**
 * @param {any} customAttributes
 * @returns {ButtonLinkType | undefined}
 */
const getLinkType = (customAttributes) => {
  if (customAttributes) {
    if (customAttributes.linkType) {
      return customAttributes.linkType;
    }
  }
};

/////////////////////////////////////////////////////////////////////
////////////////////////////// LAYOUTS //////////////////////////////
/////////////////////////////////////////////////////////////////////

/** @type {Object<string, SupportedGroupFormat>} */
const SUPPORTED_GROUP_FORMATS = {
  customComponent: 'customComponent', //layout usato per componenti custom. Cercare pxHidden
  mimicASentence: 'mimic-a-sentence',
  mimicASentenceCenter: 'mimic-a-sentence-center',
  mimicASentenceCenterWeb: 'mimic-a-sentence-center-web',
  dynamic: 'dynamic',
  default: 'default',
  defaultTPD: 'default-tpd',
  DefaultBgGrey: 'default-bg-grey', //Sfondo grigio su tutta la pagina
  CardBgWhite: 'card-bg-white', //Card bianca con i bordi superiori arrotondati
  cardForm: 'card-form',
  cardApp: 'card-app',
  roundedCard: 'rounded-card',
  flatCard: 'flat-card',
  boxWebBorderGray1: 'box-web-border-gray1',
  inlineGridXY: 'inline-gridXY', // 2 colonne di larghezza variabile
  inlineGrid7030: 'inline-grid-70-30',
  inlineTPDmiddle: 'inlineTPD-middle',
  carrelloPUHeader: 'carrello-PU-Header',
  carrelloPUFooter: 'carrello-PU-Footer',
  unicoProtezioneRibbon: 'unicoProtezione-Ribbon',
  row: 'row-layout',
  col: 'col-layout',
  card: 'card',
  box: 'box',
  stacked: 'stacked',
  priceContainer: 'price-container',
  assurancePackageRibbon: 'assurancePackageRibbon',
  accordionN: 'accordionN',
  accordionHeader: 'accordion-header',
  accordionOpened: 'accordionOpened',
  accordionClosed: 'accordionClosed',
  responsive2col: 'responsive-2col',
  responsive2colPU: 'responsive-2colPU',
  responsive2col8: 'responsive-2col8',
  responsive2col1fr2fr: 'responsive-2col1fr2fr',
  responsive3col: 'responsive-3col',
  tooltipCard: 'tooltip-card',
  customPosition: 'customPosition',
  cardWithBg: 'cardWithBg',
  responsiveAcolDrowTrowMcol: 'responsiveAcolDrowTrowMcol',
  responsiveAcolDrowTrowMcol16: 'responsiveAcolDrowTrowMcol16',
  BoxPrivacy: 'box-privacy'
};

const GroupsClass = {
  mimicASentence: 'mimicASentence',
  mimicASentenceCenter: 'mimicASentenceCenter',
  mimicASentenceRight: 'mimicASentenceRight',
  row: 'row-layout',
  col: 'col-layout',
  card: 'card',
  box: 'box',
  responsive2col: 'responsive2col',
  responsive2colPU: 'responsive2colPU',
  responsive2col8: 'responsive2col8',
  responsive2col1fr2fr: 'responsive2col1fr2fr',
  responsive3col: 'responsive3col',
};

/**
 * Maps a group format to its corresponding value from SUPPORTED_GROUP_FORMATS.
 *
 * @param {string | undefined} format - The group format to map.
 * @returns {SupportedGroupFormat | undefined} - The mapped group format value.
 */
const mapGroupFormat = (format) => {
  if (!format) return undefined;
  switch (format.toLocaleLowerCase()) {
    case 'mimic a sentence':
      return SUPPORTED_GROUP_FORMATS.mimicASentence;

    case 'mimic a sentence center':
      return SUPPORTED_GROUP_FORMATS.mimicASentenceCenter;
    case 'mimic a sentence center web':
      return SUPPORTED_GROUP_FORMATS.mimicASentenceCenterWeb;
    case 'defaultbggrey':
    case 'roundedcardbggrey':
      return SUPPORTED_GROUP_FORMATS.DefaultBgGrey;

    case 'cardbgwhite':
      return SUPPORTED_GROUP_FORMATS.CardBgWhite;

    case 'cardform':
      return SUPPORTED_GROUP_FORMATS.cardForm;

    case 'cardapp':
      return SUPPORTED_GROUP_FORMATS.cardApp;

    case 'boxwebbordergray1':
      return SUPPORTED_GROUP_FORMATS.boxWebBorderGray1;

    case 'inline grid x y':
      return SUPPORTED_GROUP_FORMATS.inlineGridXY;

    case 'dynamic':
      return SUPPORTED_GROUP_FORMATS.dynamic;

    case 'defaulttpd':
      return SUPPORTED_GROUP_FORMATS.default;

    case 'row':
      return SUPPORTED_GROUP_FORMATS.row;

    case 'col':
      return SUPPORTED_GROUP_FORMATS.col;

    case 'card':
      return SUPPORTED_GROUP_FORMATS.card;

    case 'box':
      return SUPPORTED_GROUP_FORMATS.box;

    case 'accordion n':
      return SUPPORTED_GROUP_FORMATS.accordionN;

    case 'accordionheader':
      return SUPPORTED_GROUP_FORMATS.accordionHeader;

    case 'accordionopened':
      return SUPPORTED_GROUP_FORMATS.accordionOpened;

    case 'accordionclosed':
      return SUPPORTED_GROUP_FORMATS.accordionClosed;

    case 'responsive 2col':
      return SUPPORTED_GROUP_FORMATS.responsive2col;

    case 'responsive 2col pu':
      return SUPPORTED_GROUP_FORMATS.responsive2colPU;

    case 'responsive 2col 8':
      return SUPPORTED_GROUP_FORMATS.responsive2col8;

    case 'responsive 2col 1fr2fr':
      return SUPPORTED_GROUP_FORMATS.responsive2col1fr2fr;

    case 'responsive 3col':
    case 'responsive 3col app row':
      return SUPPORTED_GROUP_FORMATS.responsive3col;
    case 'responsive acol drow trow mcol':
      return SUPPORTED_GROUP_FORMATS.responsiveAcolDrowTrowMcol;
    case 'responsive acol16 drow trow mcol':
      return SUPPORTED_GROUP_FORMATS.responsiveAcolDrowTrowMcol16;
    case 'pricecontainer':
      return SUPPORTED_GROUP_FORMATS.priceContainer;

    case 'tooltipcard':
      return SUPPORTED_GROUP_FORMATS.tooltipCard;

    case 'roundedcard':
      return SUPPORTED_GROUP_FORMATS.roundedCard;

    case 'inlinetpd middle':
      return SUPPORTED_GROUP_FORMATS.inlineTPDmiddle;

    case 'carrellopuheader':
      return SUPPORTED_GROUP_FORMATS.carrelloPUHeader;

    case 'carrellopufooter':
      return SUPPORTED_GROUP_FORMATS.carrelloPUFooter;

    case 'unicoprotezioneribbon':
      return SUPPORTED_GROUP_FORMATS.unicoProtezioneRibbon;

    case 'stacked':
      return SUPPORTED_GROUP_FORMATS.stacked;

    case 'stackedlayout':
      return SUPPORTED_GROUP_FORMATS.stacked;

    case 'stackedtpd':
      return SUPPORTED_GROUP_FORMATS.stacked;
    case 'flatcard':
      return SUPPORTED_GROUP_FORMATS.flatCard;
    case 'customcomponent':
      return SUPPORTED_GROUP_FORMATS.customComponent;
    case 'assurancepackageribbon':
      return SUPPORTED_GROUP_FORMATS.assurancePackageRibbon;
    case 'inline grid 70 30':
      return SUPPORTED_GROUP_FORMATS.inlineGrid7030;
    case 'customposition':
      return SUPPORTED_GROUP_FORMATS.customPosition;
    case 'cardWithBg':
      return SUPPORTED_GROUP_FORMATS.cardWithBg;
      case 'boxprivacy':
      return SUPPORTED_GROUP_FORMATS.BoxPrivacy;
    default:
      return format;
  }
};

/**
 * @param {string} formatString - The Pega groupFormat string.
 * @returns {Object} An object where keys are CSS custom property names (e.g., '--responsive-padding-desktop')
 */
const parseResponsivePaddingFormat = (formatString) => {
  if (typeof formatString !== 'string') return {};

  const styles = {};
  const format = formatString.trim().toUpperCase();

  const regex = /(P|D|T|M)\s*([\d\s]*?)(?=\s*[PDTM]|$)/g;
  let match;

  if (!/^[PDTM]/.test(format)) {
    return {};
  }

  while ((match = regex.exec(format)) !== null) {
    const type = match[1];
    const valueString = match[2].trim();
    if (valueString) {
      const parts = valueString.split(/\s+/);
      let cssPadding;
      switch (parts.length) {
        case 1:
          cssPadding = `${parts[0]}px`;
          break;
        case 2:
          cssPadding = `${parts[1]}px ${parts[0]}px`;
          break;
        case 4:
          cssPadding = `${parts[1]}px ${parts[2]}px ${parts[3]}px ${parts[0]}px`;
          break;
        default:
          cssPadding = parts.map((v) => `${v}px`).join(' ');
          break;
      }

      switch (type) {
        case 'P':
          styles['--responsive-padding-default'] = cssPadding;
          break;
        case 'D':
          styles['--responsive-padding-desktop'] = cssPadding;
          break;
        case 'T':
          styles['--responsive-padding-tablet'] = cssPadding;
          break;
        case 'M':
          styles['--responsive-padding-mobile'] = cssPadding;
          break;
        default:
          break;
      }
    }
  }
  return Object.keys(styles).length > 0 ? styles : {};
};

const regexTwoDigits = /\d{2}/;
const secReg = /\d{1,2}/;

export const regexpRowSpace = /^Row(?<availableSpace>( (L|R)){0,4})(?<ColWidth>( (\d+ \d+)){0,4})$/gim;
export const regexpRow =
  /^Row(?<Alignment>( (L|C|R|SA|SB|SE)(T|C|B)){1,4})(?<Gap>( \d+){0,4})(?<MWidth> MW\s*\d+)?$/gim;
export const regexpCol =
  /^Col(?<Alignment>( (T|C|B)(L|C|R|SA|SB|SE)){1,4})(?<Gap>( \d+){0,4})(?<MWidth> MW\s*\d+)?$/gim;
export const regexpCard =
  /^Card\s+(?<bgColor>[A-Z]{2})\s+(?<padding1>\d+)\s+(?<padding2>\d+)(?:\s+(?<borderWidth>\d+)\s+(?<borderColor>[A-Z]{2}))?(?:\s+(?<padding3>\d+)\s+(?<padding4>\d+))?$/g;
export const regexpBox =
  /^Box (?<bgColorBox>[a-z,1-9]+)(?:\s(?<border>[\d+]{1,3}\s[A-Z]{2}))?(?:(?<paddingBox>( \d+){1,4}))?$/gim;

/**
 * @typedef {Object} GetGroupFormat
 * @property {SupportedGroupFormat | undefined} groupFormat
 * @property {Object} [inlineStyles]
 */

/**
 * Retrieves the group format and columns width from the input group format.
 *
 * @param {string} inputGroupFormat - The input group format.
 * @returns {GetGroupFormat} - An object containing the group format and columns width (if applicable).
 */
const getGroupFormat = (inputGroupFormat) => {
  if (!inputGroupFormat || typeof inputGroupFormat !== 'string') {
    return { groupFormat: undefined, inlineStyles: {} };
  }
  const responsivePaddingStyles = parseResponsivePaddingFormat(inputGroupFormat);
  if (Object.keys(responsivePaddingStyles).length > 0) {
    return {
      groupFormat: 'responsive-padding-container',
      inlineStyles: responsivePaddingStyles,
    };
  }

  // TODO: is it used?
  // if (inputGroupFormat.includes(" CS")) {
  //   return { groupFormat: inputGroupFormat.split(" CS")[0] };
  // }

  // ROW and COLUMNS
  if (
    new RegExp(regexpCol).test(inputGroupFormat) ||
    new RegExp(regexpRow).test(inputGroupFormat) ||
    new RegExp(regexpCard).test(inputGroupFormat) ||
    new RegExp(regexpBox).test(inputGroupFormat) ||
    new RegExp(regexpRowSpace).test(inputGroupFormat)
  ) {
    const instructions = utils.decodeHTML(inputGroupFormat ?? '').split(' ');
    if (instructions) {
      let groupFormat = mapGroupFormat(instructions[0].toLowerCase());
      let styleInstructions = inputGroupFormat ? inputGroupFormat : '';
      let paddingBox = '';
      let bgcolorBox = '';
      const boxRegexResult = new RegExp(regexpBox).exec(inputGroupFormat);

      if (boxRegexResult && boxRegexResult.groups) {
        if (boxRegexResult.groups.paddingBox1) {
          paddingBox = boxRegexResult.groups.paddingBox1;
          bgcolorBox = boxRegexResult.groups.colorBox1;
        } else if (boxRegexResult.groups.paddingBox2) {
          paddingBox = boxRegexResult.groups.paddingBox2;
          bgcolorBox = boxRegexResult.groups.colorBox2;
        }

        if (paddingBox) {
          styleInstructions = `padding: ${paddingBox}`;
          groupFormat = SUPPORTED_GROUP_FORMATS.box;
        }
      }

      let inlineStyles = {};
      if (styleInstructions) {
        inlineStyles = { padding: paddingBox };
      }

      return {
        groupFormat,
        inlineStyles: inlineStyles,
      };
    }
  }

  // INLINE GRID
  if (inputGroupFormat.includes('Inline grid') && regexTwoDigits.test(inputGroupFormat)) {
    const words = utils.decodeHTML(inputGroupFormat ?? '').split(' ');

    const format = inputGroupFormat
      ? inputGroupFormat.replace(regexTwoDigits, 'x').replace(regexTwoDigits, 'y')
      : undefined;

    const columnsWidth = words.filter((word) => regexTwoDigits.test(word));

    return {
      groupFormat: mapGroupFormat(format),
      inlineStyles: {
        'grid-template-columns': `${columnsWidth[0] - 1}% ${columnsWidth[1] - 1}%`,
      },
    };
  }

  if (inputGroupFormat.includes('ShowAfter') || inputGroupFormat.includes('Accordion')) {
    let groupFormat;

    //show after n || accordion n
    if (secReg.test(inputGroupFormat)) {
      const words = inputGroupFormat?.split(' ');

      // TODO: figure out what this is for
      // if (words) this.variableNumber = Number(words[1]);

      const format = inputGroupFormat?.replace(secReg, 'N');

      return {
        groupFormat: mapGroupFormat(format),
      };
    } else {
      return {
        groupFormat: mapGroupFormat(inputGroupFormat),
      };
    }
  }

  // Fallback for all other formats. mapGroupFormat returns a mapped CSS class
  // or the original format if no specific mapping is found.
  return { groupFormat: mapGroupFormat(inputGroupFormat), inlineStyles: {} };
};

/**
 * @typedef {Object} StyleInstructions
 * @property {string | string[]} alignment - The alignment instructions.
 * @property {string | string[]} gap - The gap instructions.
 * @property {string | string[]} maxWidth - The max width instructions.
 * @property {string} type - The type of layout (Row or Col).
 * @property {string | string[]} gridTemplatecolumns - The column width instructions.
 * @property {string | string[]} availableSpace - The available space instructions.
 */

/**
 * Retrieves the style rules from the instructions.
 *
 * @param {string} format - The instructions object.
 * @returns {StyleInstructions} - The style rules generated from the instructions.
 */
const getRules = (format) => {
  if (typeof format !== 'string') {
    return {
      // COMMON
      type: '',

      // CARD
      bgColorCard: [],
      borderRadiusCard: [],
      setBorderCard: [],
      paddingCard: [],

      // BOX
      bgColorBox: [],
      paddingBox: [],

      // ROW / COL
      alignment: [],
      gap: [],
      maxWidth: [],
      gridTemplatecolumns: [],
      availableSpace: [],
    };
  }

  let type = '';
  let regexp;

  // CARD
  let bgColorCard = [];
  let borderRadiusCard = [];
  let setBorderCard = [];
  let paddingCard = [];

  // BOX
  let bgColorBox = [];
  let paddingBox = [];

  // ROW / COL
  let alignment = [];
  let gap = [];
  let maxWidth = [];
  let colWidth = [];
  let availableSpace = [];

  // Identifica tipo e regexp
  if (format.startsWith('Card')) {
    type = 'Card';
    regexp = new RegExp(regexpCard);
  } else if (format.startsWith('Box')) {
    type = 'Box';
    regexp = new RegExp(regexpBox);
  } else if (format.toLowerCase().startsWith('row')) {
    type = 'Row';
    regexp = new RegExp(regexpRow).test(format) ? new RegExp(regexpRow) : new RegExp(regexpRowSpace);
  } else {
    type = 'Col';
    regexp = new RegExp(regexpCol);
  }

  for (const match of format.matchAll(regexp)) {
    const groups = match.groups || {};

    // CARD
    if (type === 'Card') {
      bgColorCard = groups.bgColor?.split(' ') || [];
      borderRadiusCard = groups.borderRadius?.split(' ').slice(1) || [];
      setBorderCard = groups.border?.split(' ') || [];
      paddingCard = groups.padding?.split(' ').slice(1) || [];
    }

    // BOX
    if (type === 'Box') {
      bgColorBox = groups.bgColorBox?.split(' ') || [];
      paddingBox = groups.paddingBox?.split(' ').slice(1) || [];
    }

    // ROW / COL
    if (type === 'Row' || type === 'Col') {
      alignment = groups.Alignment?.split(' ').slice(1) || [];
      gap = groups.Gap?.split(' ').slice(1) || [];
      maxWidth = groups.MWidth?.split(' ').slice(1) || [];
      colWidth = groups.ColWidth?.split(' ').slice(1) || [];
      availableSpace = groups.availableSpace?.split(' ').slice(1) || [];
    }
  }

  return {
    // COMMON
    type,

    // CARD
    bgColorCard,
    borderRadiusCard,
    setBorderCard,
    paddingCard,

    // BOX
    bgColorBox,
    paddingBox,

    // ROW / COL
    alignment,
    gap,
    maxWidth,
    gridTemplatecolumns: colWidth,
    availableSpace,
  };
};

/**
 * Sets the alignment styles based on the alignment groups and layout type.
 *
 * @param {string | string[]} alignmentGroups - The alignment groups.
 * @param {string} layoutType - The layout type (Row or Col).
 */
const setAlignment = (alignmentGroups, layoutType) => {
  let containerGroup = {};

  const index = alignmentGroups.length === 4 || alignmentGroups.length === 2 ? 1 : 0;

  const setProperties = (property, value, taglia) => {
    containerGroup[`--${property}-${taglia}`] = value;
    if (taglia === 'd') {
      containerGroup[`--${property}-t`] = value;
      containerGroup[`--${property}-m`] = value;
    }
  };

  // Mapping
  const verticalMap = {
    T: 'flex-start',
    C: 'center',
    B: 'flex-end',
  };

  const horizontalMap = {
    L: 'flex-start',
    C: 'center',
    R: 'flex-end',
    SA: 'space-around',
    SB: 'space-between',
    SE: 'space-evenly',
  };

  for (let i = index; i < alignmentGroups.length; i++) {
    const taglia = i === 3 ? 'm' : i === 2 ? 't' : 'd';

    const [hAlign, vAlign] =
      alignmentGroups[i].length === 3
        ? [alignmentGroups[i][0] + alignmentGroups[i][1], alignmentGroups[i][2]]
        : [alignmentGroups[i][0], alignmentGroups[i][1]];

    if (layoutType === 'Row') {
      if (verticalMap[vAlign]) setProperties('align-items', verticalMap[vAlign], taglia);
      if (horizontalMap[hAlign]) {
        setProperties('justify-content', horizontalMap[hAlign], taglia);
        if (horizontalMap[hAlign] === 'space-between') {
          containerGroup['space-beetwen'] = true;
        }
      }
    } else if (layoutType === 'Col') {
      if (verticalMap[hAlign]) setProperties('justify-content', verticalMap[hAlign], taglia);
      if (horizontalMap[vAlign]) {
        setProperties('align-items', horizontalMap[vAlign], taglia);
        if (horizontalMap[vAlign] === 'space-between') {
          containerGroup['space-beetwen'] = true;
        }
      }
    }
  }

  return containerGroup;
};

const setGap = (gapGroups) => {
  let containerGroup = {};

  if (!gapGroups || gapGroups.length === 0) {
    return containerGroup;
  }

  if (gapGroups.length === 4) {
    containerGroup['--gap-desktop'] = `${gapGroups[1]}px`;
    containerGroup['--gap-tablet'] = `${gapGroups[2]}px`;
    containerGroup['--gap-mobile'] = `${gapGroups[3]}px`;
  } else if (gapGroups.length === 2) {
    containerGroup['--gap-desktop'] = `${gapGroups[1]}px`;
    containerGroup['--gap-tablet'] = `${gapGroups[1]}px`;
    containerGroup['--gap-mobile'] = `${gapGroups[1]}px`;
  } else {
    containerGroup['--gap-desktop'] = `${gapGroups[0]}px`;
    containerGroup['--gap-tablet'] = `${gapGroups[0]}px`;
    containerGroup['--gap-mobile'] = `${gapGroups[0]}px`;
  }

  return containerGroup;
};

const setColumnsWidth = (colWidth) => {
  let containerGroup = {};

  if (!Array.isArray(colWidth) || colWidth.length < 2) {
    return containerGroup;
  }
  containerGroup['display'] = 'grid';
  containerGroup['gridTemplateColumns'] = `${colWidth[0]}% ${colWidth[1]}%`;

  return containerGroup;
};

const setAvailableSpace = (spacing) => {
  let containerGroup = {};

  if (spacing.length === 0) {
    return containerGroup;
  }

  const index = spacing.length === 4 || spacing.length === 2 ? 1 : 0;

  containerGroup.display = 'grid';
  for (let i = index; i < spacing.length; i++) {
    const taglia = i === 1 ? 'd' : i === 2 ? 't' : 'm';
    const prop = `--grid-template-columns-${taglia}`;

    switch (spacing[i]) {
      case 'L':
        containerGroup[prop] = '1fr auto';
        break;
      case 'R':
        containerGroup[prop] = 'auto 1fr';
        break;
    }
  }

  return containerGroup;
};

const setMaxWidth = (width) => {
  let containerGroup = {};

  if (width.length > 0 && width.includes('MW')) {
    containerGroup['max-width'] = `${width.substring(2)}px`;
    containerGroup['margin-right'] = 'auto';
    containerGroup['margin-left'] = 'auto';
  }

  return containerGroup;
};

const setPaddingBox = (padding) => {
  let containerGroup = {};
  if (padding.length === 0) {
    return containerGroup;
  }

  if (padding.length === 1) {
    containerGroup.padding = `${padding[0]}px`;
  } else {
    containerGroup.padding = `${padding[1]}px`;
  }
  return containerGroup;
};

const setBgColorBox = (color) => {
  let containerGroup = {};
  if (color.length === 0) {
    return containerGroup;
  }

  if (utilsPegaText.getColor(`${color}`) !== '#9B9B9B') {
    containerGroup['background-color'] = utilsPegaText.getColor(`${color}`);
  } else {
    containerGroup['background-color'] = `${color}`;
  }
  // if (customAttributes?.productType) {
  //   switch (customAttributes?.productType.trim().toLowerCase()) {
  //     case 'auto':
  //       containerGroup.style.backgroundColor = '#0169B4';
  //       break;
  //     default:
  //       break;
  //   }
  // }

  return containerGroup;
};

/////////////////////////////////////////////////////////////////////
////////////////////////////// ACTIONS //////////////////////////////
/////////////////////////////////////////////////////////////////////

const isActionOpenPopupAssurance = (actionSets) => {
  for (const a of actionSets) {
    if (a.actions) {
      return !!a.actions.some(
        (act) =>
          act?.actionProcess?.localAction === 'OpenPopupAssurance' ||
          act?.actionProcess?.localAction === 'OpenPopupInterdip'
      );
    }
  }
};

const isActionOpenPopupGaranzia = (actionSets) => {
  for (const a of actionSets) {
    if (a.actions) {
      return !!a.actions.some(
        (act) =>
          act.actionProcess?.localAction === 'PopupDettagliGaranzie' ||
          act.actionProcess?.localAction === 'PopupInfoGaranzia'
      );
    }
  }
};

const getReferencesToUpdate = (field) => {
  const refs = {};
  const customAttributes = field?.customAttributes;
  const actionSets = field?.control?.actionSets;

  if (customAttributes) {
    for (const key in customAttributes) {
      if (key.startsWith('GestioneProcesso.') || key.startsWith('Ambito.')) {
        refs[key] = customAttributes[key];
      }
    }
  }

  if (isActionOpenPopupAssurance(actionSets)) {
    const regex = /[()]/;
    const referenceWords = field?.reference?.split(regex);

    if (referenceWords) {
      refs['GestioneProcesso.IndiceSezioneSelezionata'] = referenceWords?.[1];
      refs['GestioneProcesso.IndiceGaranziaSelezionata'] = referenceWords?.[3];
    }
  }

  if (isActionOpenPopupGaranzia(actionSets)) {
    const regex = new RegExp(
      /^\S+Pacchetti\((?<pacchetti>\d+)\)\S+Sezioni\((?<sezioni>\d+)\)\SGaranzie\((?<garanzie>\d+)\)\S+$/
    );
    const execution = regex.exec(field.reference);

    if (execution?.groups?.pacchetti)
      refs['GestioneProcesso.IndiceOffertaSelezionata'] = execution.groups.pacchetti;

    if (execution?.groups?.sezioni)
      refs['GestioneProcesso.IndiceSezioneSelezionata'] = execution.groups.sezioni;

    if (execution?.groups?.garanzie)
      refs['GestioneProcesso.IndiceGaranziaSelezionata'] = execution.groups.garanzie;

    const regexAttributi = new RegExp(/^\S+ListaAttributi\((?<attributi>\d+)\)\S+$/);
    const executionAttributi = regexAttributi.exec(field.reference);

    if (executionAttributi?.groups?.attributi)
      refs['GestioneProcesso.IndiceAttributoGaranziaSelezionata'] =
        executionAttributi.groups.attributi;
  }

  if (customAttributes?.VediEModifica === 'true' && field.reference) {
    const referenceParts = field.reference.split('.');
    referenceParts.pop();
    const newRef = [...referenceParts, 'VediEModifica'].join('.');
    refs[newRef] = 'true';
  }

  return refs;
};

const SUPPORTED_ACTIONS = {
  SET_VALUE: 'setValue',
  // POST_VALUE: "postValue",
  REFRESH: 'refresh',
  PERFORM_ACTION: 'takeAction',
  OPEN_URL: 'openUrlInWindow',
  ADD_ROW: 'addRow',
  DELETE_ROW: 'deleteRow',
  LOCAL_ACTION: 'localAction',
  OPEN_ASSIGNMENT: 'openAssignment',
  FINISH_ASSIGNMENT: 'finishAssignment',
  CLOSE_CONTAINER: 'closeContainer',
  RUN_DATA_TRANSFORM: 'runDataTransform',
};

/**
 * Retrieves the action data based on the specified field and target actions.
 *
 * @param {Object} field - The field object containing control and actionSets.
 * @returns {ActionData[]} - An array of objects, each containing an action and its associated events.
 */
const getActionData = (field) => {
  const actions = [];
  
  // Gestione actionSets (logica esistente)
  if (field?.control && field?.control?.actionSets) {
    const actionSetActions = field.control.actionSets.flatMap(({ actions, events }) =>
      actions
        .filter(({ action }) => Object.values(SUPPORTED_ACTIONS).includes(action))
        .map((action) => ({ ...action, events }))
    );
    actions.push(...actionSetActions);
  }
  
  // Gestione customAttributes per modalCloseIcon - Chiusura modale ui-modal
  const { modalCloseIcon, closeModal } = field.customAttributes || {};
  if (modalCloseIcon === "true" || closeModal === "true") {
    actions.push({
      action: SUPPORTED_ACTIONS.CLOSE_CONTAINER,
      events: ['click'], 
    });
  }
  
  return actions;
};

const VOWELS = ['a', 'e', 'i', 'o', 'u'];
const CONSONANTS = [
  'b',
  'c',
  'd',
  'f',
  'g',
  'h',
  'j',
  'k',
  'l',
  'm',
  'n',
  'p',
  'q',
  'r',
  's',
  't',
  'v',
  'w',
  'x',
  'y',
  'z',
];

const checkNameSurnameOnFiscalCode = (fiscalCode, nameOrSurname, fieldType) => {
  if (!fiscalCode) return false;

  const expectedCode = getLettersFromCF(fiscalCode, fieldType);
  const actualCode = getNameCode(nameOrSurname.toLowerCase(), fieldType === 'name');  
  return expectedCode === actualCode;
};

const getLettersFromCF = (cf, type) => {
  if (cf.length < 6) return '';
  return type === 'surname' ? cf.slice(0, 3).toLowerCase() : cf.slice(3, 6).toLowerCase();
};

const getNameCode = (text, isName) => {
  const cleaned = text.replace(/[^a-zA-Z]/g, '').toLowerCase();
  const consonants = Array.from(cleaned).filter((c) => /[^aeiou]/.test(c));
  const vowels = Array.from(cleaned).filter((c) => /[aeiou]/.test(c));

  let code;

  if (isName && consonants.length >= 4) {
    code = consonants[0] + consonants[2] + consonants[3];
  } else {
    code = consonants.slice(0, 3).join('');
  }

  if (code.length < 3) {
    code += vowels.slice(0, 3 - code.length).join('');
  }

  while (code.length < 3) {
    code += 'x';
  }

  return code;
};

export const utilsPega = {
  fields: {
    FIELD_TYPES,
  },
  pxHidden: {
    PX_HIDDEN_TYPES,
    mapPxHiddenType,
  },
  customAttributes: {
    getLinkType,
    checkNameSurnameOnFiscalCode,
  },
  layout: {
    SUPPORTED_GROUP_FORMATS,
    GroupsClass,
    getGroupFormat,
    parseResponsivePaddingFormat, // Export the new function
  },
  rowCol: {
    getRules,
    setAlignment,
    setGap,
    setColumnsWidth,
    setAvailableSpace,
    setMaxWidth,
  },
  cardBox: {
    getRules,
    setBgColorBox,
    setPaddingBox,
  },
  actions: {
    SUPPORTED_ACTIONS,
    getActionData,
    getReferencesToUpdate,
  },
};
