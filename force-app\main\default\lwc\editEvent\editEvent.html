<template>
    <lightning-quick-action-panel header="Edit Event">
        <template if:true={isLoading}>
            <lightning-spinner alternative-text="Loading" size="small"></lightning-spinner>
        </template>

        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Dettagli Appuntamento</span>
            </h3>
            <div class="slds-section__content">
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <lightning-record-picker
                        label={formFields.OwnerId.label}
                        data-field="OwnerId"
                        placeholder="Search Users..."
                        required={formFields.OwnerId.required}
                        value={formFields.OwnerId.value}
                        object-api-name="User"
                        onchange={handleFieldChangeLookup}
                    >
                    </lightning-record-picker>
                </div>
                 <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-record-picker
                            label={formFields.Group__c.label}
                            data-field="Group__c"
                            placeholder="Cerca Groups..."
                            value={formFields.Group__c.value}
                            filter={groupFilter}
                            required={formFields.Group__c.required}
                            onchange={handleFieldChangeLookup}
                            object-api-name="Group__c"
                        >
                        </lightning-record-picker>
                    </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <lightning-input 
                        label={formFields.Subject.label}
                        data-field="Subject"
                        value={formFields.Subject.value}
                        required={formFields.Subject.required}
                        onchange={handleFieldChange}>
                    </lightning-input>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <lightning-textarea 
                        label={formFields.Description.label}
                        data-field="Description"
                        value={formFields.Description.value}
                        required={formFields.Description.required}
                        onchange={handleFieldChange}>
                    </lightning-textarea>
                </div>
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-input 
                            label={formFields.StartDateTime.label}
                            data-field="StartDateTime"
                            type="datetime"
                            value={formFields.StartDateTime.value}
                            required={formFields.StartDateTime.required}
                            onchange={handleFieldChange}>
                        </lightning-input>
                    </div>
                    
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-input 
                            label={formFields.EndDateTime.label}
                            data-field="EndDateTime"
                            type="datetime"
                            value={formFields.EndDateTime.value}
                            required={formFields.EndDateTime.required}
                            onchange={handleFieldChange}>
                        </lightning-input>
                    </div>
                </div>
                <!-- Hide All-Day Event field if ServiceAppointmentId exists -->
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <template if:false={hasServiceAppointment}>
                        <lightning-input 
                            label={formFields.IsAllDayEvent.label}
                            data-field="IsAllDayEvent"
                            type="checkbox"
                            variant="label-stacked"
                            required={formFields.IsAllDayEvent.required}
                            onchange={handleFieldChangeCheckbox}>
                        </lightning-input>
                    </template>
                </div>
            </div>
        </div>
        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Partecipanti</span>
            </h3>
            <div class="slds-section__content">
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <div class="slds-form-element">
                        
                        <lightning-record-picker
                            label="Partecipanti"
                            data-field="Attendees"
                            placeholder="Search Users..."
                            object-api-name="User"
                            matching-info={attendeesMatchingInfo}
                            display-info={attendeesDisplayInfo}
                            onchange={handleAttendeesChange}>
                        </lightning-record-picker>
                        <!-- 
                        <lightning-record-picker
                            label="Partecipanti"
                            data-field="Attendees"
                            placeholder="Search Contacts..."
                            object-api-name="Contact"
                            filter={attendeesFilter}
                            matching-info={attendeesMatchingInfo}
                            display-info={attendeesDisplayInfo}
                            onchange={handleAttendeesChange}>
                        </lightning-record-picker>
                        -->
                        <div class="slds-p-top_x-small">
                            <div class="slds-pill_container">
                                <template for:each={selectedAttendees} for:item="attendee">
                                    <template lwc:if={attendee.owner}>
                                        <lightning-pill 
                                            key={attendee.recordId}
                                            label={attendee.name}
                                            name={attendee.recordId}>
                                            <lightning-icon
                                                icon-name="standard:user"
                                                alternative-text="User">
                                            </lightning-icon>
                                        </lightning-pill>
                                    </template>
                                    <template lwc:else>
                                        <lightning-pill 
                                            key={attendee.recordId}
                                            label={attendee.name}
                                            name={attendee.recordId}
                                            onremove={handleRemoveAttendee}>
                                            <lightning-icon
                                                icon-name="standard:user"
                                                alternative-text="User">
                                            </lightning-icon>
                                        </lightning-pill>
                                    </template>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <div class="slds-form-element">
                        <label class="slds-form-element__label" for="schedaSoggettoName">
                            {formFields.FinServ__Household__c.label}
                        </label>
                        <div class="slds-form-element__control">
                            <lightning-formatted-text
                                value={schedaSoggettoName}
                                id="schedaSoggettoName">
                            </lightning-formatted-text>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Modalità incontro</span>
            </h3>
            <div class="slds-section__content">
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-combobox
                            label={formFields.Modalit_di_esecuzione_incontro__c.label}
                            data-field="Modalit_di_esecuzione_incontro__c"
                            field-level-help="In caso di selezione della modalità di incontro in video chiamata sarà generato automaticamente un link di google meet e inserito nei dettagli al salvataggio dell'appuntamento."
                            value={formFields.Modalit_di_esecuzione_incontro__c.value}
                            options={modalitaIncontroOptions}
                            onchange={handleFieldChangePicklist}>
                        </lightning-combobox>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-input 
                            label={formFields.Location.label}
                            data-field="Location"
                            value={formFields.Location.value}
                            required={formFields.Location.required}
                            onchange={handleFieldChange}>
                        </lightning-input>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-input 
                            label={formFields.Telefono_Cliente__c.label}
                            data-field="Telefono_Cliente__c"
                            value={formFields.Telefono_Cliente__c.value}
                            required={formFields.Telefono_Cliente__c.required}
                            onchange={handleFieldChange}>
                        </lightning-input>
                    </div>
                
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-record-picker
                            label={formFields.Service_Territory__c.label}
                            data-field="Service_Territory__c"
                            placeholder="Cerca Agenzie/Punti vendita..."
                            display-info={agencyDisplayInfo}
                            matching-info={agencyMatchingInfo}
                            value={formFields.Service_Territory__c.value}
                            filter={serviceTerritoryFilter}
                            required={formFields.Service_Territory__c.required}
                            object-api-name="ServiceTerritory"
                            onchange={handleFieldChangeLookup}
                        >
                        </lightning-record-picker>
                    </div>
                </div>
                <div class="slds-col slds-size_1-of-1 slds-p-around_x-small">
                    <div class="slds-form-element">
                        <label class="slds-form-element__label" for="LinkGoogleMeet">
                            {formFields.ApptBookingInfoUrl__c.label}
                        </label>
                        <div class="slds-form-element__control">
                            <lightning-formatted-url
                                value={formFields.ApptBookingInfoUrl__c.value}
                                id="LinkGoogleMeet"
                                target="_blank"
                                label={formFields.ApptBookingInfoUrl__c.value}>
                            </lightning-formatted-url>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Riferimenti</span>
            </h3>
            <div class="slds-section__content">
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-record-picker
                            label={formFields.Trattativa__c.label}
                            data-field="Trattativa__c"
                            placeholder="Cerca Attività..."
                            value={formFields.Trattativa__c.value}
                            filter={trattativaFilter}
                            required={formFields.Trattativa__c.required}
                            onchange={handleFieldChangeLookup}
                            object-api-name="Opportunity"
                        >
                        </lightning-record-picker>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <lightning-record-picker
                            label={formFields.Case__c.label}
                            data-field="Case__c"
                            placeholder="Cerca Case..."
                            value={formFields.Case__c.value}
                            filter={caseFilter}
                            required={formFields.Case__c.required}
                            onchange={handleFieldChangeLookup}
                            object-api-name="Case"
                        >
                        </lightning-record-picker>
                    </div>
                </div>
            </div>
        </div>
        <!-- Hide Other Information section if ServiceAppointmentId exists -->
        <template if:false={createdBySa}>
            <div class="slds-section slds-is-open">
                <h3 class="slds-section__title slds-theme_shade">
                    <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Altre informazioni</span>
                </h3>
                <div class="slds-section__content">
                    <div class="slds-grid slds-wrap">
                        <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                            <lightning-combobox
                                label={formFields.ShowAs.label}
                                data-field="ShowAs"
                                value={formFields.ShowAs.value}
                                options={showAsOptions}
                                onchange={handleFieldChangePicklist}>
                            </lightning-combobox>
                        </div>
                        <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                            <lightning-input 
                                label={formFields.IsPrivate.label}
                                data-field="IsPrivate"
                                type="checkbox"
                                variant="label-stacked"
                                required={formFields.IsPrivate.required}
                                onchange={handleFieldChangeCheckbox}>
                            </lightning-input>
                        </div>
                        <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                            <lightning-input 
                                label={formFields.IsReminderSet.label}
                                data-field="IsReminderSet"
                                type="checkbox"
                                variant="label-stacked"
                                required={formFields.IsReminderSet.required}
                                onchange={handleFieldChangeCheckbox}>
                            </lightning-input>
                        </div>
                        <template if:true={formFields.IsReminderSet.value}> <!-- Begin - Add for fix defect 1338321 -->
                            <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                                <lightning-combobox
                                    name="reminder"
                                    label={formFields.ReminderMinutesBeforeStart.label}
                                    data-field="ReminderMinutesBeforeStart"
                                    value={formFields.ReminderMinutesBeforeStart.value}
                                    options={reminderOptions}
                                    onchange={handleFieldChangePicklist}>
                            </lightning-combobox>
                            </div>
                        </template> <!--End - Add for fix defect 1338321 -->
                        <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                            <lightning-input 
                                label={formFields.Mail__c.label}
                                data-field="Mail__c"
                                field-level-help="Se selezionato, il Cliente riceverà l’appuntamento nella sua Area Riservata e tutti i partecipanti saranno notificati tramite e-mail"
                                type="checkbox"
                                variant="label-stacked"
                                required={formFields.Mail__c.required}
                                onchange={handleFieldChangeCheckbox}>
                            </lightning-input>
                        </div>
                    </div> 
                </div>
            </div>
        </template>
        <div class="slds-section slds-is-open">
            <h3 class="slds-section__title slds-theme_shade">
                <span class="slds-truncate slds-p-horizontal_small" title="Section Title">Informazioni Tecniche</span>
            </h3>
            <div class="slds-section__content">
                <div class="slds-grid slds-wrap">
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <div class="slds-form-element">
                            <label class="slds-form-element__label" for="whoName">Nome</label>
                            <div class="slds-form-element__control">
                                <lightning-formatted-text
                                    value={whoName}
                                    id="whoName">
                                </lightning-formatted-text>
                            </div>
                        </div>
                    </div>
                    <div class="slds-col slds-size_1-of-2 slds-p-around_x-small">
                        <div class="slds-form-element">
                            <label class="slds-form-element__label" for="whatName">Correlato</label>
                            <div class="slds-form-element__control">
                                <lightning-formatted-text
                                    value={whatName}
                                    id="whatName">
                                </lightning-formatted-text>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="slds-m-top_large slds-align_absolute-center">
            <!--lightning-button 
                variant="neutral" 
                label="Cancel" 
                onclick={closeAction} 
                class="slds-m-right_x-small">
            </lightning-button> -->
            <lightning-button 
                variant="brand" 
                label="Salva"
                onclick={handleSave}>
            </lightning-button>
        </div>
    </lightning-quick-action-panel>
</template>