import { api, LightningElement, track, wire } from 'lwc';
import { CloseActionScreenEvent } from 'lightning/actions';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import getEventRecord from '@salesforce/apex/EventController.getEventRecord';
import updateEvent from '@salesforce/apex/EventController.updateEvent';
import { NavigationMixin, CurrentPageReference } from 'lightning/navigation';
import getPicklistValues from '@salesforce/apex/EventController.getPicklistValues';
import getUserName from '@salesforce/apex/EventController.getUserName';
import getContactName from '@salesforce/apex/EventController.getContactName';

export default class EditEvent extends NavigationMixin(LightningElement) {
    @api recordId;
    @track isLoading = false;
    @track createdBySa = false;
    @track modalitaIncontroOptions = [];
    @track showAsOptions = [];
    @track selectedAttendees = [];
    @track ReminderMinutesBeforeStart = '0'; // Add for fix defect 1338321

    //variable to store the event record fields. Each new field in the form must be added here
    //each property MUST REPRESENT a field of the Event object
    @track formFields = {
        OwnerId: { value: '', required: true, label: 'Assegnato A' },
        Subject: { value: '', required: true, label: 'Oggetto' },
        Description: { value: '', required: false, label: 'Descrizione' },
        StartDateTime: { value: '', required: true, label: 'Inizio' },
        EndDateTime: { value: '', required: true, label: 'Fine' },
        IsAllDayEvent: { value: false, required: false, label: 'Evento intera giornata' },
        Group__c: { value: '', required: false, label: 'Assegna a Collaborativo' },
        FinServ__Household__c: { value: '', required: false, label: 'Scheda Soggetto' },
        Location: { value: '', required: false, label: 'Luogo' },
        ApptBookingInfoUrl__c: { value: '', required: false, label: 'Link Google Meet' },
        Telefono_Cliente__c: { value: '', required: false, label: 'Telefono Cliente' },
        Service_Territory__c: { value: '', required: false, label: 'Agenzia/Punto Vendita' },
        Trattativa__c: { value: '', required: false, label: 'Trattativa' },
        Case__c: { value: '', required: false, label: 'Attività di Contatto' },
        ShowAs: { value: '', required: true, label: 'Mostra come' },
        IsPrivate: { value: false, required: false, label: 'Privato' },
        IsReminderSet: { value: false, required: false, label: 'Promemoria Impostato' },
        ReminderMinutesBeforeStart: { value: '', required: false, label: "Tempo prima dell'evento" }, // Add for fix defect 1338321
        ReminderDateTime: { value: '', required: false, label: 'Tempo notifica' },
        Mail__c: { value: false, required: false, label: 'Invia Notifica' },
        Modalit_di_esecuzione_incontro__c: { value: '', required: false, label: 'Modalità Incontro' },
    };

    //readonly fields from nested objects
    @track whatName = '';
    @track whoName = '';
    @track schedaSoggettoName = ''; //FinServ__Household__c.Name
    @track ReminderDateTime; // Add for fix defect 1338321

    reminderOptions = [ // Add for fix defect 1338321
        { label: '0 minuti', value: '0' },
        { label: '5 minuti', value: '5' },
        { label: '10 minuti', value: '10' },
        { label: '15 minuti', value: '15' },
        { label: '30 minuti', value: '30' },
        { label: '1 ora', value: '60' },
        { label: '2 ore', value: '120' },
	    { label: '3 ore', value: '180' },
	    { label: '4 ore', value: '240' },
	    { label: '5 ore', value: '300' },
	    { label: '6 ore', value: '360' },
	    { label: '7 ore', value: '420' },
	    { label: '8 ore', value: '480' },
	    { label: '9 ore', value: '540' },
	    { label: '10 ore', value: '600' },
	    { label: '11 ore', value: '660' },
	    { label: '12 ore', value: '720' },
	    { label: '18 ore', value: '1080' },
	    { label: '1 giorno', value: '1440' },
	    { label: '2 giorni', value: '2880' },
	    { label: '3 giorni', value: '4320' },
	    { label: '4 giorni', value: '5760' },
	    { label: '1 settimana', value: '10080' },
	    { label: '2 settimana', value: '21600' }
    ];

  /*handleReminderChange(event) { // Add for fix defect 1338321
    this.formFields.ReminderMinutesBeforeStart.value = event.detail.value;
    // this.ReminderMinutesBeforeStart = event.detail.value;
    console.log('Promemoria aggiornato a:', this.formFields.ReminderMinutesBeforeStart.value);
    } 
    */

    attendeesMatchingInfo = {
        primaryField: { fieldPath: 'Name'},
        additionalFields: [{ fieldPath: 'Email' }],
    };
    attendeesDisplayInfo = {
        primaryField: 'Name',
        additionalFields: ['Email'],
    };

    attendeesFilter = {
        criteria: [
            {
                fieldPath: 'Account.RecordType.DeveloperName',
                operator: 'eq',
                value: 'PersonAccount',
            }
        ]
    }

    trattativaFilter;
    caseFilter;
    groupFilter;

    agencyDisplayInfo = {
        primaryField: 'Name',
        additionalFields: ['Street'],
    };

    agencyMatchingInfo = {
        primaryField: { fieldPath: 'Name'},
        additionalFields: [{ fieldPath: 'Street'}]
    };

    //get picklist values for the field Modalit_di_esecuzione_incontro__c (Modalità Incontro)
    @wire(getPicklistValues, { 
        objectName: 'Event',
        fieldName: 'Modalit_di_esecuzione_incontro__c' 
    })
    wiredModalitaIncontroOptions({ error, data }) {
        if (data) {
            this.modalitaIncontroOptions = data;
        } else if (error) {
            console.error('Error fetching picklist values:', error);
        }
    }

    //get picklist values for the field ShowAs
    @wire(getPicklistValues, { 
        objectName: 'Event',
        fieldName: 'ShowAs'
    })
    wiredShowAsOptions({ error, data }) {
        if (data) {
            this.showAsOptions = data;
        } else if (error) {
            console.error('Error fetching ShowAs picklist values:', error);
        }
    }

    connectedCallback() {
        if (this.recordId) {
            this.initialize();
        }
    }

    renderedCallback() {
        // Set checkbox states after render
        const checkboxFields = ['IsAllDayEvent', 'IsPrivate', 'IsReminderSet', 'Mail__c'];
        
        checkboxFields.forEach(field => {
            const checkbox = this.template.querySelector(`lightning-input[data-field="${field}"]`);
            const value = this.formFields[field].value;
            if (checkbox && value) {
                checkbox.checked = value;
            }
        });

    }

    //fetch Event record data
    initialize() {
        this.isLoading = true;
        getEventRecord({ eventId: this.recordId })
            .then(result => {
                if (!result.error){
                    const recordfields = result.eventRecord;
                    console.log('event data: ' + JSON.stringify(result.eventRecord));
                    //if event has been created by Service Appointment, set the createdBySa flag to true
                    //this is used to show/hide some fields or sections in the UI
                    this.createdBySa = result.ServiceAppointmentId != null;
                    //loop through the fields and set their values
                    Object.keys(this.formFields).forEach(fieldName => {
                        this.formFields[fieldName].value = recordfields[fieldName] || '';
                    });
                    //set the readonly child fields
                    this.schedaSoggettoName = recordfields.FinServ__Household__r ? recordfields.FinServ__Household__r.Name : '';
                    this.whatName = recordfields.What ? recordfields.What.Name : '';
                    this.whoName = recordfields.Who ? recordfields.Who.Name : '';

                    // Begin - Add for fix defect 1338321
                    if (this.formFields.IsReminderSet.value == true && this.formFields.ReminderDateTime.value !== null 
                        && this.formFields.StartDateTime.value !== null) { 
                        let diffMs = new Date(this.formFields.StartDateTime.value) - new Date(this.formFields.ReminderDateTime.value);
                        let diffMinutes = Math.round(diffMs / (1000 * 60));
                        // this.formFields.ReminderMinutesBeforeStart.value = '60'; 
                        this.formFields.ReminderMinutesBeforeStart.value = diffMinutes.toString(); 
                    } // End - Add for fix defect 1338321
                    

                    //set household id for Trattativa and Attività filter
                    if (recordfields.FinServ__Household__c !== undefined) {

                        this.trattativaFilter = {
                            criteria: [
                                {
                                    fieldPath: 'FinServ__Household__c',
                                    operator: 'eq',
                                    value: recordfields.FinServ__Household__c,
                                }
                            ]
                        };

                        this.caseFilter = {
                            criteria: [
                                {
                                    fieldPath: 'FinServ__Household__c',
                                    operator: 'eq',
                                    value: recordfields.FinServ__Household__c,
                                }
                            ]
                        };

                        
                        
                    }

                     this.groupFilter = {
                            criteria: [
                                {
                                    fieldPath: 'CollaborativeAgendaActivation__c',
                                    operator: 'eq',
                                    value: true,
                                }
                            ]
                        };

                    this.serviceTerritoryFilter = {
                            criteria: [
                                {
                                    fieldPath: 'IsActive',
                                    operator: 'eq',
                                    value: true,
                                }
                            ]
                    };
                    //add owner pill
                    this.selectedAttendees.push({
                        recordId: recordfields.OwnerId,
                        name: recordfields.Owner.Name,
                        owner: true
                    });
                    //populate the selectedAttendees array with the attendees of the event
                    if (result.attendeesList) {

                        result.attendeesList.forEach(attendee => {
                            this.selectedAttendees.push({
                                recordId: attendee.Id,
                                name: attendee.Name,
                            })
                        });
                        /*
                        this.selectedAttendees = result.attendeesList.map(attendee => ({
                            recordId: attendee.Id,
                            name: attendee.Name
                        }));
                        */
                    }
                    
                }
            })
            .catch(error => {
                console.log(JSON.stringify(error));
                this.dispatchEvent(new ShowToastEvent({
                    title: 'Error Loading Record',
                    //message: error.body.message,
                    variant: 'error'
                }));
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    //method to handle the field change event of every simple input field
    handleFieldChange(event) {
        const fieldName = event.target.dataset.field;
        this.formFields[fieldName].value = event.target.value;
    }
    //method to handle the field change event of the checkbox input field
    handleFieldChangeCheckbox(event) {
        const fieldName = event.target.dataset.field;
        this.formFields[fieldName].value = event.target.checked;
    }
    //method to handle the field change event of the picklist input field
    handleFieldChangePicklist(event) {
        const fieldName = event.target.dataset.field;
        this.formFields[fieldName].value = event.detail.value;
    }
    //method to handle the field change event of the lookup input field
    handleFieldChangeLookup(event) {
        const fieldName = event.target.dataset.field;
        this.formFields[fieldName].value = event.detail.recordId;
    }

    //method specific for the field Attendees. Once the user has been selected, the record is added to
    //the pill list and the record picker is cleared
    handleAttendeesChange(event) {
        this.isLoading = true;
        const selectedUserId = event.detail.recordId;
        if (selectedUserId) {
            // Check if attendee already exists
            if (!this.selectedAttendees.some(attendee => attendee.recordId === selectedUserId)) {

                // Fetch contact name using Apex
                /*
                getContactName({ contactId: selectedUserId })
                    .then(contactName => {
                        this.selectedAttendees.push({
                            recordId: selectedUserId,
                            name: contactName})
                */ 

                // Fetch user name using Apex
                getUserName({ userId: selectedUserId })
                    .then(userName => {
                        this.selectedAttendees.push({
                            recordId: selectedUserId,
                            name: userName
                        })
                        // Clear the record picker using resetSelection()
                        const recordPicker = this.template.querySelector('lightning-record-picker[data-field="Attendees"]');
                        if (recordPicker) {
                            recordPicker.clearSelection();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching user name:', error);
                        this.dispatchEvent(new ShowToastEvent({
                            title: 'Error',
                            message: 'Unable to fetch user name',
                            variant: 'error'
                        }));
                    }).finally(() => {
                        this.isLoading = false;
                    });
            } else {
                this.isLoading = false;
                const recordPicker = this.template.querySelector('lightning-record-picker[data-field="Attendees"]');
                if (recordPicker) {
                    recordPicker.clearSelection();
                }
                this.dispatchEvent(new ShowToastEvent({
                    title: 'Warning',
                    message: 'Utente già selezionato',
                    variant: 'warning'
                }));
            }
        }
    }

    handleRemoveAttendee(event) {
        const attendeeId = event.target.name;
        this.selectedAttendees = this.selectedAttendees.filter(attendee => attendee.recordId !== attendeeId);
    }

    //validation of the fields before saving the record
    //this method checks if the required fields are filled and returns an array of validation messages
    //TODO: check if the date fields are valid. es StartDateTime < EndDateTime
    validateFields() {
        let isValid = true;
        let validationMessages = [];

        Object.keys(this.formFields).forEach(fieldName => {
            const field = this.formFields[fieldName];
            if (field.required && !field.value) {
                isValid = false;
                validationMessages.push(`${field.label} is required`);
            }
        });

        return { isValid, messages: validationMessages };
    }

    //save the record
    //the method first validates the fields and if they are valid, it sends records to the server.
    //if the server returns success, it shows a success message and refreshes the page
    handleSave() {
        const validation = this.validateFields();
        
        if (!validation.isValid) {
            this.dispatchEvent(new ShowToastEvent({
                title: 'Validation Error',
                message: validation.messages.join(', '),
                variant: 'error'
            }));
            return;
        }

        this.isLoading = true;
        const fields = {};
        Object.keys(this.formFields).forEach(fieldName => {
            fields[fieldName] = this.formFields[fieldName].value != null ? this.formFields[fieldName].value : null;
        });

        console.log('attendees selected: ', JSON.stringify(this.selectedAttendees));
        let selectedAttendeesList = [];
        this.selectedAttendees.forEach(attendee => {
            selectedAttendeesList.push(attendee.recordId);
            });
        console.log('attendeesList: ', JSON.stringify(selectedAttendeesList));
        
        updateEvent({ eventId: this.recordId, fields: fields, attendeeList: selectedAttendeesList })
            .then((result) => {
                if (!result.error) {
                    this.dispatchEvent(new ShowToastEvent({
                        title: 'Success',
                        message: 'Evento modificato con successo',
                        variant: 'success'
                    }));
                    this.refreshPage();
                } else {
                    console.log('errorMessage', result.errorMessage);
                    console.log('errorStackTraceString', result.errorStackTraceString);
                    this.dispatchEvent(new ShowToastEvent({
                        title: 'Error',
                        message: 'Impossibile modificare l\'evento',
                        variant: 'error'
                    }));
                }
            })
            .catch(error => {
                this.dispatchEvent(new ShowToastEvent({
                    title: 'Error',
                    message: error.body.message,
                    variant: 'error'
                }));
            })
            .finally(() => {
                this.isLoading = false;
            });
    }

    refreshPage(){
        this[NavigationMixin.Navigate]({
            type: 'standard__recordPage',
            attributes: {
                recordId: this.recordId,
                objectApiName: 'Event',
                actionName: 'view'
            }
        });
    }
}