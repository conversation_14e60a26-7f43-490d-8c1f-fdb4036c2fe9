<!--
  @description       : 
  <AUTHOR> <EMAIL>
  @group             : 
  @last modified on  : 04-14-2025
  @last modified by  : <EMAIL>
-->
<template>
  <div class="record-table">

    <template if:true={isLoading}>
      <div class="spinner-container absolute-overlay">
        <lightning-spinner alternative-text="Caricamento..." size="medium"></lightning-spinner>
      </div>
    </template>

    <template if:true={rows}>
      <div class="table-scroll-container">
        <div class="table-header">
          <div class="column toggle"></div>
          <div class="column product-name">Nome Prodotto</div>
          <template if:true={isSameUserAgency}>
            <div class="column policy-id">Num Polizza</div>
            <div class="column role">Ruolo cliente</div>
            <div class="column prize">Premio Annuo</div>
            <div class="column due-date">Scadenza Annua</div>
            <div class="column fractionation">Frazionamento</div>
          </template>
          <div class="column actions"></div>
        </div>

        <template for:each={rows} for:item="row">
          <div key={row.id} class={row.rowClass}>
            <div class="table-row">
              <div class="column toggle">
                <template if:false={row.isReadOnly}>
                  <template if:false={agencyTypeIsOther}>
                    <lightning-button-icon icon-name={row.iconName} onclick={toggleDetails} data-id={row.id}
                      variant="bare" alternative-text="Toggle Details"></lightning-button-icon>
                  </template>
                </template>
              </div>
              <div class="column product-name">{row.nomeProdotto}</div>
              
              <template if:true={isSameUserAgency}>
                <template lwc:if={row.isReadOnly}>
                  <div class="column policy-id">{row.idPolizza}</div>
                </template>
                <template lwc:else>
                  <div class="column policy-id polizza-link">
                    <a href="javascript:void(0);" data-id={row.id} onclick={navigateToRecord}>
                      {row.idPolizza}
                    </a>
                  </div>
                </template>

                <div class="column role">{row.ruoloCliente}</div>
                <div class="column prize">{row.premio}</div>
                <div class="column due-date">{row.scadenza}</div>
                <div class="column fractionation">{row.fractionation}</div>
              </template>
              
              <div class="column actions">
                <template if:false={row.isReadOnly}>
                  <template if:false={agencyTypeIsOther}>
                    <div class="custom-dropdown-wrapper">
                      <lightning-button-icon icon-name="utility:down" variant="border-filled" alternative-text="Azioni"
                        onclick={handleDropdownToggle} data-id={row.id}></lightning-button-icon>
                      <template if:true={row.showDropdown}>
                        <div class="dropdown-portal visible">
                          <ul class="dropdown-menu">
                            <template if:true={row.isUnica}>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="storno_cessazione">Storno/Cessazione</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="apertura_sinistro">Apertura Sinistro</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="associa_pagamento">Associa metodo di pagamento</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="cessione">Cessione</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="variazione_temporanea">Variazione temporanea</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="gestisci_contatti">Gestiscri contatti telematica</li>
                            </template>
                            <template if:true={row.isEssig}>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="visualizzazione">Visualizzazione</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="variazione">Variazione</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="sostituzione">Sostituzione</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="apertura_sinistro">Apertura Sinistro</li>
                              <!--<li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="lavora">Lavora</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="visualizza">Visualizza</li>-->
                            </template>
                            <template if:true={row.isVita}>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="lavora">Lavora</li>
                              <li onclick={handleMenuClick} data-id={row.idPolizzaRecordId} data-action="visualizza">Visualizza</li>
                            </template>
                          </ul>
                        </div>
                      </template>
                    </div>
                  </template>
                </template>
              </div>
            </div>

            <template if:true={row.isExpanded}>
              <div class="accordion-details">
                <div class="accordion-details-content">
                  <template if:true={row.isLoadingDetails}>
                    <div class="accordion-spinner-wrapper">
                      <lightning-spinner alternative-text="Caricamento dettagli..." size="small"></lightning-spinner>
                    </div>
                  </template>

                  <template if:false={row.isLoadingDetails}>
                    <div class="nested-accordion">
                      <template for:each={row.sections} for:item="section">
                        <div key={section.id} class="nested-accordion-item">
                          <div class="nested-accordion-header" onclick={toggleSection} data-id={section.id}
                            data-rowid={row.id}>
                            <lightning-icon icon-name={section.iconName} size="x-small"
                              class="arrow-icon"></lightning-icon>
                            <span class="section-title">{section.title}</span>
                          </div>

                          <template if:true={section.isOpen}>
                            <div class="nested-accordion-body">
                              <div class="card-grid">
                                <template for:each={section.fields} for:item="field">
                                  <div key={field.label} class="card-item">
                                    <strong>{field.label}</strong>
                                    <span>{field.value}</span>
                                  </div>
                                </template>
                              </div>
                            </div>
                          </template>
                        </div>
                      </template>
                    </div>
                  </template>
                </div>
              </div>
              <template if:true={row.hasMotor}>
                <div class="slds-box slds-theme_default slds-m-around_large"
                  style="display: flex; flex-direction: column; align-items: flex-start;">
                  <div class="card-highlight">
                    <span class="slds-m-right_xx-small">
                      <img src={motorIcon} class="tab-icon static-icon" alt="Polizza" />
                    </span>
                    <span style="font-weight: bold; margin-right: 1rem;">Motor</span>
                    <a href="#" data-id="motor" onclick={handleOtherPolicyClick} class="polizza-link">
                      vai alla tab Motor per vedere questo ambito di protezione collegato
                    </a>
                  </div>
                </div>
              </template>
              <template if:true={row.hasCasaEFamiglia}>
                <div class="slds-box slds-theme_default slds-m-around_large"
                  style="display: flex; flex-direction: column; align-items: flex-start;">
                  <div class="card-highlight">
                    <span class="slds-m-right_xx-small">
                      <img src={casaIcon} class="tab-icon static-icon" alt="Polizza" />
                    </span>
                    <span style="font-weight: bold; margin-right: 1rem;">Casa e Famiglia</span>
                    <a href="#" data-id="casa" onclick={handleOtherPolicyClick} class="polizza-link">
                      vai alla tab Casa e Famiglia per vedere questo ambito di protezione collegato
                    </a>
                  </div>
                </div>
              </template>
              <template if:true={row.hasPersona}>
                <div class="slds-box slds-theme_default slds-m-around_large"
                  style="display: flex; flex-direction: column; align-items: flex-start;">
                  <div class="card-highlight">
                    <span class="slds-m-right_xx-small">
                      <lightning-icon icon-name="utility:user" size="x-small"
                        class="tab-icon custom-icon"></lightning-icon>
                    </span>
                    <span style="font-weight: bold; margin-right: 1rem;">Persona</span>
                    <a href="#" data-id="persona" onclick={handleOtherPolicyClick} class="polizza-link">
                      vai alla tab Persona per vedere questo ambito di protezione collegato
                    </a>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </template>
      </div>
    </template>

    <template lwc:if={isFlowModalOpened}>
        <section role="dialog" tabindex="-1" class="slds-modal slds-fade-in-open slds-modal_small">
            <div class="slds-modal__container">
                <header class="slds-modal__header">
                    <h2 class="slds-text-heading_medium">FEI</h2>
                    <button class="slds-button slds-button_icon slds-modal__close slds-button_icon-inverse" title="Chiudi" onclick={toggleFlowModal}>
                        <lightning-icon icon-name="utility:close" alternative-text="close" size="small"></lightning-icon>
                    </button>
                </header>        
                <div class="slds-modal__content slds-p-around_medium">    
                    <lightning-flow 
                        flow-api-name="FEIQuickAction" 
                        flow-input-variables={flowInputs} 
                        onstatuschange={handleFlowStatusChange}>
                    </lightning-flow>
                    </div>
                </div>
            </section>
            <div class="slds-backdrop slds-backdrop_open"></div>
    </template>


    <template if:false={isLoading}>
      <template if:false={rows.length}>
        <div class="no-data-message">Nessun prodotto disponibile per questa categoria.</div>
      </template>
    </template>

  </div>
</template>