import { LightningElement, track } from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { getDataHandler } from 'omnistudio/utility';
import { ShowToastEvent } from 'lightning/platformShowToastEvent';
import icon_unisalute from '@salesforce/resourceUrl/unica_unisalute';
import icons_ambiti from '@salesforce/resourceUrl/icons_ambiti';

export default class TabSelectorWithIcons extends OmniscriptBaseMixin(LightningElement) {
  @track selectedTab;
  @track recordId;
  @track tabs = [];
  @track showTable = true;
  @track rawData;
  @track activeAgencyData;
  @track isLoading = false;

  currentMenu = null;

  allTabs = [];

  connectedCallback() {
    this.waitUrlParams();
    document.addEventListener('click', this.handleOutsideClick.bind(this));
  }
  
  waitUrlParams() {
    const intervalId = setInterval(() => {
      const urlParams = new URLSearchParams(window.location.search);
      const selectedTabParam = urlParams.get('c__selectedTab');
      const recordIdParam = urlParams.get('c__recordId');
      const agencyParam = urlParams.get('c__agency');
  
      if (selectedTabParam || recordIdParam || agencyParam) {
        clearInterval(intervalId); // Blocco il controllo continuo
  
        this.selectedTab = selectedTabParam || 'tutti';
        this.recordId = recordIdParam || null;
        this.agency = agencyParam || null;
  
        this.callIntegrationProcedure();
      }
    }, 200); // Controlla ogni 200ms
  }
  

  disconnectedCallback() {
    document.removeEventListener('click', this.handleOutsideClick.bind(this));
  }

  callIntegrationProcedure() {
    this.isLoading = true;
    const inputMap = {
      AccountId: this.recordId,
      Agency: this.agency
    };
    const config = JSON.stringify({
      "type": "integrationprocedure",
      "value": {
        "ipMethod": "ProdottiAssicurativiDetails_GetData",
        "inputMap": JSON.stringify(inputMap)
      }
    });

    getDataHandler(config)
      .then(result => {
        // Verifica se il risultato è una stringa JSON e parsalo
        let parsedResult;
        try {
          parsedResult = typeof result === 'string' ? JSON.parse(result) : result;
        } catch (e) {
          this.isLoading = false;
          console.error('Errore parsing JSON:', e);
          this.showErrorToast('Errore nel parsing della risposta JSON.');
          return;
        }

        const data = parsedResult?.IPResult?.result;
        if (data) {
          this.rawData = data;
          this.isLoading = false;
        
          // Seleziona la sezione corretta: same_user_agency o other_agency
          this.activeAgencyData = data.same_user_agency || data.other_agency || {};
          this.updateTabsWithCounts(this.activeAgencyData);
        } else {
          console.warn('Struttura inattesa nella risposta:', parsedResult);
          this.isLoading = false;
          this.showErrorToast('Struttura inattesa nella risposta della Integration Procedure.');
        }
    })
    .catch(error => {
      console.error('Errore nella chiamata alla IP:', error);
      this.showErrorToast('Errore durante la chiamata alla Integration Procedure.');
    });
}


updateTabsWithCounts(agencyData) {
  const getCount = (section) => (section?.policies?.length || 0);

  this.allTabs = [
    {
      id: 'tutti',
      label: 'Tutti',
      count: this.sumAllPolicies(agencyData) || 0,
      icon: 'standard:all',
      isStaticIcon: false,
      iconClass: 'tab-icon static-icon'
    },
    {
      id: 'motor',
      label: 'Veicoli e Mobilità',
      count: getCount(agencyData.motor),
      icon: icons_ambiti + '/<EMAIL>',
      isStaticIcon: true,
      iconClass: 'tab-icon static-icon'
    },
    {
      id: 'casa',
      label: 'Casa e Famiglia',
      count: getCount(agencyData.casa_famiglia),
      icon: icons_ambiti + '/<EMAIL>',
      isStaticIcon: true,
      iconClass: 'tab-icon static-icon'
    },
    {
      id: 'persona',
      label: 'Persona',
      count: getCount(agencyData.persona),
      icon: icons_ambiti + '/<EMAIL>',
      isStaticIcon: true,
      iconClass: 'tab-icon static-icon'
    },
    /*{
      id: 'attivita',
      label: 'Attività',
      count: getCount(agencyData.attivita),
      icon: 'utility:touch_action',
      isStaticIcon: false
    },*/
    {
      id: 'vita',
      label: 'Vita',
      count: getCount(agencyData.vita),
      icon: icons_ambiti + '/<EMAIL>',
      isStaticIcon: true,
      iconClass: 'tab-icon static-icon'
    },
    {
      id: 'salute',
      label: '',
      count: getCount(agencyData.unisalute),
      icon: icon_unisalute + '/<EMAIL>',
      isStaticIcon: true,
      iconClass: 'tab-icon static-icon-logo'
    }
  ];

  this.updateTabs();
}

sumAllPolicies(agencyData) {
  return ['motor', 'casa_famiglia', 'persona', 'attivita', 'vita', 'unisalute']
    .reduce((acc, key) => acc + (agencyData[key]?.policies?.length || 0), 0);
}
  updateTabs() {
    const filteredTabs = this.allTabs.filter(tab => tab.count > 0 || tab.id === 'tutti');

    this.tabs = filteredTabs.map(tab => ({
      ...tab,
      class: tab.id === this.selectedTab ? 'tab active' : 'tab'
    }));
  }

  handleTabClick(event) {
    this.selectedTab = event.currentTarget.dataset.id;
    this.updateTabs();
    this.showTable = false;
    setTimeout(() => {
      this.showTable = true;
    }, 0);
  }

  get getPoliciesForSelectedTab() {
    if (!this.activeAgencyData) return [];
  
    const map = {
      motor: 'motor',
      casa: 'casa_famiglia',
      persona: 'persona',
      attivita: 'attivita',
      vita: 'vita',
      salute: 'unisalute'
    };
  
    if (this.selectedTab === 'tutti') {
      return Object.values(this.activeAgencyData)
        .flatMap(cat => Array.isArray(cat.policies) ? cat.policies : []);
    }
  
    const key = map[this.selectedTab];
    return this.activeAgencyData[key]?.policies || [];
  }

  handleChangeTab(event) {
    console.log('handleChangeTab', event.detail);
    const tabEvent = {
      currentTarget: {
        dataset: {
          id: event.detail.policyType
        }
      }
    }
    this.handleTabClick(tabEvent);
  }
  

  handleOpenMenu(event) {
    const { options } = event.detail;
    const container = this.template.querySelector('.parent-dropdown-portal');
    container.innerHTML = '';

    const triggerElement = event.currentTarget;
    const rect = triggerElement.getBoundingClientRect();

    const ul = document.createElement('ul');
    ul.classList.add('dropdown-menu-global');

    const topPosition = rect.bottom + 10;
    const windowHeight = window.innerHeight;
    const menuHeight = 300;

    if (topPosition + menuHeight > windowHeight) {
      ul.style.top = `${rect.top - 10 - menuHeight}px`;
    } else {
      ul.style.top = `${topPosition}px`;
    }

    ul.style.left = `${rect.left}px`;
    ul.style.zIndex = '100000';

    options.forEach(opt => {
      const li = document.createElement('li');
      li.textContent = opt.label;
      li.addEventListener('click', () => {
        container.innerHTML = '';
        this.currentMenu = null;
        opt.action();
      });
      ul.appendChild(li);
    });

    container.appendChild(ul);
    ul.classList.add('visible');
  }

  handleOutsideClick(event) {
    const portal = this.template.querySelector('.parent-dropdown-portal');
    if (portal && !portal.contains(event.target)) {
      portal.innerHTML = '';
      this.currentMenu = null;
    }
  }
  showErrorToast(message) {
    const toast = new ShowToastEvent({
      title: 'Errore',
      message: message,
      variant: 'error'
    });
    this.dispatchEvent(toast);
  }
}