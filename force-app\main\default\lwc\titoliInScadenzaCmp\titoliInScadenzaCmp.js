import { LightningElement, wire, track, api} from 'lwc';
import { OmniscriptBaseMixin } from 'omnistudio/omniscriptBaseMixin';
import { CurrentPageReference } from 'lightning/navigation';

import apexGetTitoliInScadenzaFromIntegrationProcedure from '@salesforce/apex/TitoliFeiPermissionHelper.getTitoliInScadenzaFromIntegrationProcedure';
import apexGetUserMandati from '@salesforce/apex/TitoliFeiPermissionHelper.getUserMandati';
import apexGetParamsForFei from '@salesforce/apex/TitoliFeiPermissionHelper.getParamsForFei';
import apexEnableActionsAsNeeded from '@salesforce/apex/TitoliFeiPermissionHelper.enableActionsAsNeeded';
import apexGetPrt from '@salesforce/apex/TitoliFeiPermissionHelper.getPrt';
import apexGetAccountData from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountData';
import apexGetAccountDetailsData from '@salesforce/apex/TitoliFeiPermissionHelper.getAccountDetailsData';
//import apexCreateBarcodeAndXML from '@salesforce/apex/TitoliFeiPermissionHelper.createBarcodeAndXML';


const actionsProdottoUnico = [
    { label: 'Completa', name: 'completa', disabled: false }, 
    { label: 'Incassa', name: 'incassa', disabled: false },
    { label: 'FEA', name: 'fea', disabled: true },
    { label: 'Firma', name: 'firma', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: true },
    //{ label: 'Vedi Documento', name: 'vediDocumento', disabled: true },
    //{ label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: true },
    //{ label: 'Incasso', name: 'Incasso', disabled: true },
    //{ label: 'Invio da remoto', name: 'invioDaremoto', disabled: true },
    { label: 'Stampa', name: 'stampa', disabled: false },
    //{ label: 'Sostituzione', name: 'sostituzione', disabled: true },
    //{ label: 'Variazione', name: 'variazione', disabled: true },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false }
    //{ label: 'Aggiungi ai preferiti', name: 'aggiungiAiPreferiti', disabled: true }
];

const actionsVecchiPortafogli = [
    { label: 'Incasso', name: 'incasso', disabled: false },
    { label: 'Incasso multiplo', name: 'incassoMultiplo', disabled: false },
    { label: 'Riprendi', name: 'riprendi', disabled: true },
    { label: 'Rettifica', name: 'rettifica', disabled: false },
    { label: 'Vedi Documento', name: 'vediDocumento', disabled: false },
    { label: 'Sostituzione', name: 'sostituzione', disabled: false },
    { label: 'Variazione', name: 'variazione', disabled: false },
    { label: 'Scheda QT', name: 'schedaQT', disabled: false },
    //{ label: 'Invio da Remoto', name: 'invioDaRemoto', disabled: false },
];

export default class TitoliInScadenzaCmp extends OmniscriptBaseMixin(LightningElement) {

    actionsProdottoUnico = actionsProdottoUnico;
    actionsVecchiPortafogli = actionsVecchiPortafogli;
    titoliInScadenzaColumn = [];
    titoliInScadenzaData = [];
    datatableRendered = false;
    isFlowModalOpened = false;
    isLWCModalOpened = false;
    isInvioRemotoModalOpened = false;

    labelLoadingTitoli = 'Caricamento titoli';
    showSpinner = false;

    societyNameToIdMap = new Map([['Unipol', "SOC_1"],['UniSalute', "SOC_4"]]);

    params = {
        feiId: "feiId",
        fiscalcode: "CF",
        feiRequestPayload: "{}",
        permissionSetName: "permissionSetName",
        society:"society"
    }

    @wire(CurrentPageReference)
    currentPageReference;

    @api accountId;
    accountData;
    accountDetailsData;

    connectedCallback() {

        if(this.currentPageReference?.state.c__AccountId != undefined){
            this.accountId = this.currentPageReference.state.c__AccountId;
        }

        this.getTitoliInScadenzaData();
        this.getUserMandati();
        this.getAccountData();
        this.getAccountDetailsData();
    }

    getTitoliInScadenzaData(){
        this.showSpinner = true;
        apexGetTitoliInScadenzaFromIntegrationProcedure({accountId : this.accountId})
        .then((result) => {
            this.titoliInScadenzaData = Array.isArray(result['Response']) ? result['Response'] : [result['Response']];
            //this.titoliInScadenzaData = JSON.parse('[{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"Multicanalità off","premio":25.48,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","modello":"-","isFEAVisualizzata":true,"contId":"****************","nFolder":"-","ambito":"Danni non auto","xquietanzaId":"*********","inviiRemoti":"0","isSostituzioneAttiva":true,"numeroAppendice":"0","agenzia":"1853","polizza":"*********","dataEffettoTitolo":"2025-06-29","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"*********","isRettificaAttiva":false,"targa":"-","dataScadenzaTitolo":"30-06-2025","operazione":"I","tipoDocumento":1000,"inviiAr":"0","firmata":"NO","rettificabile":"NO","emessaNonIncassabile":false,"compagnia":"Unipol","isIncassaAttiva":false,"unibox":0,"frazionamento":"MENSILE","polizzaPosizione":"077/*********","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"Multicanalità off","premio":25.48,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","modello":"-","isFEAVisualizzata":true,"contId":"****************","nFolder":"-","ambito":"Danni non auto","xquietanzaId":"*********","inviiRemoti":"0","isSostituzioneAttiva":true,"numeroAppendice":"0","agenzia":"1853","polizza":"*********","dataEffettoTitolo":"2025-05-30","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"*********","isRettificaAttiva":false,"targa":"-","dataScadenzaTitolo":"31-05-2025","operazione":"I","tipoDocumento":1000,"inviiAr":"0","firmata":"NO","rettificabile":"NO","emessaNonIncassabile":false,"compagnia":"Unipol","isIncassaAttiva":false,"unibox":0,"frazionamento":"MENSILE","polizzaPosizione":"077/*********","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"Multicanalità off","premio":25.48,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","modello":"-","isFEAVisualizzata":true,"contId":"****************","nFolder":"-","ambito":"Danni non auto","xquietanzaId":"*********","inviiRemoti":"0","isSostituzioneAttiva":true,"numeroAppendice":"0","agenzia":"1853","polizza":"*********","dataEffettoTitolo":"2025-04-29","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"*********","isRettificaAttiva":false,"targa":"-","dataScadenzaTitolo":"30-04-2025","operazione":"I","tipoDocumento":1000,"inviiAr":"0","firmata":"NO","rettificabile":"NO","emessaNonIncassabile":false,"compagnia":"Unipol","isIncassaAttiva":false,"unibox":0,"frazionamento":"MENSILE","polizzaPosizione":"077/*********","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true},{"isFEAAttiva":false,"isProdottoUnico":false,"isFirmaAttiva":false,"tipo":"Quietanza","omnicanalita":"Multicanalità off","premio":5.41,"isCompletaAttiva":true,"isIncassoAttiva":true,"isVariazioneAttiva":true,"titoliAlLegale":"NO","modello":"-","isFEAVisualizzata":true,"contId":"****************","nFolder":"-","ambito":"Danni non auto","xquietanzaId":"239391072","inviiRemoti":"0","isSostituzioneAttiva":true,"numeroAppendice":"0","agenzia":"1853","polizza":"204551224","dataEffettoTitolo":"2025-06-04","ramo":"077","prodottoAbilitatoInvioFea":true,"idDoc":"117176653","isRettificaAttiva":false,"targa":"-","dataScadenzaTitolo":"05-06-2025","operazione":"I","tipoDocumento":1000,"inviiAr":"0","firmata":"NO","rettificabile":"NO","emessaNonIncassabile":false,"compagnia":"Unipol","isIncassaAttiva":false,"unibox":0,"frazionamento":"MENSILE","polizzaPosizione":"077/204551224","isAbbinato":false,"Society":"","IsUnisalute":true,"IsUnipol":true}]'); //Guy De Roquefeuil: Mock for IP response
            console.log('titoliInScadenzaDataResult: '+JSON.stringify(this.titoliInScadenzaData));
            
            this.enableActionsAsNeeded();
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err getTitoli: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    getUserMandati(){
        apexGetUserMandati({accId: this.accountId})
        .then((result) => {
            this.showUnipolButton = result['hasMandatoUnipol']; //TODO: aggiungi custom permission
            this.showUniSaluteButton = result['hasMandatoUnisalute'];
        })
        .catch((error) => {
            //TODO: insert show toast
            console.log(JSON.stringify(error));
        });
    }

    getAccountData(){
        apexGetAccountData({accountId: this.accountId})
        .then(resultAcc => {
            this.accountData = resultAcc;
            console.log('accountData:  ' + JSON.stringify(resultAcc));
        })
    }

    getAccountDetailsData(){
        apexGetAccountDetailsData({accountId: this.accountId})
        .then(resultDetails => {
            this.accountDetailsData = resultDetails;
            console.log('detailsData: ' + JSON.stringify(resultDetails));
        })
    }

    getRowActions(row, doneCallback) {
        const matchingRow = this.titoliInScadenzaData.find(r => r.rowId === row.rowId);
        const actions = matchingRow?.rowActions || [];
        doneCallback(actions);
    }

    enableActionsAsNeeded(){
        this.datatableRendered = false;
        this.showSpinner = true;
        apexEnableActionsAsNeeded({actionsJSON: JSON.stringify(this.actionsProdottoUnico)})
        .then((result) => {
            let arrayTitoli = [];
            let rowsProdottoUnico = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico === true);
            rowsProdottoUnico.forEach(titolo => {

                let feaPresent = true;
                let feaAction = {};
                let firmaAction = {};
                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                currentRow.rowActions = result.map(action => ({ ...action }));

                currentRow.rowActions.forEach(action => {

                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'fea':
                            
                            if (currentRow?.tipo !== 'Quietanza') {
                                feaAction = action;
                                feaPresent = false;
                            } else {

                                if (currentRow?.isStampabileInFEA === false || currentRow?.prodottoAbilitatoInvioFea === false || currentRow?.inviiRemoti !== '0' || currentRow.inviiAr !== '0')  {
                                    action.disabled = true;
                                }
                            }
                                    
                            break;
                        
                        case 'completa':
                            if (currentRow?.isCompletaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'R' || (currentRow?.firmata === 'Firmato' && (currentRow?.emessaNonIncassabile === false || (currentRow?.frazionamento !== undefined && currentRow?.frazionamento.toLowerCase() !== 'annuale'))) || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)){
                                action.disabled = true;
                            }
                            */
                            break;
                    
                        case 'incassa':
                            
                            if (currentRow?.isIncassaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if(currentRow?.operazione === 'R' || currentRow?.firmata === 'Non firmato' || currentRow?.emessaNonIncassabile === true){
                                action.disabled = true;
                            }
                            */
                            break;

                        case 'rettifica':
                           
                            if (currentRow?.isRettificaAttiva !== true) {
                                action.disabled = true;
                            }
                            /*
                            if (currentRow?.operazione === 'I') {
                                action.disabled = true;
                            }
                            */
                            break;
                        
                        case 'firma':

                            if (feaPresent === true) {
                                firmaAction = action;
                            } else {
                                if (currentRow?.isFirmaAttiva !== true) {
                                    action.disabled = true;
                                }
                                /*
                                if (currentRow?.firmata === 'Firmato' || (currentRow?.idContrattoPTF === null || currentRow?.idContrattoPTF === undefined)) {
                                    action.disabled = true;
                                }
                                */
                            }
                            break;

                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                if (feaPresent == false) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(feaAction), 1);
                } else if (feaPresent == true) {
                    currentRow.rowActions.splice(currentRow.rowActions.indexOf(firmaAction), 1);
                }

                arrayTitoli.push(currentRow);
            });

            let rowsVecchiPortafogli = this.titoliInScadenzaData.filter(prodottoUnicoRow => prodottoUnicoRow.isProdottoUnico !== true);
            this.titoliInScadenzaData = [];
            rowsVecchiPortafogli.forEach(titolo => {

                let currentRow = { ...titolo };

                currentRow.rowId = `${currentRow.nFolder || ''}_${currentRow.agenzia || ''}_${Math.random().toString(36).substring(2, 8)}`;
                if(currentRow.polizza != null && currentRow.polizza != undefined){
                    currentRow.polizza = currentRow.polizza.length > 1 ? currentRow.polizza.toString().replaceAll(',', '-') : currentRow.polizza.toString();
                    currentRow.ramoPolizza = currentRow.ramo != undefined ? currentRow.ramo + '/' + currentRow.polizza : currentRow.polizza;
                }
                if (currentRow.esposizioneAR != null && currentRow.esposizioneAR != undefined) {
                    let esposizioneArray = currentRow.esposizioneAR.split('-');
                    currentRow.esposizioneAR = esposizioneArray[2] + '-' +  esposizioneArray[1] + '-' +  esposizioneArray[0];
                }
                console.log('actions vecchiP: ' + JSON.stringify(this.actionsVecchiPortafogli));
                currentRow.rowActions = this.actionsVecchiPortafogli.map(action => ({ ...action }));
                console.log('row actions vecchi: ' + JSON.stringify(currentRow.rowActions));
                currentRow.rowActions.forEach(action => {

                    if (action.disabled === true && (action.name !== 'fea' || (action.name !== 'firma' && feaPresent === false))) {
                        return;
                    }

                    switch (action.name) {
                        case 'incasso':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'incassoMultiplo':

                            if (currentRow?.isIncassoAttiva !== true) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'rettifica':
                            if (currentRow?.isRettificaAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'schedaQT':
                                
                            if ((currentRow?.uplCrmQuietId === null || currentRow?.uplCrmQuietId  === undefined) || (currentRow?.contId === null || currentRow.contId === undefined)) {
                                action.disabled = true;
                            }
                            break;
                        
                        case 'vediDocumento':
                                
                            if (currentRow?.isVediDocumentoAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        case 'sostituzione':
                                
                            if (currentRow?.isSostituzioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;
                        
                        case 'variazione':
                                
                            if (currentRow?.isVariazioneAttiva !== true) {
                                
                                action.disabled = true;
                            }
                            break;

                        default:
                            break;
                    }
                    
                });

                arrayTitoli.push(currentRow);
            });

            console.log('titoliInScadenzaData post lavorazione: ' + JSON.stringify(arrayTitoli));
            this.titoliInScadenzaData = [...arrayTitoli];
            this.titoliInScadenzaColumn = [
                { label: 'Società', fieldName: 'compagnia' },
                { label: 'Tipo', fieldName: 'tipo' },
                { label: 'Ambito', fieldName: 'ambito' },
                { label: 'N. Folder', fieldName: 'nFolder' },
                { label: 'N. Polizza/Posizione', fieldName: 'ramoPolizza' },
                { label: 'Scadenza', fieldName: 'dataScadenzaTitolo' },
                { label: 'Frazionamento', fieldName: 'frazionamento' },
                { label: 'Premio (comprensivo di Unibox)', fieldName: 'premio', type: 'currency' },
                { label: 'di cui Unibox', fieldName: 'unibox', type: 'currency' },
                { label: 'Omnicanalità', fieldName: 'omnicanalita' },
                { label: 'Esposizione AR', fieldName: 'esposizioneAR', cellAttributes: { alignment: 'center' }},
                { label: 'Targa', fieldName: 'targa' },
                { label: 'Modello', fieldName: 'modello' },
                { label: 'Titoli al legale', fieldName: 'titoliAlLegale' },
                { label: 'Rettificabile', fieldName: 'rettificabile' },
                { label: 'Firmato', fieldName: 'firmata' },
                { type: 'action', typeAttributes: { rowActions: this.getRowActions.bind(this), menuAlignment: 'auto'} }
            ];
            this.datatableRendered = true;
            this.showSpinner = false;
        })
        .catch((error) => {
            console.log('err in ROWS: ' + JSON.stringify(error));
            this.showSpinner = false;
        });
    }

    handleStampe(event){
         const feiId = event.currentTarget.dataset.feiId;
        const society = event.currentTarget.dataset.societyId;
        this.flowInputs = [
            { name: 'FEIID', type: 'String', value: feiId},
            { name: 'recordId', type: 'String', value: this.accountId },
            { name: 'society', type: 'String', value: society }         
        ];
        this.toggleFlowModal();
    }

    toggleFlowModal(){
        this.isFlowModalOpened = !this.isFlowModalOpened;
    }

    toggleLWCModal(){
        this.isLWCModalOpened = !this.isLWCModalOpened;
    }

    handleFlowStatusChange(event) {
        if (event.detail.status === 'FINISHED' || event.detail.status === 'ERROR')
           this.toggleFlowModal();
    }

    handleRowAction(event){
        const actionName = event.detail.action.name;
        const row = event.detail.row;
        console.log("actionName: " + actionName);
        switch (actionName) {
        case 'completa': {
                let feiId = 'PU.COMPLETA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'incassa': {
                let feiId = 'PU.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({agencyCode: '{$agenzia}', companyCode: '{$compagnia}', folderId: row.nFolder}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'firma': {
                let feiId = 'PU.FIRMA';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({contractid: row.idContrattoPTF}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'rettifica': {

                let feiId = row.isProdottoUnico == true ? 'PU.RETTIFICA' : 'CP.INCASSO';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {

                    if (feiId === 'PU.RETTIFICA') {
                        
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({agencyCode: '{$agenzia}', companyCode: '{$compagnia}', folderId: row.nFolder}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };

                    } else if(feiId === 'CP.INCASSO'){
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({operazione: 'R', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'schedaQT': {
                let feiId = 'SCHEDA.QT';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({xqt: row.uplCrmQuietId, contid: row.contId, agency: row.agenzia}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    };
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                });
                break;
            }
            case 'stampa': {
                let stampaParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                stampaParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                console.log('uuid: '+uuid+'\n');
                let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                stampaParams.body = JSON.stringify({
                    filtroStampaQtzListaDocumentiDaStampare: {
                        ristampa: true,
                        fromFe: true,
                        motRistampaTpCd: "-1",
                        testoMotRistampa: "Stampa precedente rovinata",
                        flagAnteprima: false,
                        flagGeneraDocumenti: false,
                        listaDocumentiDaStampare: [
                            {
                                idQt: row?.uplCrmQuietId,
                                tipoDocumento: tipoDocumento,
                                idDocumento: row?.idDoc
                            }
                        ],
                        docTpCd: row?.tipoDocumento,
                        guid: uuid,
                        agenziaFiglia: row?.agenzia,
                        agenziaMadre: row?.agenziaMadre,
                        compagnia: compagniaCode
                    }
                });
                console.log(`stampaParams: ${JSON.stringify(stampaParams)}`);
                apexGetPrt({inputParams: stampaParams})
                .then(resultPrt => {

                    const feiId = 'SFEA.PRINT.PAPER';
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feiRequest: {
                                    requestCode: "SFEA.PRINT.PAPER",
                                    feiRequest: {
                                        feasRequest: {
                                            mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente',
                                            srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                            srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                            cmp: compagniaCode,
                                            prj: "LEON",
                                            app: "SFCRM",
                                            finalPrint: false,
                                            saveStatus: false,
                                            sendMail: false,
                                            sendSms: false,
                                            keys: {
                                                NARC: {
                                                    value: row.numeroArchivio,
                                                    name: "NARC"
                                                },
                                                SUBAG: {
                                                    value: row.subAgenzia,
                                                    name: "SUBAG"
                                                },
                                                CODFIS: {
                                                    value: this.accountData.ExternalId__c,
                                                    name: "CODFIS"
                                                },
                                                NPOL: {
                                                    value: row.polizza,
                                                    name: "NPOL"
                                                },
                                                AGEN: {
                                                    value: row.agenzia,
                                                    name: "AGEN"
                                                },
                                                COMP: {
                                                    value: compagniaCode,
                                                    name: "COMP"
                                                },
                                                MSGID: {
                                                    value: uuid
                                                },
                                                BTCID: {
                                                    value: "15596523", //-> da valorizzare con idQueue ritornato dal servizio recuperaPrt/motiviRistampa, se non già presente nella response originaria
                                                    name: "BTCID"
                                                },
                                                DOCID: {
                                                    "value": resultPrt?.idQueue, //-> da valorizzare con campo idDoc da output servizio titoli
                                                    name: "DOCID"
                                                },
                                                ITEMID: {
                                                    "value": row.docItemId,
                                                    name: "ITEMID"
                                                }
                                            },
                                            descriptors: [
                                                {
                                                    type: "7266",
                                                    descriptor: "prt0",
                                                    keys: {
                                                        COMP: {
                                                            value: compagniaCode,
                                                            name: "COMP"
                                                        },
                                                        NPOL: {
                                                            value: row.polizza,
                                                            name: "NPOL"
                                                        },
                                                        NARC: {
                                                            value: row.numeroArchivio,
                                                            name: "NARC"
                                                        },
                                                        NAPP: {
                                                            value: row.numeroAppendice,
                                                            name: "NAPP"
                                                        },
                                                        TIPOP: {
                                                            value: row.tipoOp,
                                                            name: "TIPOP"
                                                        },
                                                        EFFVAR: {
                                                            value: row.dtEffVar,
                                                            name: "EFFVAR"
                                                        },
                                                        EFFTIT: {
                                                            value: row.dataEffettoTitolo,
                                                            name: "EFFTIT"
                                                        }
                                                    }
                                                }
                                            ],
                                            signTyp: "A",
                                        }
                                    },
                                    requestId: "SFEA.PRINT.PAPER-1747294655933"
                                },
                                feiAddress: {
                                    hostpoint: 'https://essig-inte.unipolsai.it',
                                    entrypoint: "",
                                    endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                    identity: "SFCRM"
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        };
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    });
                }).catch(error => {
                    console.error('Error retrieving PRT:', JSON.stringify(error));
                })

                break;
            }

            case 'fea': {
                let feaParams = this.getDefaultFEAParams(row);
                feaParams.prtForPrint = false;

                apexGetPrt({inputParams: feaParams})
                .then(result => {
                    console.log('PRT retrieved successfully:', result);
                        this.executeFeaFei(result, row);
                }).catch(error => {
                        console.error('Error retrieving PRT:', JSON.stringify(error));
                })
                break;
            }

            case 'incasso': {
                let feiId = 'CP.INCASSO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Numpol: row.polizza, w_Ramopo: row.ramo}),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })

                break;
            }
            case 'incassoMultiplo': {
                let feiId = 'CP.INCASSO.MULTIPLO';
                console.log('dataEffettoTitolo: ' + row.dataEffettoTitolo);
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({operazione: 'I', w_Codage: row.agenzia, w_Compag: compagniaCode, w_Data: row.dataEffettoTitolo, w_Codcl: this.accountData.ExternalId__c }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'sostituzione': {
                let feiId = row.ambito === 'Auto' ? 'NPAC.SOSTITUZIONE.POLIZZA' : 'RE.SOSTITUZIONE.POLIZZA';
                let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            agenzia: row.agenzia, 
                            agenziaSostituita: row.agenzia,
                            codiceFiscale: this.accountData.ExternalId__c,
                            compagnia: compagniaCode, //da capire dove prendere nuovo valore compagnia per la sostituzione
                            compagniaSostituita: compagniaCode,
                            polizzaSostituita: row.polizza, // verifica se soltanto primo numero polizza
                            ramoSostituita: row.ramo,
                            subAgenzia: row.subAgenzia,
                            tipoOperazione: "SO",
                            prodotto: "", // da capire dove prendere valore prodotto
                            
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'variazione': {
                let feiId = row.ambito === 'Auto' ? 'RA.VARIAZIONE.POLIZZA' : 'RE.VARIAZIONE.POLIZZA';
                let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            agenzia: row.agenzia, 
                            agenziaSostituita: row.agenzia,
                            codiceFiscale: this.accountData.ExternalId__c,
                            compagnia: compagniaCode,
                            compagniaSostituita: compagniaCode,
                            polizzaSostituita: row.polizza,
                            ramoSostituita: row.ramo,
                            subAgenzia: row.subAgenzia,
                            tipoOperazione: "VA",
                            prodotto: "", // da capire dove prendere valore prodotto
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }
            case 'vediDocumento': {
                let feiId = 'SFEA.PRINT.DRAFT';
                let vediDocParams = this.getDefaultFEAParams(row);
                console.log('row selected: ' +JSON.stringify(row));
                vediDocParams.prtForPrint = true;
                let uuid = self.crypto.randomUUID();
                let tipoDocumento = row.tipo != undefined && row.tipo == 'Quietanza' ? "Q" : row.tipo != undefined && row.tipo == "Regolazione Premio" ? "QTREG" : "";
                vediDocParams.body = JSON.stringify({
                    filtroStampaQtzListaDocumentiDaStampare: {
                        ristampa: true,
                        fromFe: true,
                        motRistampaTpCd: "-1",
                        testoMotRistampa: "Stampa precedente rovinata",
                        flagAnteprima: false,
                        flagGeneraDocumenti: false,
                        listaDocumentiDaStampare: [
                            {
                                idQt: row?.uplCrmQuietId,
                                tipoDocumento: tipoDocumento,
                                idDocumento: row?.idDoc
                            }
                        ],
                        docTpCd: row?.tipoDocumento,
                        guid: uuid,
                        agenziaFiglia: row?.agenzia,
                        agenziaMadre: row?.agenziaMadre,
                        compagnia: row?.compagnia
                    }
                });
                vediDocParams.vediDocumento = true;
                apexGetPrt({inputParams: feaParams})
                .then(result => {
                    console.log('resulrPRT: ' + JSON.stringify(result));
                    
                    apexGetParamsForFei({feiId: feiId})
                    .then(result => {
                        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                        this.params = {
                            feiId: feiId,
                            feiRequestPayload: JSON.stringify({
                                feasRequest: {
                                    mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                    srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                    cmp: compagniaCode,
                                    prj: 'LEON', //da confermare
                                    app: 'SFCRM', //statico
                                    finalPrint: false, //da confermare
                                    saveStatus: false, //da confermare
                                    sendMail: false, //da confermare
                                    sendSms: false, //da confermare
                                    watermarkTyp: 'QUIET', //da confermare
                                    keys: {
                                        NARC: {
                                            value: row.numeroArchivio, //da confermare
                                            name: "NARC"
                                        },
                                        SUBAG: {
                                            value: row.subAgenzia, //da confermare
                                            name: "SUBAG"
                                        },
                                        CODFIS: {
                                            value: this.accountData.ExternalId__c,
                                            name: "CODFIS"
                                        },
                                        RAM: {
                                            value: row.ramo,
                                            name: "RAM"
                                        },
                                        NPOL: {
                                            value: row.polizza,
                                            name: "NPOL"
                                        },
                                        NTRG: {
                                            value: row.targa,
                                            name: "NTRG"
                                        },
                                        AGEN: {
                                            value: row.agenzia,
                                            name: "AGEN"
                                        },
                                        COMP: {
                                            value: compagniaCode,
                                            name: "COMP"
                                        },
                                        MSGID: {
                                            value: uuid, //UUID generato per recuperaPrt
                                            name: "MSGID"
                                        },
                                        BTCID: {
                                            value: resultPrt.idQueue, //da mappare con idQueue risposta del servizio recuperaPrt
                                            name: "STCID"
                                        },
                                        DOCID: {
                                            value: row.idDoc,
                                            name: "DOCID"
                                        },
                                        ITEMID: {
                                            value: row.docItemId,
                                            name: "ITEMID"
                                        },
                                    }
                                }
                            }),
                            fiscalcode: result["fiscalCode"] || "CF",
                            permissionSetName: result["permissionSetName"] || "",
                            society: this.societyNameToIdMap.get(row.compagnia) || ''
                        }
                        console.log("params: "+JSON.stringify(this.params));
                        this.toggleLWCModal();
                    })
                }).catch(error => {
                    console.log(JSON.stringify(error));
                })
                break;
            }
            case 'riprendi': {
                let feiId = 'SFEA.VIEW.DASHBOARD';
                apexGetParamsForFei({feiId: feiId})
                .then(result => {
                    let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
                    this.params = {
                        feiId: feiId,
                        feiRequestPayload: JSON.stringify({
                            feasRequest: {
                                mnuAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente',
                                srcAppBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page',
                                srcAppFailBckUrl: 'https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/courtesy-page', //rotta da definire
                                keys: {
                                    AGEN: {
                                        value: row.agenzia,
                                        name: "AGEN"
                                    },
                                    COMP: {
                                        value: compagniaCode,
                                        name: "COMP"
                                    },
                                    RAM: {
                                        value: row.ramo,
                                        name: "RAM"
                                    },
                                    NPOL: {
                                        value: row.polizza,
                                        name: "NPOL"
                                    },
                                    TIPOP: {
                                        value: row.tipoOp,
                                        name: "TIPOP"
                                    }
                                }
                            }
                        }),
                        fiscalcode: result["fiscalCode"] || "CF",
                        permissionSetName: result["permissionSetName"] || "",
                        society: this.societyNameToIdMap.get(row.compagnia) || ''
                    }
                    console.log("params: "+JSON.stringify(this.params));
                    this.toggleLWCModal();
                })
                break;
            }

            case 'invioDaremoto': {

                //da aggiungere integration procedure per chiamate ai servizi: /abilitazionePostalizzatore
                //e /print/motiviRistampaCat=BASE

                //dopo ricezione dati
                //apertura modale
                break;
            }

            default:
                console.warn('Azione non riconosciuta:', actionName);
                this.params = null;
        }
    }

    executeFeaFei(params, row){
        let compagniaCode = row.compagnia.toLowerCase() === 'unipol' ? 1 : row.compagnia.toLowerCase() === 'unisalute' ? 4 : null;
        if (params.FEI == 'requestSign'){
            let feiId = 'SFEA.SIGN';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify({
                        feiRequest: {
                            requestCode: "SFEA.SIGN",
                            feiRequest: {
                                feasRequest: {
                                    mnuAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app//courtesy-page",
                                    srcAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app//courtesy-page",
                                    srcAppFailBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page", //rotta da definire
                                    cmp: compagniaCode,
                                    prj: "LEON",
                                    app: "SFCRM",
                                    signTyp: "M",
                                    contactTyp: "F",
                                    channelTyp: "AGZ",
                                    saveStatus: true,
                                    finalPrint: false,
                                    sendMail: true,
                                    sendSms: true,
                                    sendCash: true,
                                    keys: {
                                        NUMTIT: {
                                            value: row.idTitolo,
                                            name: "NUMTIT"
                                        },
                                        ICTR: {
                                            value: row.ITCTValue,
                                            name: "ICTR"
                                        },
                                        NFLD: {
                                            value: row.nFolder,
                                            name: "NFLD"
                                        },
                                        NAPP: {
                                            value: row.numeroAppendice,
                                            name: "NAPP"
                                        },
                                        TIPOP: {
                                            value: row.tipoOp,
                                            name: "TIPOP"
                                        },
                                        CIUID: {
                                            value: compagniaCode === 1 ? accountDetailsData.Unipol.SourceSystemIdentifier__c : compagniaCode === 4 ? this.accountDetailsData.UniSalute.SourceSystemIdentifier__c : '',
                                            name: "CIUID"
                                        },
                                        INIVAL: {
                                            value: row.dataEffettoPolizza,
                                            name: "INIVAL"
                                        },
                                        EFFTIT: {
                                            value: row.dataEffettoTitolo,
                                            name: "EFFTIT"
                                        },
                                        EFFVAR: {
                                            value: row.dtEffVar,
                                            name: "EFFVAR"
                                        },
                                        NVER: {
                                            value: "1",
                                            name: "NVER"
                                        },
                                        DATRIF: {
                                            value: new Date().toISOString(),
                                            name: "DATRIF"
                                        },
                                        FAS: {
                                            value: "VEN",
                                            name: "FAS"
                                        },
                                        JRN: {
                                            value: "AGE",
                                            name: "JRN"
                                        },
                                        INOUT: {
                                            value: "OUTBOUND",
                                            name: "INOUT"
                                        },
                                        TIPFIRM: {
                                            value: "FEA",
                                            name: "TIPFIRM"
                                        },
                                        PRSFIRM: {
                                            value: "SI",
                                            name: "PRSFIRM"
                                        },
                                        FRMDOC: {
                                            value: "DIG",
                                            name: "FRMDOC"
                                        },
                                        UMOD: {
                                            value: "Online",
                                            name: "UMOD"
                                        },
                                        USYS: {
                                            value: "DO",
                                            name: "USYS"
                                        },
                                        STAT: {
                                            value: "VLD",
                                            name: "STAT"
                                        },
                                        SMSSNDKEY: {
                                            "value": "12c389e3-b692-46cc-bf35-c4776be4c702", //-> da verificare e/o sostituire con chiave corretta per SF
                                            "name": "SMSSNDKEY"
                                        },
                                        MAILSNDKEY: {
                                            value: "4867dbb1-d49c-405f-ba97-326800f06901", //-> da verificare e/o sostituire con chiave corretta per SF
                                            name: "MAILSNDKEY"
                                        },
                                        NARCFLD: {
                                            value: row.numeroArchivioProdottoUnico,
                                            name: "NARCFLD"
                                        },
                                        NPOLFLD: {
                                            value: row.NPOLFLDValue,
                                            name: "NPOLFLD"
                                        },
                                        NARC: {
                                            value: row.numeroArchivio,
                                            name: "NARC"
                                        },
                                        SUBAG: {
                                            value: row.subagenzia,
                                            name: "SUBAG"
                                        },
                                        CODFIS: {
                                            value: this.accountData.ExternalId__c,
                                            name: "CODFIS"
                                        },
                                        RAM: {
                                            value: row.ramo,
                                            name: "RAM"
                                        },
                                        NPOL: {
                                            value: row.polizza,
                                            name: "NPOL"
                                        },
                                        AGEN: {
                                            value: row.agenzia,
                                            name: "AGEN"
                                        },
                                        COMP: {
                                            value: compagniaCode,
                                            name: "COMP"
                                        }
                                    },
                                    descriptors: [
                                        {
                                            type: "POL",
                                            descriptor: "prt0",
                                            keys: {
                                                NFLD: {
                                                    value: row.nFolder,
                                                    name: "NFLD"
                                                },
                                                COMP: {
                                                    value: compagniaCode,
                                                    name: "COMP"
                                                },
                                                NPOL: {
                                                    value: row.polizza,
                                                    name: "NPOL"
                                                },
                                                NARC: {
                                                    value: row.numeroArchivio,
                                                    name: "NARC"
                                                },
                                                NAPP: {
                                                    value: row.numeroAppendice,
                                                    name: "NAPP"
                                                },
                                                TIPOP: {
                                                    value: row.tipoOp,
                                                    name: "TIPOP"
                                                },
                                                EFFVAR: {
                                                    value: row.dtEffVar,
                                                    name: "EFFVAR"
                                                },
                                                EFFTIT: {
                                                    value: row.dataEffettoTitolo,
                                                    name: "EFFTIT"
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            requestId: "SFEA.SIGN-1747298088710"
                            },
                            feiAddress: {
                                hostpoint: "https://essig-inte.unipolsai.it",
                                entrypoint: "",
                                endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                identity: "WKCS"
                            }
                        }
                    ), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || "",
                    society: this.societyNameToIdMap.get(row.compagnia) || ''
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
            return;
        }
        if (params.FEI == 'resumeSign'){
            let feiId = 'SFEA.SIGN.DASHBOARD';
            apexGetParamsForFei({feiId: feiId})
            .then(result => {
                let feiRequestPayload;
                if (row.isProdottoUnico === true) {
                    feiRequestPayload = JSON.stringify(
                        {
                            feiRequest: {
                                requestCode: "SFEA.SIGN.DASHBOARD",
                                feiRequest: {
                                    feasRequest: {
                                        mnuAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente",
                                        srcAppBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page",
                                        srcAppFailBckUrl: "https://essig-inte.unipolsai.it/WorkspaceWeb/app/courtesy-page", //rotta da definire
                                        keys: {
                                            AGEN: {
                                                value: row.agenzia,
                                                name: "AGEN"
                                            },
                                            COMP: {
                                                value: compagniaCode,
                                                name: "COMP"
                                            },
                                            NPOL: {
                                                value: row.polizza,
                                                name: "NPOL"
                                            },
                                            TIPOP: {
                                                value: row.tipoOp,
                                                name: "TIPOP"
                                            },
                                            NAPP: {
                                                value: row.numeroAppendice,
                                                name: "NAPP"
                                            },
                                            EFFTIT: {
                                                value: row.dataEffettoTitolo,
                                                name: "EFFTIT"
                                            },
                                            MODFIRM: {
                                                value: "M",
                                                name: "MODFIRM"
                                            },
                                            TIPRIC: {
                                                value: "10",
                                                name: "TIPRIC"
                                            },
                                            RETFEISRC: {
                                                value: "true",
                                                name: "RETFEISRC"
                                            }
                                        },
                                    },
                                    "requestId": "SFEA.SIGN.DASHBOARD-1747297517069"
                                },
                                feiAddress: {
                                    hostpoint: "https://essig-inte.unipolsai.it",
                                    entrypoint: "",
                                    endpoint: "/WorkspaceWeb/app/crm/scheda_cliente",
                                    identity: "WKCS"
                                }   
                            }
                        }
                    )
                } else {
                    feiRequestPayload = JSON.stringify(
                        {
                            feiRequest: {
                                requestCode: "SFEA.VIEW.DASHBOARD",
                                feiRequest: {
                                    feasRequest: {
                                        mnuAppBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/crm/scheda_cliente",
                                        srcAppBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/courtesy-page",
                                        srcAppFailBckUrl: "https://essig-form.unipolsai.it/WorkspaceWeb/app/courtesy-page", // rotta da definire
                                        keys: {
                                            AGEN: {
                                                value: row.agenzia,
                                                name: "AGEN"
                                            },
                                            COMP: {
                                                value: compagniaCode,
                                                name: "COMP"
                                            },
                                            RAM: {
                                                value: row.ramo,
                                                "name": "RAM"
                                            },
                                            NPOL: {
                                                value: row.polizza,
                                                name: "NPOL"
                                            },
                                            TIPOP: {
                                                value: row.tipoOp,
                                                name: "TIPOP"
                                            }
                                        },
                                        
                                    }
                                },
                                "requestId": "SFEA.VIEW.DASHBOARD-1752650554358"
                            },
                            feiAddress: {
                                hostpoint: "https://essig-form.unipolsai.it",
                                entrypoint: "",
                                endpoint: "/WorkspaceWeb/app/crm/scheda_cliente", //-> indirizzo pagina SF
                                identity: "WKCS"
                            }
                        }

                    )
                }
                this.params = {
                    feiId: feiId,
                    feiRequestPayload: JSON.stringify(feiRequestPayload), //TODO: metti json completo
                    fiscalcode: result["fiscalCode"] || "CF",
                    permissionSetName: result["permissionSetName"] || "",
                    society: this.societyNameToIdMap.get(row.compagnia) || ''
                };
                console.log("params: "+JSON.stringify(this.params));
                this.toggleLWCModal();
            }).catch(error => {
                console.log('Error retrieving parameters for FEA:' + JSON.stringify(error));
                }
            );
        }
    }

    getDefaultFEAParams(row) {
        return {
            idDoc: row.idDoc,
            ramo: row.ramo,
            polizza: row.polizza,
            polizzaProdottoUnico: row.isProdottoUnico,
            numeroAppendice: row.numeroAppendice,
            dataEffettoTitolo: row.dataEffettoTitolo,
            prtForPrint: false,
            vediDocumento: false
        };

    }
}