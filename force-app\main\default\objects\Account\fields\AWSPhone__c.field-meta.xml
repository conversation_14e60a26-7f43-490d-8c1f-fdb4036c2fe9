<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AWSPhone__c</fullName>
    <externalId>false</externalId>
    <formula>IF(
  ISBLANK(Phone),
  &quot;&quot;,
  IF(
    AND(
      BEGINS(SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;), &quot;+39&quot;),
      LEN(SUBSTITUTE(SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;),&quot;+39&quot;,&quot;&quot;)) &gt;= 8,
      LEN(SUBSTITUTE(SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;),&quot;+39&quot;,&quot;&quot;)) &lt;= 15
    ),
    SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;),
    IF(
      AND(
        LEN(SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;)) &gt;= 8,
        LEN(SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;)) &lt;= 15
      ),
      &quot;+39&quot; &amp; SUBSTITUTE(Phone,&quot; &quot;,&quot;&quot;),
      &quot;&quot; 
    )
  )
)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>AWSPhone</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
