<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>Sottoesito__c</fullName>
    <label>Sottoesito</label>
    <required>false</required>
    <trackFeedHistory>false</trackFeedHistory>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Picklist</type>
    <valueSet>
        <controllingField>Esito__c</controllingField>
        <restricted>true</restricted>
        <valueSetDefinition>
            <sorted>false</sorted>
            <value>
                <fullName>Altro</fullName>
                <default>false</default>
                <label>Altro</label>
            </value>
            <value>
                <fullName>Per modifica</fullName>
                <default>false</default>
                <label>Per modifica</label>
            </value>
            <value>
                <fullName>Per acquisto</fullName>
                <default>false</default>
                <label>Per acquisto</label>
            </value>
            <value>
                <fullName>Calcola quotazione</fullName>
                <default>false</default>
                <label>Calcola quotazione</label>
            </value>
            <value>
                <fullName>Nessuna quotazione calcolata</fullName>
                <default>false</default>
                <label>Nessuna quotazione calcolata</label>
            </value>
            <value>
                <fullName>Appuntamento in presenza</fullName>
                <default>false</default>
                <label>Appuntamento in presenza</label>
            </value>
            <value>
                <fullName>Appuntamento da remoto</fullName>
                <default>false</default>
                <label>Appuntamento da remoto</label>
            </value>
            <value>
                <fullName>Interessato anche ad altri prodotti</fullName>
                <default>false</default>
                <label>Interessato anche ad altri prodotti</label>
            </value>
            <value>
                <fullName>Il cliente passa/chiama in autonomia</fullName>
                <default>false</default>
                <label>Il cliente passa/chiama in autonomia</label>
            </value>
            <value>
                <fullName>Pagamento in autonomia da remoto</fullName>
                <default>false</default>
                <label>Pagamento in autonomia da remoto</label>
            </value>
            <value>
                <fullName>Richiesta premio</fullName>
                <default>false</default>
                <label>Richiesta premio</label>
            </value>
            <value>
                <fullName>Richieste modifiche al preventivo/polizza</fullName>
                <default>false</default>
                <label>Richieste modifiche al preventivo/polizza</label>
            </value>
            <value>
                <fullName>Presenza offerta altra Compagnia</fullName>
                <default>false</default>
                <label>Presenza offerta altra Compagnia</label>
            </value>
            <value>
                <fullName>Richiesta informazioni aggiuntive</fullName>
                <default>false</default>
                <label>Richiesta informazioni aggiuntive</label>
            </value>
            <value>
                <fullName>Info su polizze di altri prodotti</fullName>
                <default>false</default>
                <label>Info su polizze di altri prodotti</label>
            </value>
            <value>
                <fullName>Richiesta altra modalità  di pagamento</fullName>
                <default>false</default>
                <label>Richiesta altra modalità  di pagamento</label>
            </value>
            <value>
                <fullName>Acquisto/preventivo altra Compagnia</fullName>
                <default>false</default>
                <label>Acquisto/preventivo altra Compagnia</label>
            </value>
            <value>
                <fullName>Prezzo troppo alto</fullName>
                <default>false</default>
                <label>Prezzo troppo alto</label>
            </value>
            <value>
                <fullName>Problematiche sinistri</fullName>
                <default>false</default>
                <label>Problematiche sinistri</label>
            </value>
            <value>
                <fullName>Bene non assicurabile</fullName>
                <default>false</default>
                <label>Bene non assicurabile</label>
            </value>
            <value>
                <fullName>Optout</fullName>
                <default>false</default>
                <label>Optout</label>
            </value>
            <value>
                <fullName>Copertura già  presente con altra compagnia</fullName>
                <default>false</default>
                <label>Copertura già  presente con altra compagnia</label>
            </value>
            <value>
                <fullName>Problematiche economiche</fullName>
                <default>false</default>
                <label>Problematiche economiche</label>
            </value>
            <value>
                <fullName>Problematiche con la Compagnia</fullName>
                <default>false</default>
                <label>Problematiche con la Compagnia</label>
            </value>
            <value>
                <fullName>Richiesta no contatto CC</fullName>
                <default>false</default>
                <label>Richiesta no contatto CC</label>
            </value>
            <value>
                <fullName>Cliente non interessato ad assicurarsi UnipolSai</fullName>
                <default>false</default>
                <label>Cliente non interessato ad assicurarsi UnipolSai</label>
            </value>
            <value>
                <fullName>Cliente non interessato alla chiamata</fullName>
                <default>false</default>
                <label>Cliente non interessato alla chiamata</label>
            </value>
            <value>
                <fullName>Numero errato/ non esistente</fullName>
                <default>false</default>
                <label>Numero errato/ non esistente</label>
            </value>
            <value>
                <fullName>Mancata risposta</fullName>
                <default>false</default>
                <label>Mancata risposta</label>
            </value>
            <value>
                <fullName>Indisponibilità  recapiti</fullName>
                <default>false</default>
                <label>Indisponibilità  recapiti</label>
            </value>
            <value>
                <fullName>Fuori SLA di CC</fullName>
                <default>false</default>
                <label>Fuori SLA di CC</label>
            </value>
            <value>
                <fullName>Non inviato al CC per errore tecnico</fullName>
                <default>false</default>
                <label>Non inviato al CC per errore tecnico</label>
            </value>
            <value>
                <fullName>Azione principale chiusa negativa</fullName>
                <default>false</default>
                <label>Azione principale chiusa negativa</label>
            </value>
            <value>
                <fullName>Azione principale chiusa positiva</fullName>
                <default>false</default>
                <label>Azione principale chiusa positiva</label>
            </value>
            <value>
                <fullName>Chiusura negativa - processo conversion</fullName>
                <default>false</default>
                <label>Chiusura negativa - processo conversion</label>
            </value>
            <value>
                <fullName>Polizza venduta</fullName>
                <default>false</default>
                <label>Polizza venduta</label>
            </value>
            <value>
                <fullName>Revoca Consensi CC</fullName>
                <default>false</default>
                <label>Revoca Consensi CC</label>
            </value>
            <value>
                <fullName>Unsubscribe CC</fullName>
                <default>false</default>
                <label>Unsubscribe CC</label>
            </value>
            <value>
                <fullName>Altro Vantaggio</fullName>
                <default>false</default>
                <label>Altro Vantaggio</label>
            </value>
            <value>
                <fullName>Concorrenza</fullName>
                <default>false</default>
                <label>Concorrenza</label>
            </value>
            <value>
                <fullName>Prodotto</fullName>
                <default>false</default>
                <label>Prodotto</label>
            </value>
            <value>
                <fullName>Prezzo</fullName>
                <default>false</default>
                <label>Prezzo</label>
            </value>
        </valueSetDefinition>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Altro</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Per modifica</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Per acquisto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Calcola quotazione</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Nessuna quotazione calcolata</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <valueName>Appuntamento in presenza</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <valueName>Appuntamento da remoto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato con appuntamento</controllingFieldValue>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Interessato anche ad altri prodotti</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Il cliente passa/chiama in autonomia</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato senza appuntamento</controllingFieldValue>
            <valueName>Pagamento in autonomia da remoto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Richiesta premio</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Richieste modifiche al preventivo/polizza</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Presenza offerta altra Compagnia</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Richiesta informazioni aggiuntive</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Info su polizze di altri prodotti</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da richiamare</controllingFieldValue>
            <valueName>Richiesta altra modalità  di pagamento</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Acquisto/preventivo altra Compagnia</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Prezzo troppo alto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Problematiche sinistri</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Bene non assicurabile</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Optout</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Copertura già  presente con altra compagnia</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Problematiche economiche</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Problematiche con la Compagnia</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Richiesta no contatto CC</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Cliente non interessato ad assicurarsi UnipolSai</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Cliente non interessato alla chiamata</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Revoca Consensi CC</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Unsubscribe CC</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Altro Vantaggio</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Concorrenza</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Prodotto</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non interessato</controllingFieldValue>
            <valueName>Prezzo</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non contattabile</controllingFieldValue>
            <valueName>Numero errato/ non esistente</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non contattabile</controllingFieldValue>
            <valueName>Mancata risposta</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non contattabile</controllingFieldValue>
            <valueName>Indisponibilità  recapiti</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da contattare</controllingFieldValue>
            <valueName>Fuori SLA di CC</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Da contattare</controllingFieldValue>
            <valueName>Non inviato al CC per errore tecnico</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non esitato</controllingFieldValue>
            <valueName>Azione principale chiusa negativa</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non esitato</controllingFieldValue>
            <valueName>Azione principale chiusa positiva</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Non esitato</controllingFieldValue>
            <valueName>Chiusura negativa - processo conversion</valueName>
        </valueSettings>
        <valueSettings>
            <controllingFieldValue>Interessato</controllingFieldValue>
            <valueName>Polizza venduta</valueName>
        </valueSettings>
    </valueSet>
</CustomField>
