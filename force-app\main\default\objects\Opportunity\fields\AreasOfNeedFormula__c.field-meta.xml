<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AreasOfNeedFormula__c</fullName>
    <externalId>false</externalId>
    <formula>&quot;&quot; &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Cane e Gatto&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Cane e Gatto&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Casa&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Casa&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Famiglia&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Famiglia&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Infortuni&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Infortuni&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Mobilità&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Mobilità&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Salute&apos;), IMAGE(&quot;/resource/unica_unisalute/<EMAIL>&quot;, &quot;Salute&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Veicoli&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Veicoli&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;) &amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Viaggio&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Viaggio&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;)&amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Vita&apos;), IMAGE(&quot;/resource/icons_ambiti/<EMAIL>&quot;, &quot;Vita&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;)&amp;
IF(INCLUDES(AreaOfNeed__c, &apos;Previdenza integrativa&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/previdenza.png&quot;, &quot;Previdenza integrativa&quot;, 20, 20) &amp; &quot; &quot;, &quot;&quot;)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Areas of Need Formula</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <trackTrending>false</trackTrending>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
