<?xml version="1.0" encoding="UTF-8"?>
<CustomField xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>AreasOfNeedFormula__c</fullName>
    <externalId>false</externalId>
    <formula>&quot;&quot; &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Cane e Gatto&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/pet.png&quot;, &quot;Cane e Gatto&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Casa&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/casa.png&quot;, &quot;Casa&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Famiglia&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/famiglia.png&quot;, &quot;Famiglia&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Infortuni&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/infortuni.png&quot;, &quot;Infortuni&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Mobilità&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/mobilita.png&quot;, &quot;Mobilità&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Salute&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/salute.png&quot;, &quot;Salute&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Veicoli&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/veicoli.png&quot;, &quot;Veicoli&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Viaggio&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/viaggi.png&quot;, &quot;Viaggio&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Vita&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/<EMAIL>&quot;, &quot;Vita&quot;, 32, 32), &quot;&quot;) &amp;
IF(INCLUDES(AreasOfNeed__c, &apos;Previdenza integrativa&apos;), IMAGE(&quot;/resource/areas_of_need/areas_of_need/on/previdenza.png&quot;, &quot;Previdenza integrativa&quot;, 32, 32), &quot;&quot;)</formula>
    <formulaTreatBlanksAs>BlankAsZero</formulaTreatBlanksAs>
    <label>Areas of Need Formula</label>
    <required>false</required>
    <trackHistory>false</trackHistory>
    <type>Text</type>
    <unique>false</unique>
</CustomField>
