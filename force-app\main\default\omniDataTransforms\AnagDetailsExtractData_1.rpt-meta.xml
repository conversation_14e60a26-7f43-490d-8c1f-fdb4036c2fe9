<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON>: NPI su Dettaglio Anagrafica</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperExtractAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;accountDetail_provincia&quot; : &quot;Text&quot;,
  &quot;accountDetail_localita&quot; : &quot;Text&quot;,
  &quot;accountDetail_indirizzoCompleto&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_id&quot; : &quot;Text&quot;,
  &quot;accountDetail_id&quot; : &quot;Text&quot;,
  &quot;accountAgency_regione&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_regione&quot; : &quot;Text&quot;,
  &quot;AccountDetail_presso&quot; : &quot;Text&quot;,
  &quot;accountDetail_numeroCivico&quot; : &quot;Text&quot;,
  &quot;accountDetail_codiceBelfioreComune&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_presso&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_numeroCivico&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_codiceBelfioreComune&quot; : &quot;Text&quot;,
  &quot;accountDetail_cellulare&quot; : &quot;Text&quot;,
  &quot;accountDetail_idContattoCellulare&quot; : &quot;Text&quot;,
  &quot;accountDetail_idContattoMail&quot; : &quot;Text&quot;,
  &quot;accountDetail_mail&quot; : &quot;Text&quot;,
  &quot;accountDetail_dataNascita&quot; : &quot;Text&quot;,
  &quot;accountDetail_dataDecesso&quot; : { },
  &quot;accountDetail_FiscalCode&quot; : &quot;Text&quot;,
  &quot;accountDetail_FiscalCodeStatus&quot; : &quot;Text&quot;,
  &quot;accountDetail_BirthCountry&quot; : &quot;Text&quot;,
  &quot;accountDetail_BirthDate&quot; : &quot;Text&quot;,
  &quot;accountDetail_BirthPlace&quot; : &quot;Text&quot;,
  &quot;accountDetail_BirthProvince&quot; : &quot;Text&quot;,
  &quot;accountDetail_Gender&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Domicilio&quot; : &quot;Text&quot;,
  &quot;accountDetail_Eta&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_DataCessazione&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_AgenziaPrevalente&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_AdesioneCauzione&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Subagenzia&quot; : &quot;Text&quot;,
  &quot;accountAccountRelationSociey_Id&quot; : &quot;Text&quot;,
  &quot;account_Age&quot; : &quot;Text&quot;,
  &quot;accountDetail_FullName&quot; : &quot;Text&quot;,
  &quot;accountDetail_VatNumber&quot; : &quot;Text&quot;,
  &quot;accountDetail_DataCostituzione&quot; : &quot;Text&quot;,
  &quot;accountDetail_StatoPartitaIva&quot; : &quot;Text&quot;,
  &quot;accountDetail_TipoPersonaGiuridica&quot; : &quot;Text&quot;,
  &quot;accountDetail_CompanyType&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_SourceSystemOrigin&quot; : &quot;Text&quot;,
  &quot;accountDetail_ClientePerimetro&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_SubAgencyCode&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_indirizzo&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_StatoEmail&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_StatoCellulare&quot; : &quot;Text&quot;,
  &quot;accountDetail_FirstName&quot; : &quot;Text&quot;,
  &quot;accountDetail_LastName&quot; : &quot;Text&quot;,
  &quot;account_ExternalId&quot; : &quot;Text&quot;,
  &quot;accountDetail_Occupation&quot; : &quot;Text&quot;,
  &quot;accountDetail_Residenza&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail__DataCreazione&quot; : &quot;Text&quot;,
  &quot;account_FEA&quot; : true,
  &quot;accountDetail_Ciu&quot; : &quot;Text&quot;,
  &quot;accountDetail_ProfessioneDescrizione&quot; : &quot;Text&quot;,
  &quot;accountDetail_DataCessazione&quot; : &quot;Text&quot;,
  &quot;accountDetailPrivateArea_Cellulare&quot; : &quot;Text&quot;,
  &quot;accountDetailPrivateArea_Email&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_Email&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_Cellulare&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_FonteCellulare&quot; : &quot;Text&quot;,
  &quot;accountDetailMDM_FonteEmail&quot; : &quot;Text&quot;,
  &quot;accountAccountRelationAgency_Id&quot; : &quot;Text&quot;,
  &quot;AccountAgencyDetail_cap&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_localita&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_idIndirizzo&quot; : &quot;Text&quot;,
  &quot;accountDetail_idIndirizzo&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_latitudine&quot; : 0,
  &quot;accountAgencyDetail_longitudine&quot; : 0,
  &quot;accountAgencyDetail_stato&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_provincia&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_indirizzoCompleto&quot; : &quot;Text&quot;,
  &quot;accountDetail_cap&quot; : &quot;Text&quot;,
  &quot;accountDetail_longitudine&quot; : &quot;Text&quot;,
  &quot;accountDetail_latitudine&quot; : &quot;Text&quot;,
  &quot;accountDetail_stato&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Cellulare&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_CellularePreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_CellulareUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_CellulareId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Email&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_EmailPreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_EmailUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_EmailId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_PEC&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_PECPreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_PECUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_PECId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Fax&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_FaxPreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_FaxUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_FaxId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Telefono&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_TelefonoPreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_TelefonoUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_TelefonoId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_TelefonoReferente&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_TelefonoReferentePreferred&quot; : &quot;Boolean&quot;,
  &quot;accountAgencyDetail_TelefonoReferenteUsage&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_TelefonoReferenteId&quot; : &quot;Text&quot;,
  &quot;accountAgencyDetail_Referente&quot; : &quot;Text&quot;
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagDetailsExtractData</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>AnagDetailsExtractDataCustom1033</globalKey>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>14.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountAgencyDetail:SourceSystemCreatedDate__c ISBLANK &quot;-&quot; | | var:AccountAgencyDetail:SourceSystemCreatedDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountAgencyDetail:SourceSystemCreatedDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountAgencyDetail:SourceSystemCreatedDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountAgencyDetail:SourceSystemCreatedDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>DataInizio</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem6</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>SocietaId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem7</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Society</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountAgencyDetail:Street__c ISBLANK | var:AccountAgencyDetail:City__c ISBLANK &amp;&amp; | var:AccountAgencyDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountAgencyDetail:Street__c &apos;,/\/\/&apos; + var:AccountAgencyDetail:City__c + &apos;,/\/\/&apos; + var:AccountAgencyDetail:Country__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountAgencyDetail:Street__c) &amp;&amp; ISBLANK(AccountAgencyDetail:City__c) &amp;&amp; ISBLANK(AccountAgencyDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountAgencyDetail:Street__c +&apos;, &apos;+AccountAgencyDetail:City__c+&apos;, &apos;+AccountAgencyDetail:Country__c)))</formulaExpression>
        <formulaResultPath>Domicilio</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypeMDM</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem10</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetailNPI:OtherStreet__c ISBLANK | var:AccountDetailNPI:OtherPostalCode__c ISBLANK &amp;&amp; | var:AccountDetailNPI:OtherCity__c ISBLANK &amp;&amp; | var:AccountDetailNPI:OtherCountry__c ISBLANK &amp;&amp; &quot;-&quot; var:AccountDetailNPI:OtherStreet__c &apos;,/\/\/&apos; + var:AccountDetailNPI:OtherState__c + &apos;,/\/\/&apos; + var:AccountDetailNPI:OtherCity__c + &apos;,/\/\/&apos; + var:AccountDetailNPI:OtherPostalCode__c + IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetailNPI:OtherStreet__c) &amp;&amp; ISBLANK(AccountDetailNPI:OtherPostalCode__c) &amp;&amp;ISBLANK(AccountDetailNPI:OtherCity__c) &amp;&amp; ISBLANK(AccountDetailNPI:OtherCountry__c)), &quot;-&quot;, (AccountDetailNPI:OtherStreet__c +&apos;, &apos;  + AccountDetailNPI:OtherState__c +&apos;, &apos;
+AccountDetailNPI:OtherCity__c +&apos;, &apos;+AccountDetailNPI:OtherPostalCode__c)))</formulaExpression>
        <formulaResultPath>Domicilio2</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIKE</filterOperator>
        <filterValue>Society:ExternalId__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem8</globalKey>
        <inputFieldName>ExternalId__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;ProfessioneImpiego&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem9</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeEmploymentType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:ProfessionCode__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem14</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeProfession</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:AccountDetailsNPI__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem12</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>15.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem13</globalKey>
        <inputFieldName>Agenzia__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>14.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;ProfessioneSpecializzazione&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem15</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeSpecialization</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem16</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Agenzia&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem19</globalKey>
        <inputFieldName>FinServ__Role__r.FinServ__InverseRole__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:ProfessionEmploymentType__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem20</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeEmploymentType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountAgencyDetail:ClientTerminationDate__c ISBLANK &quot;-&quot; | | var:AccountAgencyDetail:ClientTerminationDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountAgencyDetail:ClientTerminationDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountAgencyDetail:ClientTerminationDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountAgencyDetail:ClientTerminationDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>DataCessazione</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem17</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Society:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem18</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem23</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem24</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem21</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem22</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:AccountAgencyDetail:SubAgencyCode__c 10 &lt; | &quot;0000&quot; | var:AccountAgencyDetail:SubAgencyCode__c TOSTRING CONCAT | var:AccountAgencyDetail:SubAgencyCode__c 100 &lt; | &quot;000&quot; | var:AccountAgencyDetail:SubAgencyCode__c TOSTRING CONCAT | &quot;00&quot; | var:AccountAgencyDetail:SubAgencyCode__c TOSTRING CONCAT IF IF</formulaConverted>
        <formulaExpression>IF(AccountAgencyDetail:SubAgencyCode__c &lt; 10, CONCAT(&quot;0000&quot;, TOSTRING(AccountAgencyDetail:SubAgencyCode__c)), IF(AccountAgencyDetail:SubAgencyCode__c &lt; 100, CONCAT(&quot;000&quot;, TOSTRING(AccountAgencyDetail:SubAgencyCode__c)), CONCAT(&quot;00&quot;, TOSTRING(AccountAgencyDetail:SubAgencyCode__c))))</formulaExpression>
        <formulaResultPath>codiceGestore</formulaResultPath>
        <formulaSequence>11.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem27</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypeAccAgencyId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem28</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationAgenzia:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem25</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:BPERProtectionStartDate__c ISBLANK &quot;-&quot; | var:AccountDetail:BPERProtectionStartDate__c &apos;/\/\/-/\/\/&apos; + var:AccountDetail:BPERProtectionEndDate__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:BPERProtectionStartDate__c),&quot;-&quot;,CONCAT(AccountDetail:BPERProtectionStartDate__c + &apos; - &apos;+ AccountDetail:BPERProtectionEndDate__c))</formulaExpression>
        <formulaResultPath>ConcatDate</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem26</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAgencyDetail:SubAgencyCode__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem31</globalKey>
        <inputFieldName>CIP__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>14.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem69</globalKey>
        <inputFieldName>AccountAgencyDetail:RegistryStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_RegistryStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;AA&quot; : &quot;Annullato&quot;,
  &quot;CE&quot; : &quot;Cessato&quot;,
  &quot;EF&quot; : &quot;Effettivo&quot;,
  &quot;PC&quot; : &quot;Procedura Batch&quot;,
  &quot;PO&quot; : &quot;Potenziale&quot;,
  &quot;TR&quot; : &quot;Trasferito&quot;,
  &quot;SC&quot; : &quot;Scorporato&quot;,
  &quot;CES&quot; : &quot;Cessato&quot;,
  &quot;EFF&quot; : &quot;Effettivo&quot;,
  &quot;POT&quot; : &quot;Potenziale&quot;,
  &quot;TRA&quot; : &quot;Trasferito&quot;,
  &quot;SCO&quot; : &quot;Scorporato&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:BirthDate__c ISBLANK &quot;-&quot; | var:AccountDetail:BirthDate__c AGE ( IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:BirthDate__c),&quot;-&quot;,AGE(AccountDetail:BirthDate__c)</formulaExpression>
        <formulaResultPath>Eta</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem32</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem70</globalKey>
        <inputFieldName>AccountDetail:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Street__c ISBLANK | var:AccountDetail:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetail:City__c ISBLANK &amp;&amp; | var:AccountDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetail:Street__c &apos;,/\/\/&apos; + var:AccountDetail:State__c + &apos;,/\/\/&apos; + var:AccountDetail:City__c + &apos;,/\/\/&apos; + var:AccountDetail:PostalCode__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:Street__c) &amp;&amp; ISBLANK(AccountDetail:PostalCode__c) &amp;&amp;ISBLANK(AccountDetail:City__c) &amp;&amp; ISBLANK(AccountDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountDetail:Street__c + &apos;, &apos;  + AccountDetail:State__c +&apos;, &apos;
+AccountDetail:City__c +&apos;, &apos;+AccountDetail:PostalCode__c)))</formulaExpression>
        <formulaResultPath>Indirizzo</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem29</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem67</globalKey>
        <inputFieldName>AccountDetailNPI:OtherIstatCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceIstatComune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:Indirizzo var:Domicilio2 == var:Domicilio2 &quot;-&quot; == ||</formulaConverted>
        <formulaExpression>(Indirizzo == Domicilio2) || (Domicilio2 == &quot;-&quot;)</formulaExpression>
        <formulaResultPath>flagDomicilio</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem30</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem68</globalKey>
        <inputFieldName>AccountDetail:SourceSystemStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_statoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:ProfessionSpecializationType__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem35</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeSpecialization</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem73</globalKey>
        <inputFieldName>AccountDetail:ProfessionLegalEntityType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_TipologiaPersonaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Compagnia&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem36</globalKey>
        <inputFieldName>FinServ__Role__r.FinServ__InverseRole__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem74</globalKey>
        <inputFieldName>MappingCodeSpecialization:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_SpecializzazioneDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Professione&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem33</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeProfession</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem71</globalKey>
        <inputFieldName>AccountDetailNPI:OtherAddress__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_indirizzoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountAgencyDetail:SourceSystemCreatedDate__c ISBLANK &quot;-&quot; | | var:AccountAgencyDetail:SourceSystemCreatedDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountAgencyDetail:SourceSystemCreatedDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountAgencyDetail:SourceSystemCreatedDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountAgencyDetail:SourceSystemCreatedDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:SourceSystemCreatedDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>aadDataCreazione</formulaResultPath>
        <formulaSequence>10.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem34</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem72</globalKey>
        <inputFieldName>Account:FEA__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>account_FEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem2</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailPA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:ProfessionSectorType__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem39</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>13.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeSectorType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetailPA:AccountDetailsNPI__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem3</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>17.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailPANPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypePA</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem40</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailPA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem0</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;ProfessioneSettore&apos;</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem37</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>13.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeSectorType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem75</globalKey>
        <inputFieldName>AccountAgencyDetail:FeaFlagContactsOwnership__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_flagProprietaContattiFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetailMDM:AccountDetailsNPI__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem1</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>16.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDMNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem38</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem76</globalKey>
        <inputFieldName>AccountAgencyDetail:Phone__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Telefono</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:BirthDate__c ISBLANK &quot;-&quot; | | var:AccountDetail:BirthDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountDetail:BirthDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountDetail:BirthDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:BirthDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountDetail:BirthDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountDetail:BirthDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountDetail:BirthDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>dataNascita</formulaResultPath>
        <formulaSequence>9.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypeId</filterValue>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem41</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem78</globalKey>
        <inputFieldName>AccountAgencyDetail:FaxSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_FaxId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountAgencyDetail:ClientTerminationDate__c ISBLANK &quot;-&quot; | | var:AccountAgencyDetail:ClientTerminationDate__c 8 10 SUBSTRING &quot;/&quot; + | var:AccountAgencyDetail:ClientTerminationDate__c 5 7 SUBSTRING + &quot;/&quot; + | var:AccountAgencyDetail:ClientTerminationDate__c 0 4 SUBSTRING + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountAgencyDetail:ClientTerminationDate__c),&quot;-&quot;,CONCAT(SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 8, 10) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 5, 7) + &quot;/&quot; + SUBSTRING(AccountAgencyDetail:ClientTerminationDate__c, 0, 4)))</formulaExpression>
        <formulaResultPath>aadDataCessazione</formulaResultPath>
        <formulaSequence>12.0</formulaSequence>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem42</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem79</globalKey>
        <inputFieldName>AccountDetail:CadestralCountryCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_codiceBelfioreStato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NO</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem77</globalKey>
        <inputFieldName>AccountAgencyDetail:IsClientInOtherAgency__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_clientInOtherAgency</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem45</globalKey>
        <inputFieldName>AccountDetail:ProfessionEmploymentType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_impiego</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem82</globalKey>
        <inputFieldName>cip:ComponentDescription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codiceGestore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem46</globalKey>
        <inputFieldName>AccountDetail:FiscalCodeStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_FiscalCodeStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;F&quot; : &quot;FORZATO&quot;,
  &quot;C&quot; : &quot;CALCOLATO&quot;,
  &quot;N&quot; : &quot;CONFERMATO&quot;,
  &quot;S&quot; : &quot;STRANIERO&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem83</globalKey>
        <inputFieldName>AccountAgencyDetail:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem43</globalKey>
        <inputFieldName>AccountDetailMDMNPI:EmailStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_StatoEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem80</globalKey>
        <inputFieldName>AccountDetailNPI:OtherLatitude__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>accountDetail_latitudineDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem44</globalKey>
        <inputFieldName>AccountAgencyDetail:FeaFlagSubscription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_flagAdesioneFEA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem81</globalKey>
        <inputFieldName>AccountAgencyDetail:ContactPersonMobileUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_TelefonoReferenteUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem49</globalKey>
        <inputFieldName>AccountAgencyDetail:Society__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem86</globalKey>
        <inputFieldName>AccountAgencyDetail:SubAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_codiceSubagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem50</globalKey>
        <inputFieldName>AccountAgencyDetail:SubAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_SubAgencyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem87</globalKey>
        <inputFieldName>AccountDetail:Latitude__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_latitudine</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem47</globalKey>
        <inputFieldName>AccountDetailNPI:OtherIstatCodeAnag1__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceIstatAnag1Domi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem84</globalKey>
        <inputFieldName>AccountDetail:ProfessionLegalEntityType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_personaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem48</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_TipoPersonaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem85</globalKey>
        <inputFieldName>AccountAgencyDetail:RegistryStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_statoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem53</globalKey>
        <inputFieldName>AccountDetail:Street__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_indirizzoCompleto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem90</globalKey>
        <inputFieldName>AccountAgencyDetail:FaxFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_FaxPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem54</globalKey>
        <inputFieldName>AccountDetail:BillingCountry__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgency_regione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem91</globalKey>
        <inputFieldName>AccountDetailMDMNPI:MobileStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_StatoCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem129</globalKey>
        <inputFieldName>AccountDetail:StreetNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_numeroCivico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem51</globalKey>
        <inputFieldName>MappingCodeProfession:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_ProfessioneDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem88</globalKey>
        <inputFieldName>MappingCodeSectorType:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_PubPrivDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem52</globalKey>
        <inputFieldName>AccountDetail:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem89</globalKey>
        <inputFieldName>AccountDetail:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem57</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_DataCessazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem94</globalKey>
        <inputFieldName>AccountDetail:ProfessionSpecializationType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_specializzazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem132</globalKey>
        <inputFieldName>AccountDetail:VatNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_VatNumber</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem58</globalKey>
        <inputFieldName>AccountDetailNPI:OtherPostalCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_capDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem95</globalKey>
        <inputFieldName>AccountAgencyDetail:CertifiedEmail__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_PEC</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem133</globalKey>
        <inputFieldName>AccountDetailNPI:OtherAddressNormalizationType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoNormalizzatoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem55</globalKey>
        <inputFieldName>MappingCodeEmploymentType:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_TipoProfessioneDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem92</globalKey>
        <inputFieldName>AccountDetail:PostalCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_cap</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem130</globalKey>
        <inputFieldName>AccountDetail:Dus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_dus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem56</globalKey>
        <inputFieldName>AccountAgencyDetail:Fax__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Fax</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem93</globalKey>
        <inputFieldName>AccountAgencyDetail:EndDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_dataFineEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem131</globalKey>
        <inputFieldName>AccountDetail:City__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_localita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem61</globalKey>
        <inputFieldName>AccountDetailNPI:OtherAt__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_pressoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem98</globalKey>
        <inputFieldName>AccountDetail:AddressNormalizationType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoNormalizzato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem136</globalKey>
        <inputFieldName>AccountDetailNPI:OtherAddressType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoIndirizzoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem62</globalKey>
        <inputFieldName>Domicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_Domicilio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem99</globalKey>
        <inputFieldName>AccountDetailNPI:OtherAddressSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_idIndirizzoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem137</globalKey>
        <inputFieldName>AccountDetail:CompanyType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_CompanyType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem59</globalKey>
        <inputFieldName>AccountAgencyDetail:SubAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Subagenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem96</globalKey>
        <inputFieldName>AccountDetail:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_mail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem134</globalKey>
        <inputFieldName>AccountAgencyDetail:FlagBondAuthorization__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_AdesioneCauzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem60</globalKey>
        <inputFieldName>AccountDetail:IstatCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceIstatComune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem97</globalKey>
        <inputFieldName>AccountDetail:IstatCodeAnag1__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceIstatAnag1</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem135</globalKey>
        <inputFieldName>Indirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem65</globalKey>
        <inputFieldName>AccountAgencyDetail:PrelevantAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_AgenziaPrevalente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem102</globalKey>
        <inputFieldName>AccountAgencyDetail:PhoneFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_TelefonoPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem66</globalKey>
        <inputFieldName>AccountAgencyDetail:MobileUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_CellulareUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem63</globalKey>
        <inputFieldName>AccountDetail:Country__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_stato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem100</globalKey>
        <inputFieldName>AccountDetailNPI:OtherCadestralCountryCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_codiceBelfioreStatoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem138</globalKey>
        <inputFieldName>AccountDetailMDM:SourceSystemOrigin__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_SourceSystemOrigin</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem64</globalKey>
        <inputFieldName>AccountDetailPANPI:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailPrivateArea_Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem101</globalKey>
        <inputFieldName>AccountDetail:BirthDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_dataNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem104</globalKey>
        <inputFieldName>AccountDetailNPI:OtherLongitude__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Double</outputFieldFormat>
        <outputFieldName>accountDetail_longitudineDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem141</globalKey>
        <inputFieldName>Domicilio2</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Domicilio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem105</globalKey>
        <inputFieldName>AccountDetail:FiscalCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_FiscalCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem142</globalKey>
        <inputFieldName>AccountDetailNPI:OtherDug__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_dugDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem139</globalKey>
        <inputFieldName>AccountAgencyDetail:CertifiedEmailUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_PECUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem103</globalKey>
        <inputFieldName>AccountDetail:DeathDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>accountDetail_dataDecesso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem140</globalKey>
        <inputFieldName>AccountDetailNPI:OtherCadastralCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceBelfioreComuneDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem108</globalKey>
        <inputFieldName>Eta</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Eta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem145</globalKey>
        <inputFieldName>AccountAccountRelationCompagnia:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAccountRelationSociey_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>null</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem109</globalKey>
        <inputFieldName>AccountAgencyDetail:TopClientDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Object</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_dataClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem146</globalKey>
        <inputFieldName>AccountDetailNPI:OtherStreetNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_numeroCivicoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem106</globalKey>
        <inputFieldName>AccountDetail:FullName__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_FullName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem143</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_DataCostituzione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem107</globalKey>
        <inputFieldName>AccountDetail:MobileSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_idContattoCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem144</globalKey>
        <inputFieldName>AccountDetail:State__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_provincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem112</globalKey>
        <inputFieldName>AccountDetailNPI:OtherStreet__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_indirizzoCompletoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem149</globalKey>
        <inputFieldName>AccountAgencyDetail:TopClientFlag__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_flagClienteTop</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem113</globalKey>
        <inputFieldName>AccountAgencyDetail:DomicileCountry__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_regione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem150</globalKey>
        <inputFieldName>AccountDetailPANPI:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailPrivateArea_Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem110</globalKey>
        <inputFieldName>AccountAgencyDetail:MobileSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_CellulareId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem147</globalKey>
        <inputFieldName>AccountDetail:BirthPlace__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_BirthPlace</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem111</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem148</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_ClientePerimetro</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem116</globalKey>
        <inputFieldName>AccountDetail:LastName__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_LastName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem153</globalKey>
        <inputFieldName>AccountDetail:Dug__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_dug</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem117</globalKey>
        <inputFieldName>AccountDetail:ProfessionCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_professione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem154</globalKey>
        <inputFieldName>AccountDetailNPI:OtherCountry__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_statoDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem114</globalKey>
        <inputFieldName>AccountDetail:ProfessionCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Occupation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem151</globalKey>
        <inputFieldName>AccountAgencyDetail:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem115</globalKey>
        <inputFieldName>AccountDetailMDMNPI:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem152</globalKey>
        <inputFieldName>AccountAgencyDetail:EmailFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_EmailPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem120</globalKey>
        <inputFieldName>AccountAccountRelationAgenzia:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAccountRelationAgency_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem157</globalKey>
        <inputFieldName>AccountDetailNPI:OtherCity__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_localitaDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem121</globalKey>
        <inputFieldName>AccountAgencyDetail:FaxUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_FaxUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem158</globalKey>
        <inputFieldName>AccountAgencyDetail:CertifiedEmailFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_PECPreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem118</globalKey>
        <inputFieldName>AccountAgencyDetail:CertifiedEmailSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_PECId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem155</globalKey>
        <inputFieldName>AccountAgencyDetail:ContactPersonMobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_TelefonoReferente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem119</globalKey>
        <inputFieldName>AccountDetail:Longitude__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_longitudine</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem156</globalKey>
        <inputFieldName>AccountDetail:Gender__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_Gender</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem124</globalKey>
        <inputFieldName>AccountAgencyDetail:ContactPersonMobileSourceSystemId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_TelefonoReferenteId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem161</globalKey>
        <inputFieldName>AccountAgencyDetail:ContactPersonMobileName__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Referente</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem125</globalKey>
        <inputFieldName>AccountDetail:VatNumberStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_VatNumberStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;F&quot; : &quot;FORZATO&quot;,
  &quot;C&quot; : &quot;CALCOLATO&quot;,
  &quot;N&quot; : &quot;CONFERMATO&quot;,
  &quot;S&quot; : &quot;STRANIERO&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem162</globalKey>
        <inputFieldName>aadDataCessazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_DataCessazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Society:Id</filterValue>
        <globalKey>AnagDetailsExtractDataCustom4862</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>14.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem122</globalKey>
        <inputFieldName>AccountAgencyDetail:MobileFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_CellularePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem159</globalKey>
        <inputFieldName>flagDomicilio</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountDetail_flagDomicilio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem123</globalKey>
        <inputFieldName>Account:Age__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>account_Age</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem160</globalKey>
        <inputFieldName>AccountDetail:BirthProvince__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_BirthProvince</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem128</globalKey>
        <inputFieldName>AccountDetail:SourceSystemType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem126</globalKey>
        <inputFieldName>AccountDetail:CadastralCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_codiceBelfioreComune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem163</globalKey>
        <inputFieldName>AccountDetail:At__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>AccountDetail_presso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem127</globalKey>
        <inputFieldName>AccountDetail:EmailSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_idContattoMail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem164</globalKey>
        <inputFieldName>AccountDetail:AddressType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoIndirizzo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem167</globalKey>
        <inputFieldName>AccountAgencyDetail:EffectiveDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_dataInizioEffetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem168</globalKey>
        <inputFieldName>AccountDetail:FirstName__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_FirstName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem165</globalKey>
        <inputFieldName>AccountDetailMDMNPI:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem166</globalKey>
        <inputFieldName>AccountDetailMDMNPI:EmailSource__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_FonteEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem171</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_StatoPartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem172</globalKey>
        <inputFieldName>AccountAgencyDetail:EmailSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_EmailId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem169</globalKey>
        <inputFieldName>dataNascita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_BirthDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem170</globalKey>
        <inputFieldName>AccountAgencyDetail:PhoneUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_TelefonoUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem175</globalKey>
        <inputFieldName>AccountDetail:AddressSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_idIndirizzo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem176</globalKey>
        <inputFieldName>AccountDetailMDMNPI:MobileSource__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_FonteCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem173</globalKey>
        <inputFieldName>AccountDetailNPI:OtherDus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_dusDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem174</globalKey>
        <inputFieldName>aadDataCreazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail__DataCreazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem179</globalKey>
        <inputFieldName>AccountDetail:BirthCountry__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_BirthCountry</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem180</globalKey>
        <inputFieldName>AccountAgencyDetail:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem177</globalKey>
        <inputFieldName>AccountAgencyDetail:ContactPersonMobileFlagPreferred__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_TelefonoReferentePreferred</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem178</globalKey>
        <inputFieldName>AccountAgencyDetail:PhoneSourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_TelefonoId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem183</globalKey>
        <inputFieldName>AccountDetail:SourceSystemRegistryType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_tipoAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem184</globalKey>
        <inputFieldName>Account:ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>account_ExternalId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem181</globalKey>
        <inputFieldName>AccountDetailNPI:OtherState__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_provinciaDomi</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem182</globalKey>
        <inputFieldName>AccountDetail:ProfessionSectorType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_settore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem185</globalKey>
        <inputFieldName>AccountAgencyDetail:EmailUsageType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_EmailUsage</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;Personal&quot; : &quot;PER&quot;,
  &quot;Work&quot; : &quot;LAV&quot;,
  &quot;P&quot; : &quot;PER&quot;,
  &quot;L&quot; : &quot;LAV&quot;,
  &quot;PER&quot; : &quot;PER&quot;,
  &quot;LAV&quot; : &quot;LAV&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagDetailsExtractDataCustom0jI9O000000ycK9UAIItem186</globalKey>
        <inputFieldName>AccountDetail:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagDetailsExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;RecTypePA&quot; : &quot;0129X000008npfyQAA&quot;,
  &quot;RecTypeMDM&quot; : &quot;0129X000008npfxQAA&quot;,
  &quot;RecTypeId&quot; : &quot;0129X000008npfwQAA&quot;,
  &quot;RecTypeAccAgencyId&quot; : &quot;0129X000008npfuQAA&quot;,
  &quot;SocietaId&quot; : &quot;0019X00000sU898QAC&quot;,
  &quot;AccountId&quot; : &quot;0019V000012b33sQAA&quot;,
  &quot;UserId&quot; : &quot;0059X00000PrV0GQAV&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>AnagDetailsExtractData_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
