<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <description><PERSON>: Gestore Anagrafica + Referente Aziendale</description>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;DataMapperExtractAction1&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>AnagExtractData</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>64799a4d-dbcf-4635-9147-6356f42f9914</globalKey>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem76</globalKey>
        <inputFieldName>AccountDetailMDMNPI:EmailStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_emailStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem19</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem34</globalKey>
        <inputFieldName>AccountAccountRelationCompagnia:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAccountRelationSociey_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem18</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem69</globalKey>
        <inputFieldName>AccountAgencyDetail:ClientTerminationDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_clientTerminationDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem11</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem56</globalKey>
        <inputFieldName>AccountDetail:Relation__r.FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem35</globalKey>
        <inputFieldName>feaFlagSubscription</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_fea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;Si&quot;,
  &quot;false&quot; : &quot;No&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem47</globalKey>
        <inputFieldName>AccountDetail:ProfessionCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_occupazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem24</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem74</globalKey>
        <inputFieldName>User:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>user_id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem62</globalKey>
        <inputFieldName>AccountDetail:FeaMail__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_feaEmail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>d992e687-f2e1-4aa7-9c88-732b47fba4e0</globalKey>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem75</globalKey>
        <inputFieldName>AccountAgencyDetail:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_mobile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem61</globalKey>
        <inputFieldName>AccountDetail:FeaEffectiveDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>accountDetail_feaDataInizio</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem40</globalKey>
        <inputFieldName>Age</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>account_eta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem73</globalKey>
        <inputFieldName>AccountAgencyDetail:PrelevantAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_prelevantAgencyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem41</globalKey>
        <inputFieldName>AccountDetail:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Street__c ISBLANK | var:AccountDetail:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetail:City__c ISBLANK &amp;&amp; | var:AccountDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetail:Street__c &apos;,/\/\/&apos; + var:AccountDetail:State__c + &apos;,/\/\/&apos; + var:AccountDetail:City__c + &apos;,/\/\/&apos; + var:AccountDetail:PostalCode__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:Street__c) &amp;&amp; ISBLANK(AccountDetail:PostalCode__c) &amp;&amp;ISBLANK(AccountDetail:City__c) &amp;&amp; ISBLANK(AccountDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountDetail:Street__c +&apos;, &apos; +AccountDetail:State__c +&apos;, &apos;
+AccountDetail:City__c +&apos;, &apos;+AccountDetail:PostalCode__c)))</formulaExpression>
        <formulaResultPath>Indirizzo</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem38</globalKey>
        <inputFieldName>User:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>user_name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Agenzia&apos;</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem0</globalKey>
        <inputFieldName>FinServ__Role__r.FinServ__InverseRole__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Society:ExternalId__c &quot;SOC_4&quot; = &quot;Unisalute&quot; &quot;Altro&quot; IF</formulaConverted>
        <formulaExpression>IF(Society:ExternalId__c=&quot;SOC_4&quot;,&quot;Unisalute&quot;,&quot;Altro&quot;)</formulaExpression>
        <formulaResultPath>companyForModaleFea</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem79</globalKey>
        <inputFieldName>Indirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:AccountDetailsNPI__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem4</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem80</globalKey>
        <inputFieldName>AccountAgencyDetail:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accounAgencyDetail_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>ND</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem39</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>sinistri</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetail:ProfessionCode__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem1</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeProfession</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem42</globalKey>
        <inputFieldName>Account:SourceSocietyDescription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>account_socOrig</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem77</globalKey>
        <inputFieldName>AccountDetail:IsCorporate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Corporate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;Si&quot;,
  &quot;false&quot; : &quot;No&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem6</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem78</globalKey>
        <inputFieldName>Account:ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>account_cf</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:BPERProtectionStartDate__c ISBLANK &quot;-&quot; | var:AccountDetail:BPERProtectionStartDate__c &apos;/\/\/-/\/\/&apos; + var:AccountDetail:BPERProtectionEndDate__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:BPERProtectionStartDate__c),&quot;-&quot;,CONCAT(AccountDetail:BPERProtectionStartDate__c + &apos; - &apos;+ AccountDetail:BPERProtectionEndDate__c))</formulaExpression>
        <formulaResultPath>ConcatDate</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem45</globalKey>
        <inputFieldName>AccountDetailMDMNPI:MobileStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetailMDM_mobileStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>AnagExtractDataCustom152</globalKey>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>3ce5f5fd-00a7-48e6-a811-c12223111aa7</globalKey>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>12.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem46</globalKey>
        <inputFieldName>AccountDetail:VatNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>PartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;CIP&apos;</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem9</globalKey>
        <inputFieldName>RecordType.Name</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Societa</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem8</globalKey>
        <inputFieldName>Society__r.ExternalId__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>4b5a2c2a-c675-4d0b-a5a9-7b71350ae4e2</globalKey>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Compagnia&apos;</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem12</globalKey>
        <inputFieldName>FinServ__Role__r.FinServ__InverseRole__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationAgenzia:Id</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem7</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem43</globalKey>
        <inputFieldName>cip:ComponentDescription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>gestoreAnagrafica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>&quot;Prospect&quot;</formulaConverted>
        <formulaExpression>&quot;Prospect&quot;</formulaExpression>
        <formulaResultPath>statoSoggetto</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem22</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:BirthDate__c ISBLANK &quot;-&quot; | var:AccountDetail:BirthDate__c AGE ( IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(AccountDetail:BirthDate__c),&quot;-&quot;,AGE(AccountDetail:BirthDate__c)</formulaExpression>
        <formulaResultPath>Age</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem23</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Society:Id</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem25</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>18e3da99-5657-43ab-af98-3eff592d3b36</globalKey>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem60</globalKey>
        <inputFieldName>AccountAgencyDetail:RegistryStatus__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_registryStatus</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountDetailMDM:AccountDetailsNPI__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem29</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDMNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem59</globalKey>
        <inputFieldName>AccountAgencyDetail:SubAgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_SubAgencyCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:AccountAgencyDetail:IsClientInOtherAgency__c</formulaConverted>
        <formulaExpression>AccountAgencyDetail:IsClientInOtherAgency__c</formulaExpression>
        <formulaResultPath>tempClienteAltraAgenzia</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem28</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem65</globalKey>
        <inputFieldName>AccountAgencyDetail:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>5b2f855e-065d-4bba-966e-a5c46a9c94a0</globalKey>
        <inputObjectName>AccountDetailsNPI__c</inputObjectName>
        <inputObjectQuerySequence>11.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDMNPI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem66</globalKey>
        <inputFieldName>AccountDetailMDMNPI:Email__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem16</globalKey>
        <inputFieldName>FinServ__RelatedAccount__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAgencyDetail:SubAgencyCode__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem27</globalKey>
        <inputFieldName>CIP__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:FeaVersionType__c ISBLANK var:AccountDetail:FeaEndDate__c | TODAY &lt;= || &quot;No&quot; | var:AccountDetail:FeaVersionType__c &quot;0&quot; = &quot;Da/\/\/aggiornare&quot; &quot;Si&quot; IF ( IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:FeaVersionType__c) || (AccountDetail:FeaEndDate__c &lt;= TODAY()),&quot;No&quot;,IF(AccountDetail:FeaVersionType__c = &quot;0&quot;,&quot;Da aggiornare&quot;,&quot;Si&quot;))</formulaExpression>
        <formulaResultPath>feaFlagSubscription</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem26</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>b746a47b-cff7-4933-926d-47ed9f1ed6c8</globalKey>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeProfession</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem32</globalKey>
        <inputFieldName>AccountDetailMDMNPI:Mobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetailMDM_Mobile</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem53</globalKey>
        <inputFieldName>User:IdAzienda__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>agenzia_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem64</globalKey>
        <inputFieldName>AccountDetailMDM:SourceSystemOrigin__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_originator</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;1&quot; : &quot;Unipol Assicurazioni S.p.A&quot;,
  &quot;2&quot; : &quot;BIM&quot;,
  &quot;4&quot; : &quot;UNISALUTE&quot;,
  &quot;5&quot; : &quot;LINEAR&quot;,
  &quot;7&quot; : &quot;FATTORIE DEL CERRO&quot;,
  &quot;8&quot; : &quot;LINEAR LIFE&quot;,
  &quot;10&quot; : &quot;ALG&quot;,
  &quot;11&quot; : &quot;NOVAAEG&quot;,
  &quot;12&quot; : &quot;BPER&quot;,
  &quot;13&quot; : &quot;TIM&quot;,
  &quot;14&quot; : &quot;SANTINI&quot;,
  &quot;15&quot; : &quot;CONAD&quot;,
  &quot;16&quot; : &quot;FINITALIA&quot;,
  &quot;17&quot; : &quot;UNIPOLTECH&quot;,
  &quot;18&quot; : &quot;UNA_NAXOS&quot;,
  &quot;19&quot; : &quot;CRIF/CRIBIS&quot;,
  &quot;20&quot; : &quot;PRONTO ASSISTANCE&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem48</globalKey>
        <inputFieldName>AccountDetail:AtecoCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CodiceAteco</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem63</globalKey>
        <inputFieldName>AccountAgencyDetail:EndDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_enddate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypeAccAgencyId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem17</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountAgencyDetails__c</inputObjectName>
        <inputObjectQuerySequence>6.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAgencyDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>c78643d1-03ac-436a-bc11-dfed6a5400a5</globalKey>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem15</globalKey>
        <inputFieldName>Agenzia__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem68</globalKey>
        <inputFieldName>Society:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>compagnia_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>fde3f015-3940-413a-af11-891aa236f23d</globalKey>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>[pagamento]</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem31</globalKey>
        <inputFieldName>AccountDetail:PaymentMethods__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_pagamento</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;Professione&apos;</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem14</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>9.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>MappingCodeProfession</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem67</globalKey>
        <inputFieldName>AccountDetail:FEA__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_adesioneFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;true&quot; : &quot;Si&quot;,
  &quot;false&quot; : &quot;No&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem54</globalKey>
        <inputFieldName>AccountAgencyDetail:FlagBondAuthorization__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_flagBondAuthorization</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem30</globalKey>
        <inputFieldName>statoSoggetto</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>asset_statoSoggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>ND</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem52</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>contenzioso</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem33</globalKey>
        <inputFieldName>companyForModaleFea</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>companyForModaleFea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem57</globalKey>
        <inputFieldName>MappingCodeProfession:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_ProfessioneDescrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem71</globalKey>
        <inputFieldName>AccountDetail:FeaMobile__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_feaCellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem44</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>FEAModal</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem51</globalKey>
        <inputFieldName>AccountAccountRelationAgenzia:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountAccountRelationAgency_Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Society:Id</filterValue>
        <globalKey>AnagExtractDataCustom9016</globalKey>
        <inputFieldName>Society__c</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>10.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>cip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;MDM&apos;</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem20</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem72</globalKey>
        <inputFieldName>AccountDetail:IsGroupMember__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_isGroupMember</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;false&quot; : &quot;NO&quot;,
  &quot;true&quot; : &quot;SI&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecTypeId</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem21</globalKey>
        <inputFieldName>RecordTypeId</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>7.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>81830cc7-8620-4da0-bcab-3c830d826df3</globalKey>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Societa</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem10</globalKey>
        <inputFieldName>ExternalId__c</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Society</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem55</globalKey>
        <inputFieldName>AccountDetail:OmnichannelBehaviour__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_comportamentoOmnicanale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem36</globalKey>
        <inputFieldName>AccountDetail:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem50</globalKey>
        <inputFieldName>AccountDetail:PostalCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountDetail_cap</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>LIMIT</filterOperator>
        <filterValue>1</filterValue>
        <globalKey>c6c18ce0-5921-4947-addb-df5a987ccb08</globalKey>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Society</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>NO</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem37</globalKey>
        <inputFieldName>AccountAgencyDetail:IsClientInOtherAgency__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>accountAgencyDetail_clienteAltraAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;false&quot; : &quot;NO&quot;,
  &quot;true&quot; : &quot;SI&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem13</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>8.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetailMDM</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Mancante</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem58</globalKey>
        <inputFieldName>AccountDetail:SourceSystemConsentCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountDetail_SourceSystemConsentCode</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem70</globalKey>
        <inputFieldName>AccountDetail:CompanyType__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>FormaGiuridica</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>AnagExtractDataCustom0jI9O000000ycNNUAYItem49</globalKey>
        <inputFieldName>AccountAgencyDetail:EffectiveDate__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>AnagExtractData</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>accountAgencyDetail_effectiveDate</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;RecTypeAccAgencyId&quot; : &quot;0129X000008npfuQAA&quot;,
  &quot;RecTypeId&quot; : &quot;0129X000008npfwQAA&quot;,
  &quot;Societa&quot; : &quot;SOC_1&quot;,
  &quot;AccountId&quot; : &quot;0019V000011j3DZQAY&quot;,
  &quot;UserId&quot; : &quot;0059X00000PrV0GQAV&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>AnagExtractData_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
