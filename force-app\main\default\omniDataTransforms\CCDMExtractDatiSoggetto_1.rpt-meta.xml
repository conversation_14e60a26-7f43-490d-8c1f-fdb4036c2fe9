<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CCDMExtractDatiSoggetto</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>AccountAccountRelationCompagnia:Id</filterValue>
        <globalKey>CCDMExtractDatiSoggettoCustom4002</globalKey>
        <inputFieldName>Relation__c</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountDetail</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:AccountDetail:Street__c ISBLANK | var:AccountDetail:PostalCode__c ISBLANK &amp;&amp; | var:AccountDetail:City__c ISBLANK &amp;&amp; | var:AccountDetail:Country__c ISBLANK &amp;&amp; &quot;-&quot; | var:AccountDetail:Street__c &apos;,/\/\/&apos; + var:AccountDetail:State__c + &apos;,/\/\/&apos; + var:AccountDetail:City__c + &apos;,/\/\/&apos; + var:AccountDetail:PostalCode__c + CONCAT IF</formulaConverted>
        <formulaExpression>IF((ISBLANK(AccountDetail:Street__c) &amp;&amp; ISBLANK(AccountDetail:PostalCode__c) &amp;&amp;ISBLANK(AccountDetail:City__c) &amp;&amp; ISBLANK(AccountDetail:Country__c)), &quot;-&quot;, (CONCAT(AccountDetail:Street__c +&apos;, &apos; +AccountDetail:State__c +&apos;, &apos;
+AccountDetail:City__c +&apos;, &apos;+AccountDetail:PostalCode__c)))</formulaExpression>
        <formulaResultPath>residenzaF</formulaResultPath>
        <formulaSequence>8.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom2374</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom776</globalKey>
        <inputFieldName>spesaF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Spesa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom1233</globalKey>
        <inputFieldName>clienteVipF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Cliente Vip</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:ClienteVip__c ISBLANK &quot;--&quot; var:Account:ClienteVip__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:ClienteVip__c),&quot;--&quot;,Account:ClienteVip__c)</formulaExpression>
        <formulaResultPath>clienteVipF</formulaResultPath>
        <formulaSequence>6.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom624</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Capacitadispesa__c ISBLANK &quot;--&quot; var:Account:Capacitadispesa__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Capacitadispesa__c),&quot;--&quot;,Account:Capacitadispesa__c)</formulaExpression>
        <formulaResultPath>spesaF</formulaResultPath>
        <formulaSequence>7.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom8249</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem11</globalKey>
        <inputFieldName>Account:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Case:AccountId</filterValue>
        <globalKey>CCDMExtractDatiSoggettoCustom5562</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountAccountRelationCompagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem8</globalKey>
        <inputFieldName>Account:Subject_Name_Case__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Soggetto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom2268</globalKey>
        <inputFieldName>residenzaF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Residenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem7</globalKey>
        <inputFieldName>emailF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Email</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem10</globalKey>
        <inputFieldName>codiceFiscaleF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem9</globalKey>
        <inputFieldName>cellulareF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Cellulare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>recordId</filterValue>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem4</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Case</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Case</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:VatNumber__c ISBLANK &quot;--&quot; var:Account:VatNumber__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:VatNumber__c),&quot;--&quot;,Account:VatNumber__c)</formulaExpression>
        <formulaResultPath>partitaIvaF</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom4388</globalKey>
        <inputFieldName>dataNascitaF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>DataDiNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem6</globalKey>
        <inputFieldName>partitaIvaF</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>PartitaIva</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Case:AccountId</filterValue>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem5</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Account</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Account</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Email__c ISBLANK &quot;--&quot; var:Account:Email__c IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Email__c),&quot;--&quot;,Account:Email__c)</formulaExpression>
        <formulaResultPath>emailF</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:PersonBirthdate ISBLANK &quot;--&quot; var:Account:PersonBirthdate IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:PersonBirthdate),&quot;--&quot;,Account:PersonBirthdate)</formulaExpression>
        <formulaResultPath>dataNascitaF</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom3796</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:FinServ__TaxId__pc ISBLANK &quot;--&quot; var:Account:FinServ__TaxId__pc IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:FinServ__TaxId__pc),&quot;--&quot;,Account:FinServ__TaxId__pc)</formulaExpression>
        <formulaResultPath>codiceFiscaleF</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:Account:Phone ISBLANK &quot;--&quot; var:Account:Phone IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(Account:Phone),&quot;--&quot;,Account:Phone)</formulaExpression>
        <formulaResultPath>cellulareF</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>CCDMExtractDatiSoggettoCustom0jI9O000000vsi1UAAItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>CCDMExtractDatiSoggetto</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;recordId&quot; : &quot;5009O00000TrUAYQA3&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>CCDMExtractDatiSoggetto_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
