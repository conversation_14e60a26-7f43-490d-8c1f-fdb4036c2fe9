<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Loop&quot; : [ {
    &quot;LoopBlockIterationIndex&quot; : 1,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiStatus&quot; : true,
    &quot;FlagIdentificazione&quot; : true,
    &quot;NumeroIdentificazioneFiscale&quot; : 44455,
    &quot;ResidenzaFiscale&quot; : {
      &quot;CodiceABI&quot; : &quot;94&quot;,
      &quot;Descrizione&quot; : &quot;GERMANIA&quot;,
      &quot;CodiceBelfiore&quot; : &quot;Z112&quot;,
      &quot;Attivo&quot; : true,
      &quot;Ue&quot; : true
    },
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z112&quot;
  }, {
    &quot;LoopBlockIterationIndex&quot; : 2,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiStatus&quot; : true,
    &quot;FlagIdentificazione&quot; : true,
    &quot;NumeroIdentificazioneFiscale&quot; : 45678,
    &quot;ResidenzaFiscale&quot; : {
      &quot;CodiceABI&quot; : &quot;264&quot;,
      &quot;Descrizione&quot; : &quot;BIELORUSSIA&quot;,
      &quot;CodiceBelfiore&quot; : &quot;Z139&quot;,
      &quot;Attivo&quot; : true,
      &quot;Ue&quot; : false
    },
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z139&quot;
  }, {
    &quot;LoopBlockIterationIndex&quot; : 3,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiStatus&quot; : true,
    &quot;FlagIdentificazione&quot; : false,
    &quot;NumeroIdentificazioneFiscale&quot; : null,
    &quot;ResidenzaFiscale&quot; : {
      &quot;CodiceABI&quot; : &quot;37&quot;,
      &quot;Descrizione&quot; : &quot;SAN MARINO&quot;,
      &quot;CodiceBelfiore&quot; : &quot;Z130&quot;,
      &quot;Attivo&quot; : true,
      &quot;Ue&quot; : false
    },
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z130&quot;
  } ],
  &quot;DettagliModifica&quot; : {
    &quot;PaeseDiNascitaUSA&quot; : false,
    &quot;CodiceFiscale&quot; : &quot;****************&quot;,
    &quot;RecordId&quot; : &quot;a1i9X0000050jblQAA&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;N/D&quot;,
    &quot;Societa&quot; : &quot;UnipolSai&quot;,
    &quot;PaeseDiNascita&quot; : &quot;N/D&quot;
  },
  &quot;Stati&quot; : {
    &quot;Options&quot; : [ {
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmdUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmgUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmhUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmoUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmtUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmwUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CILE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnBUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnKUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCndUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCneUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCngUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnhUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCniUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnlUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnmUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnoUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCntUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GABON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnuUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo7UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo8UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoBUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCobUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCocUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCodUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCofUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCogUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z000&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCokUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jColUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jComUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jConUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCooUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCorUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCosUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCotUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCouUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCovUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCowUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCozUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp8UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpgUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCphUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCplUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCppUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCprUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCptUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpwUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqKUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqdUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqmUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqtUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCquUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr7UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;
    } ]
  },
  &quot;AllCittadinanze&quot; : [ {
    &quot;id&quot; : 1,
    &quot;codice&quot; : &quot;301&quot;,
    &quot;descrizione&quot; : &quot;AFGHANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 2,
    &quot;codice&quot; : &quot;201&quot;,
    &quot;descrizione&quot; : &quot;ALBANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 3,
    &quot;codice&quot; : &quot;401&quot;,
    &quot;descrizione&quot; : &quot;ALGERINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 4,
    &quot;codice&quot; : &quot;202&quot;,
    &quot;descrizione&quot; : &quot;ANDORRANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 5,
    &quot;codice&quot; : &quot;402&quot;,
    &quot;descrizione&quot; : &quot;ANGOLANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 6,
    &quot;codice&quot; : &quot;602&quot;,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 7,
    &quot;codice&quot; : &quot;358&quot;,
    &quot;descrizione&quot; : &quot;ARMENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 8,
    &quot;codice&quot; : &quot;701&quot;,
    &quot;descrizione&quot; : &quot;AUSTRALIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 9,
    &quot;codice&quot; : &quot;203&quot;,
    &quot;descrizione&quot; : &quot;AUSTRIACA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 10,
    &quot;codice&quot; : &quot;359&quot;,
    &quot;descrizione&quot; : &quot;AZERBAIGIAN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 11,
    &quot;codice&quot; : &quot;505&quot;,
    &quot;descrizione&quot; : &quot;BAHAMAS&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 12,
    &quot;codice&quot; : &quot;304&quot;,
    &quot;descrizione&quot; : &quot;BAHREIN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 13,
    &quot;codice&quot; : &quot;305&quot;,
    &quot;descrizione&quot; : &quot;BANGLADESH&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 14,
    &quot;codice&quot; : &quot;506&quot;,
    &quot;descrizione&quot; : &quot;BARBADOS&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 15,
    &quot;codice&quot; : &quot;206&quot;,
    &quot;descrizione&quot; : &quot;BELGA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 16,
    &quot;codice&quot; : &quot;507&quot;,
    &quot;descrizione&quot; : &quot;BELIZE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 17,
    &quot;codice&quot; : &quot;406&quot;,
    &quot;descrizione&quot; : &quot;BENIN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 18,
    &quot;codice&quot; : &quot;306&quot;,
    &quot;descrizione&quot; : &quot;BHUTANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 19,
    &quot;codice&quot; : &quot;256&quot;,
    &quot;descrizione&quot; : &quot;BIELORUSSA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 20,
    &quot;codice&quot; : &quot;307&quot;,
    &quot;descrizione&quot; : &quot;BIRMANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 21,
    &quot;codice&quot; : &quot;604&quot;,
    &quot;descrizione&quot; : &quot;BOLIVIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 22,
    &quot;codice&quot; : &quot;252&quot;,
    &quot;descrizione&quot; : &quot;BOSNIACA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 23,
    &quot;codice&quot; : &quot;408&quot;,
    &quot;descrizione&quot; : &quot;BOTSWANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 24,
    &quot;codice&quot; : &quot;605&quot;,
    &quot;descrizione&quot; : &quot;BRASILIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 25,
    &quot;codice&quot; : &quot;219&quot;,
    &quot;descrizione&quot; : &quot;BRITANNICA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 26,
    &quot;codice&quot; : &quot;309&quot;,
    &quot;descrizione&quot; : &quot;BRUNEI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 27,
    &quot;codice&quot; : &quot;209&quot;,
    &quot;descrizione&quot; : &quot;BULGARA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 28,
    &quot;codice&quot; : &quot;409&quot;,
    &quot;descrizione&quot; : &quot;BURKINA FASO&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 29,
    &quot;codice&quot; : &quot;410&quot;,
    &quot;descrizione&quot; : &quot;BURUNDI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 30,
    &quot;codice&quot; : &quot;310&quot;,
    &quot;descrizione&quot; : &quot;CAMBOGIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 31,
    &quot;codice&quot; : &quot;411&quot;,
    &quot;descrizione&quot; : &quot;CAMERUNENSE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 32,
    &quot;codice&quot; : &quot;509&quot;,
    &quot;descrizione&quot; : &quot;CANADESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 33,
    &quot;codice&quot; : &quot;413&quot;,
    &quot;descrizione&quot; : &quot;CAPOVERDIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 34,
    &quot;codice&quot; : &quot;257&quot;,
    &quot;descrizione&quot; : &quot;CECA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 35,
    &quot;codice&quot; : &quot;414&quot;,
    &quot;descrizione&quot; : &quot;CENTRAFRICANA, REPUBBLICA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 36,
    &quot;codice&quot; : &quot;415&quot;,
    &quot;descrizione&quot; : &quot;CIAD&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 37,
    &quot;codice&quot; : &quot;606&quot;,
    &quot;descrizione&quot; : &quot;CILENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 38,
    &quot;codice&quot; : &quot;311&quot;,
    &quot;descrizione&quot; : &quot;CINGALESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 39,
    &quot;codice&quot; : &quot;315&quot;,
    &quot;descrizione&quot; : &quot;CIPRIOTA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 40,
    &quot;codice&quot; : &quot;608&quot;,
    &quot;descrizione&quot; : &quot;COLOMBIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 41,
    &quot;codice&quot; : &quot;417&quot;,
    &quot;descrizione&quot; : &quot;COMORE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 42,
    &quot;codice&quot; : &quot;463&quot;,
    &quot;descrizione&quot; : &quot;CONGO, REP.DEMOCRATICA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 43,
    &quot;codice&quot; : &quot;418&quot;,
    &quot;descrizione&quot; : &quot;CONGOLESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 44,
    &quot;codice&quot; : &quot;513&quot;,
    &quot;descrizione&quot; : &quot;COSTATICANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 45,
    &quot;codice&quot; : &quot;250&quot;,
    &quot;descrizione&quot; : &quot;CROATA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 46,
    &quot;codice&quot; : &quot;514&quot;,
    &quot;descrizione&quot; : &quot;CUBANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 47,
    &quot;codice&quot; : &quot;212&quot;,
    &quot;descrizione&quot; : &quot;DANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 48,
    &quot;codice&quot; : &quot;515&quot;,
    &quot;descrizione&quot; : &quot;DOMINICA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 49,
    &quot;codice&quot; : &quot;609&quot;,
    &quot;descrizione&quot; : &quot;ECUADOREGNA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 50,
    &quot;codice&quot; : &quot;419&quot;,
    &quot;descrizione&quot; : &quot;EGIZIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 51,
    &quot;codice&quot; : &quot;322&quot;,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 52,
    &quot;codice&quot; : &quot;466&quot;,
    &quot;descrizione&quot; : &quot;ERITREA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 53,
    &quot;codice&quot; : &quot;247&quot;,
    &quot;descrizione&quot; : &quot;ESTONE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 54,
    &quot;codice&quot; : &quot;420&quot;,
    &quot;descrizione&quot; : &quot;ETIOPE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 55,
    &quot;codice&quot; : &quot;253&quot;,
    &quot;descrizione&quot; : &quot;EX REP. JUGOSLAVIA MACEDONE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 56,
    &quot;codice&quot; : &quot;245&quot;,
    &quot;descrizione&quot; : &quot;FEDERAZIONE RUSSA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 57,
    &quot;codice&quot; : &quot;703&quot;,
    &quot;descrizione&quot; : &quot;FIGI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 58,
    &quot;codice&quot; : &quot;323&quot;,
    &quot;descrizione&quot; : &quot;FILIPPINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 59,
    &quot;codice&quot; : &quot;214&quot;,
    &quot;descrizione&quot; : &quot;FINLANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 60,
    &quot;codice&quot; : &quot;215&quot;,
    &quot;descrizione&quot; : &quot;FRANCESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 61,
    &quot;codice&quot; : &quot;421&quot;,
    &quot;descrizione&quot; : &quot;GABON&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 62,
    &quot;codice&quot; : &quot;422&quot;,
    &quot;descrizione&quot; : &quot;GAMBIA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 63,
    &quot;codice&quot; : &quot;360&quot;,
    &quot;descrizione&quot; : &quot;GEORGIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 64,
    &quot;codice&quot; : &quot;423&quot;,
    &quot;descrizione&quot; : &quot;GHANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 65,
    &quot;codice&quot; : &quot;518&quot;,
    &quot;descrizione&quot; : &quot;GIAMAICANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 66,
    &quot;codice&quot; : &quot;326&quot;,
    &quot;descrizione&quot; : &quot;GIAPPONESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 67,
    &quot;codice&quot; : &quot;424&quot;,
    &quot;descrizione&quot; : &quot;GIBUTI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 68,
    &quot;codice&quot; : &quot;327&quot;,
    &quot;descrizione&quot; : &quot;GIORDANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 69,
    &quot;codice&quot; : &quot;220&quot;,
    &quot;descrizione&quot; : &quot;GRECA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 70,
    &quot;codice&quot; : &quot;523&quot;,
    &quot;descrizione&quot; : &quot;GUATEMALTECA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 71,
    &quot;codice&quot; : &quot;425&quot;,
    &quot;descrizione&quot; : &quot;GUINEA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 72,
    &quot;codice&quot; : &quot;426&quot;,
    &quot;descrizione&quot; : &quot;GUINEA BISSAU&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 73,
    &quot;codice&quot; : &quot;427&quot;,
    &quot;descrizione&quot; : &quot;GUINEA EQUATORIALE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 74,
    &quot;codice&quot; : &quot;612&quot;,
    &quot;descrizione&quot; : &quot;GUYANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 75,
    &quot;codice&quot; : &quot;524&quot;,
    &quot;descrizione&quot; : &quot;HAITIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 76,
    &quot;codice&quot; : &quot;525&quot;,
    &quot;descrizione&quot; : &quot;HONDUREGNA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 77,
    &quot;codice&quot; : &quot;330&quot;,
    &quot;descrizione&quot; : &quot;INDIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 78,
    &quot;codice&quot; : &quot;331&quot;,
    &quot;descrizione&quot; : &quot;INDONESIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 79,
    &quot;codice&quot; : &quot;333&quot;,
    &quot;descrizione&quot; : &quot;IRACHENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 80,
    &quot;codice&quot; : &quot;332&quot;,
    &quot;descrizione&quot; : &quot;IRANIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 81,
    &quot;codice&quot; : &quot;221&quot;,
    &quot;descrizione&quot; : &quot;IRLANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 82,
    &quot;codice&quot; : &quot;223&quot;,
    &quot;descrizione&quot; : &quot;ISLANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 83,
    &quot;codice&quot; : &quot;334&quot;,
    &quot;descrizione&quot; : &quot;ISRAELIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 84,
    &quot;codice&quot; : &quot;0&quot;,
    &quot;descrizione&quot; : &quot;ITALIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 85,
    &quot;codice&quot; : &quot;404&quot;,
    &quot;descrizione&quot; : &quot;IVORIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 86,
    &quot;codice&quot; : &quot;356&quot;,
    &quot;descrizione&quot; : &quot;KAZAKA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 87,
    &quot;codice&quot; : &quot;428&quot;,
    &quot;descrizione&quot; : &quot;KENIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 88,
    &quot;codice&quot; : &quot;361&quot;,
    &quot;descrizione&quot; : &quot;KIRGISA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 89,
    &quot;codice&quot; : &quot;272&quot;,
    &quot;descrizione&quot; : &quot;KOSOVARA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 90,
    &quot;codice&quot; : &quot;335&quot;,
    &quot;descrizione&quot; : &quot;KUATIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 91,
    &quot;codice&quot; : &quot;336&quot;,
    &quot;descrizione&quot; : &quot;LAOTIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 92,
    &quot;codice&quot; : &quot;429&quot;,
    &quot;descrizione&quot; : &quot;LESOTHO&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 93,
    &quot;codice&quot; : &quot;248&quot;,
    &quot;descrizione&quot; : &quot;LETTONE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 94,
    &quot;codice&quot; : &quot;337&quot;,
    &quot;descrizione&quot; : &quot;LIBANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 95,
    &quot;codice&quot; : &quot;430&quot;,
    &quot;descrizione&quot; : &quot;LIBERIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 96,
    &quot;codice&quot; : &quot;431&quot;,
    &quot;descrizione&quot; : &quot;LIBICA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 97,
    &quot;codice&quot; : &quot;225&quot;,
    &quot;descrizione&quot; : &quot;LIECHTENSTEIN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 98,
    &quot;codice&quot; : &quot;249&quot;,
    &quot;descrizione&quot; : &quot;LITUANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 99,
    &quot;codice&quot; : &quot;226&quot;,
    &quot;descrizione&quot; : &quot;LUSSEMBURGHESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 100,
    &quot;codice&quot; : &quot;432&quot;,
    &quot;descrizione&quot; : &quot;MADAGASCAR&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 101,
    &quot;codice&quot; : &quot;434&quot;,
    &quot;descrizione&quot; : &quot;MALAWI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 102,
    &quot;codice&quot; : &quot;339&quot;,
    &quot;descrizione&quot; : &quot;MALDIVE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 103,
    &quot;codice&quot; : &quot;340&quot;,
    &quot;descrizione&quot; : &quot;MALESIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 104,
    &quot;codice&quot; : &quot;435&quot;,
    &quot;descrizione&quot; : &quot;MALI&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 105,
    &quot;codice&quot; : &quot;227&quot;,
    &quot;descrizione&quot; : &quot;MALTESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 106,
    &quot;codice&quot; : &quot;436&quot;,
    &quot;descrizione&quot; : &quot;MAROCCHINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 107,
    &quot;codice&quot; : &quot;712&quot;,
    &quot;descrizione&quot; : &quot;MARSHALL, ISOLE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 108,
    &quot;codice&quot; : &quot;437&quot;,
    &quot;descrizione&quot; : &quot;MAURITANIA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 109,
    &quot;codice&quot; : &quot;438&quot;,
    &quot;descrizione&quot; : &quot;MAURITIUS&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 110,
    &quot;codice&quot; : &quot;527&quot;,
    &quot;descrizione&quot; : &quot;MESSICANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 111,
    &quot;codice&quot; : &quot;254&quot;,
    &quot;descrizione&quot; : &quot;MOLDOVA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 112,
    &quot;codice&quot; : &quot;229&quot;,
    &quot;descrizione&quot; : &quot;MONEGASCA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 113,
    &quot;codice&quot; : &quot;341&quot;,
    &quot;descrizione&quot; : &quot;MONGOLA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 114,
    &quot;codice&quot; : &quot;270&quot;,
    &quot;descrizione&quot; : &quot;MONTENEGRINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 115,
    &quot;codice&quot; : &quot;440&quot;,
    &quot;descrizione&quot; : &quot;MOZAMBICO&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 116,
    &quot;codice&quot; : &quot;441&quot;,
    &quot;descrizione&quot; : &quot;NAMIBIA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 117,
    &quot;codice&quot; : &quot;715&quot;,
    &quot;descrizione&quot; : &quot;NAURU&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 118,
    &quot;codice&quot; : &quot;719&quot;,
    &quot;descrizione&quot; : &quot;NEOZELANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 119,
    &quot;codice&quot; : &quot;342&quot;,
    &quot;descrizione&quot; : &quot;NEPALESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 120,
    &quot;codice&quot; : &quot;529&quot;,
    &quot;descrizione&quot; : &quot;NICARACUENSE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 121,
    &quot;codice&quot; : &quot;442&quot;,
    &quot;descrizione&quot; : &quot;NIGER&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 122,
    &quot;codice&quot; : &quot;443&quot;,
    &quot;descrizione&quot; : &quot;NIGERIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 123,
    &quot;codice&quot; : &quot;231&quot;,
    &quot;descrizione&quot; : &quot;NORVEGESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 124,
    &quot;codice&quot; : &quot;232&quot;,
    &quot;descrizione&quot; : &quot;OLANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 125,
    &quot;codice&quot; : &quot;343&quot;,
    &quot;descrizione&quot; : &quot;OMAN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 126,
    &quot;codice&quot; : &quot;344&quot;,
    &quot;descrizione&quot; : &quot;PACHISTANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 127,
    &quot;codice&quot; : &quot;324&quot;,
    &quot;descrizione&quot; : &quot;PALESTINESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 128,
    &quot;codice&quot; : &quot;530&quot;,
    &quot;descrizione&quot; : &quot;PANAMENSE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 129,
    &quot;codice&quot; : &quot;721&quot;,
    &quot;descrizione&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 130,
    &quot;codice&quot; : &quot;614&quot;,
    &quot;descrizione&quot; : &quot;PARAGUAIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 131,
    &quot;codice&quot; : &quot;615&quot;,
    &quot;descrizione&quot; : &quot;PERUVIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 132,
    &quot;codice&quot; : &quot;233&quot;,
    &quot;descrizione&quot; : &quot;POLACCA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 133,
    &quot;codice&quot; : &quot;234&quot;,
    &quot;descrizione&quot; : &quot;PORTOGHESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 134,
    &quot;codice&quot; : &quot;345&quot;,
    &quot;descrizione&quot; : &quot;QATAR&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 135,
    &quot;codice&quot; : &quot;516&quot;,
    &quot;descrizione&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 136,
    &quot;codice&quot; : &quot;314&quot;,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE CINESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 137,
    &quot;codice&quot; : &quot;319&quot;,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE NORDCOREAN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 138,
    &quot;codice&quot; : &quot;320&quot;,
    &quot;descrizione&quot; : &quot;REPUBBLICA SUDCOREANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 139,
    &quot;codice&quot; : &quot;235&quot;,
    &quot;descrizione&quot; : &quot;ROMENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 140,
    &quot;codice&quot; : &quot;446&quot;,
    &quot;descrizione&quot; : &quot;RUANDA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 141,
    &quot;codice&quot; : &quot;534&quot;,
    &quot;descrizione&quot; : &quot;SAINT KITTS E NEVIS&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 142,
    &quot;codice&quot; : &quot;725&quot;,
    &quot;descrizione&quot; : &quot;SALOMONE, ISOLE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 143,
    &quot;codice&quot; : &quot;517&quot;,
    &quot;descrizione&quot; : &quot;SALVADOREGNA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 144,
    &quot;codice&quot; : &quot;727&quot;,
    &quot;descrizione&quot; : &quot;SAMOA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 145,
    &quot;codice&quot; : &quot;236&quot;,
    &quot;descrizione&quot; : &quot;SANMARINESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 146,
    &quot;codice&quot; : &quot;246&quot;,
    &quot;descrizione&quot; : &quot;SANTA SEDE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 147,
    &quot;codice&quot; : &quot;448&quot;,
    &quot;descrizione&quot; : &quot;SAO TOME E PRINCIPE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 148,
    &quot;codice&quot; : &quot;302&quot;,
    &quot;descrizione&quot; : &quot;SAUDITA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 149,
    &quot;codice&quot; : &quot;450&quot;,
    &quot;descrizione&quot; : &quot;SENEGALESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 150,
    &quot;codice&quot; : &quot;271&quot;,
    &quot;descrizione&quot; : &quot;SERBA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 151,
    &quot;codice&quot; : &quot;224&quot;,
    &quot;descrizione&quot; : &quot;SERBA / MONTENEGRINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 152,
    &quot;codice&quot; : &quot;449&quot;,
    &quot;descrizione&quot; : &quot;SEYCELLES&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 153,
    &quot;codice&quot; : &quot;451&quot;,
    &quot;descrizione&quot; : &quot;SIERRA LEONE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 154,
    &quot;codice&quot; : &quot;346&quot;,
    &quot;descrizione&quot; : &quot;SINGAPORE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 155,
    &quot;codice&quot; : &quot;348&quot;,
    &quot;descrizione&quot; : &quot;SIRIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 156,
    &quot;codice&quot; : &quot;255&quot;,
    &quot;descrizione&quot; : &quot;SLOVACCA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 157,
    &quot;codice&quot; : &quot;251&quot;,
    &quot;descrizione&quot; : &quot;SLOVENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 158,
    &quot;codice&quot; : &quot;453&quot;,
    &quot;descrizione&quot; : &quot;SOMALA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 159,
    &quot;codice&quot; : &quot;239&quot;,
    &quot;descrizione&quot; : &quot;SPAGNOLA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 160,
    &quot;codice&quot; : &quot;536&quot;,
    &quot;descrizione&quot; : &quot;STATUNITENSE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 161,
    &quot;codice&quot; : &quot;454&quot;,
    &quot;descrizione&quot; : &quot;SUDAFRICANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 162,
    &quot;codice&quot; : &quot;455&quot;,
    &quot;descrizione&quot; : &quot;SUDANESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 163,
    &quot;codice&quot; : &quot;616&quot;,
    &quot;descrizione&quot; : &quot;SURINAME&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 164,
    &quot;codice&quot; : &quot;240&quot;,
    &quot;descrizione&quot; : &quot;SVEDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 165,
    &quot;codice&quot; : &quot;241&quot;,
    &quot;descrizione&quot; : &quot;SVIZZERA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 166,
    &quot;codice&quot; : &quot;456&quot;,
    &quot;descrizione&quot; : &quot;SWAZILAND&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 167,
    &quot;codice&quot; : &quot;362&quot;,
    &quot;descrizione&quot; : &quot;TAGIKA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 168,
    &quot;codice&quot; : &quot;349&quot;,
    &quot;descrizione&quot; : &quot;TAILANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 169,
    &quot;codice&quot; : &quot;363&quot;,
    &quot;descrizione&quot; : &quot;TAIWAN&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 170,
    &quot;codice&quot; : &quot;457&quot;,
    &quot;descrizione&quot; : &quot;TANZANIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 171,
    &quot;codice&quot; : &quot;216&quot;,
    &quot;descrizione&quot; : &quot;TEDESCA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 172,
    &quot;codice&quot; : &quot;338&quot;,
    &quot;descrizione&quot; : &quot;TIMOR ORIENTALE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 173,
    &quot;codice&quot; : &quot;458&quot;,
    &quot;descrizione&quot; : &quot;TOGOLESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 174,
    &quot;codice&quot; : &quot;730&quot;,
    &quot;descrizione&quot; : &quot;TONGA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 175,
    &quot;codice&quot; : &quot;617&quot;,
    &quot;descrizione&quot; : &quot;TRINIDAD E TOBAGO&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 176,
    &quot;codice&quot; : &quot;460&quot;,
    &quot;descrizione&quot; : &quot;TUNISINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 177,
    &quot;codice&quot; : &quot;351&quot;,
    &quot;descrizione&quot; : &quot;TURCA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 178,
    &quot;codice&quot; : &quot;364&quot;,
    &quot;descrizione&quot; : &quot;TURKMENA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 179,
    &quot;codice&quot; : &quot;243&quot;,
    &quot;descrizione&quot; : &quot;UCRAINA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 180,
    &quot;codice&quot; : &quot;461&quot;,
    &quot;descrizione&quot; : &quot;UGANDESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 181,
    &quot;codice&quot; : &quot;244&quot;,
    &quot;descrizione&quot; : &quot;UNGHERESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 182,
    &quot;codice&quot; : &quot;618&quot;,
    &quot;descrizione&quot; : &quot;URUGUAIANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 183,
    &quot;codice&quot; : &quot;357&quot;,
    &quot;descrizione&quot; : &quot;UZBEKA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 184,
    &quot;codice&quot; : &quot;732&quot;,
    &quot;descrizione&quot; : &quot;VANUATU&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 185,
    &quot;codice&quot; : &quot;619&quot;,
    &quot;descrizione&quot; : &quot;VENZUELANA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 186,
    &quot;codice&quot; : &quot;353&quot;,
    &quot;descrizione&quot; : &quot;VIETNAMITA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 187,
    &quot;codice&quot; : &quot;354&quot;,
    &quot;descrizione&quot; : &quot;YEMENITA&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 188,
    &quot;codice&quot; : &quot;464&quot;,
    &quot;descrizione&quot; : &quot;ZAMBESE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  }, {
    &quot;id&quot; : 189,
    &quot;codice&quot; : &quot;465&quot;,
    &quot;descrizione&quot; : &quot;ZIMBABWE&quot;,
    &quot;flagAttivo&quot; : true,
    &quot;StatusCode&quot; : 200
  } ],
  &quot;AllStati&quot; : {
    &quot;Options&quot; : [ {
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmdUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmgUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmhUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmoUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmtUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmwUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CILE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnBUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnKUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCndUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCneUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCngUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnhUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCniUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnlUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnmUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnoUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCntUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GABON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnuUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo7UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo8UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoBUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCobUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCocUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCodUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCofUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCogUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z000&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCokUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jColUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jComUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jConUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCooUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCorUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCosUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCotUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCouUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCovUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCowUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCozUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp8UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpPUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpUUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpgUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCphUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpiUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCplUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpnUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCppUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCprUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpsUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCptUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpwUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq0UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq6UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqDUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqEUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqGUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqIUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqKUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqMUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqNUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqOUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqQUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqRUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqSUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqTUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqVUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqWUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqXUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqYUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqZUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqaUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqbUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqcUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqdUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqeUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqfUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqjUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqkUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqmUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqpUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqqUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqrUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqtUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCquUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqvUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqxUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqyUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqzUAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr1UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr2UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr3UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr4UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr5UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr7UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr9UAE&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrAUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrCUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrFUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrHUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrJUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrLUAU&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;
    } ]
  },
  &quot;id&quot; : 430,
  &quot;compagnia&quot; : &quot;unipolsai&quot;,
  &quot;ciu&quot; : 7176357,
  &quot;tipologiaSocietaFatca&quot; : &quot;0&quot;,
  &quot;residenzaFiscaleUSA&quot; : true,
  &quot;residenzaFiscale&quot; : true,
  &quot;cittadinanzaUSA&quot; : true,
  &quot;cittadinanze&quot; : [ {
    &quot;codice&quot; : &quot;602&quot;,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;flagAttivo&quot; : null
  } ],
  &quot;poteriFirmaUSA&quot; : false,
  &quot;poteriFirma&quot; : true,
  &quot;potCittadinanzaUsa&quot; : false,
  &quot;tin&quot; : &quot;12345TYUF&quot;,
  &quot;giin&quot; : null,
  &quot;indirizzo&quot; : null,
  &quot;residenzeFiscaliEstere&quot; : [ {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z112&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : &quot;44455&quot;
  }, {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z139&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : &quot;45678&quot;
  }, {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z130&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : null
  } ],
  &quot;ErrorMessage&quot; : &quot;&quot;,
  &quot;ErrorType&quot; : &quot;&quot;,
  &quot;StatusCode&quot; : 200
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>DatiFatcaTrasformResponse</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem30</globalKey>
        <inputFieldName>AllStati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem31</globalKey>
        <inputFieldName>id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem28</globalKey>
        <inputFieldName>ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem29</globalKey>
        <inputFieldName>residenzaFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem34</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Estero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem35</globalKey>
        <inputFieldName>poteriFirmaUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PoteriFirmaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem32</globalKey>
        <inputFieldName>ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem33</globalKey>
        <inputFieldName>cittadinanzaUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>CittadinanzaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem36</globalKey>
        <inputFieldName>Loop:FlagIdentificazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:FlagIdentificazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>var:Loop:LoopBlockIterationIndex 1 -</formulaConverted>
        <formulaExpression>Loop:LoopBlockIterationIndex - 1</formulaExpression>
        <formulaResultPath>Index</formulaResultPath>
        <formulaSequence>5.0</formulaSequence>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem37</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceABI</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:CodiceABI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem40</globalKey>
        <inputFieldName>DettagliModifica:RecordId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:RecordId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem41</globalKey>
        <inputFieldName>DettagliModifica:PaeseDiNascitaUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>PaeseDiNascitaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem38</globalKey>
        <inputFieldName>DettagliModifica:RecordId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RecordId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem39</globalKey>
        <inputFieldName>DettagliModifica:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem42</globalKey>
        <inputFieldName>AllCittadinanze:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>N/D</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem43</globalKey>
        <inputFieldName>DettagliModifica:CodiceFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:
  var:
    var:Loop:NumeroIdentificazioneFiscale var:null == var:
    var:Loop:NumeroIdentificazioneFiscale &quot;&quot; == var:Loop:NumeroIdentificazioneFiscale &quot;-&quot; var:
  == OR var:
  false var:
  var:true
 IF</formulaConverted>
        <formulaExpression>IF(
  OR(
    Loop:NumeroIdentificazioneFiscale == null,
    Loop:NumeroIdentificazioneFiscale == &quot;&quot;,
    Loop:NumeroIdentificazioneFiscale == &quot;-&quot;
  ),
  false,
  true
)</formulaExpression>
        <formulaResultPath>FlagIdentificazione</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:DettagliModifica:CodiceFiscale &quot;Z404&quot; SUBSTRING INCLUDES</formulaConverted>
        <formulaExpression>INCLUDES (SUBSTRING(DettagliModifica:CodiceFiscale, &quot;Z404&quot;))</formulaExpression>
        <formulaResultPath>Test</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>N/D</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem5</globalKey>
        <inputFieldName>DettagliModifica:PaeseDiResidenza</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>PaeseDiResidenza</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>N/D</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem6</globalKey>
        <inputFieldName>tipologiaSocietaFatca</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>TipologiaSocietaFatca</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:DettagliModifica:PaeseDiNascitaUSA true == | var:DettagliModifica:CodiceFiscale &quot;Z404&quot; SUBSTRING INCLUDES &amp;&amp; true false IF</formulaConverted>
        <formulaExpression>IF(DettagliModifica:PaeseDiNascitaUSA == true &amp;&amp; INCLUDES (SUBSTRING(DettagliModifica:CodiceFiscale, &quot;Z404&quot;))  , true, false)</formulaExpression>
        <formulaResultPath>ModCitt</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:cittadinanze TOSTRING</formulaConverted>
        <formulaExpression>TOSTRING(cittadinanze)</formulaExpression>
        <formulaResultPath>ListCittadinanze</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem9</globalKey>
        <inputFieldName>potCittadinanzaUsa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PotCittadinanzaUsa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem10</globalKey>
        <inputFieldName>DettagliModifica:PaeseDiNascita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>PaeseDiNascita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem7</globalKey>
        <inputFieldName>AllStati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:OptionsStati:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem8</globalKey>
        <inputFieldName>residenzaFiscaleUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem11</globalKey>
        <inputFieldName>Loop:NumeroIdentificazioneFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:NumeroIdentificazioneFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem14</globalKey>
        <inputFieldName>poteriFirma</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PoteriFirmaEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{ }</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem15</globalKey>
        <inputFieldName>tin</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>TinUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem12</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Ue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:UnioneEuropea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Cittadinanze</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem18</globalKey>
        <inputFieldName>cittadinanze:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Cittadinanze:Codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem19</globalKey>
        <inputFieldName>AllStati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem16</globalKey>
        <inputFieldName>AllStati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:OptionsStati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem17</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem22</globalKey>
        <inputFieldName>cittadinanze:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Cittadinanze:Descrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem23</globalKey>
        <inputFieldName>DettagliModifica:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Estero:Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem20</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Estero:CodiceBelfiore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem21</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:CodiceBelfioreOld</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem26</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsStati</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem27</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Attivo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:Attivo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem24</globalKey>
        <inputFieldName>AllCittadinanze:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>DatiFatcaTrasformResponseCustom0jI9V000000yu0vUAAItem25</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>DatiFatcaTrasformResponse</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:Nazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Loop&quot; : [ {
    &quot;LoopBlockIterationIndex&quot; : 1,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiLoadStatus&quot; : true,
    &quot;FlagIdentificazione&quot; : false,
    &quot;NumeroIdentificazioneFiscale&quot; : null,
    &quot;ResidenzaFiscale&quot; : null,
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z131&quot;
  }, {
    &quot;LoopBlockIterationIndex&quot; : 2,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiLoadStatus&quot; : true,
    &quot;FlagIdentificazione&quot; : false,
    &quot;NumeroIdentificazioneFiscale&quot; : null,
    &quot;ResidenzaFiscale&quot; : null,
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z100&quot;
  } ],
  &quot;DettagliModifica&quot; : {
    &quot;PaeseDiNascita&quot; : &quot;086&quot;,
    &quot;Societa&quot; : &quot;Unipol&quot;,
    &quot;SourceSystemIdentifier&quot; : &quot;7179358&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;N/D&quot;,
    &quot;RecordId&quot; : &quot;a1i9V000006ZLseQAG&quot;,
    &quot;CodiceStato&quot; : &quot;Z000&quot;,
    &quot;CodiceFiscale&quot; : &quot;****************&quot;,
    &quot;PaeseDiNascitaUSA&quot; : false
  },
  &quot;id&quot; : 646,
  &quot;compagnia&quot; : &quot;unipolsai&quot;,
  &quot;ciu&quot; : 7179358,
  &quot;tipologiaSocietaFatca&quot; : &quot;0&quot;,
  &quot;residenzaFiscaleUSA&quot; : false,
  &quot;residenzaFiscale&quot; : true,
  &quot;cittadinanzaUSA&quot; : false,
  &quot;cittadinanze&quot; : [ {
    &quot;codice&quot; : &quot;215&quot;,
    &quot;descrizione&quot; : &quot;FRANCESE&quot;,
    &quot;flagAttivo&quot; : null
  }, {
    &quot;codice&quot; : &quot;326&quot;,
    &quot;descrizione&quot; : &quot;GIAPPONESE&quot;,
    &quot;flagAttivo&quot; : null
  } ],
  &quot;poteriFirmaUSA&quot; : false,
  &quot;poteriFirma&quot; : false,
  &quot;potCittadinanzaUsa&quot; : false,
  &quot;tin&quot; : &quot;-&quot;,
  &quot;giin&quot; : null,
  &quot;indirizzo&quot; : null,
  &quot;residenzeFiscaliEstere&quot; : [ {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z131&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : null
  }, {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z100&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : null
  } ],
  &quot;ErrorMessage&quot; : &quot;&quot;,
  &quot;ErrorType&quot; : &quot;&quot;,
  &quot;StatusCode&quot; : 200
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>DatiFatcaTrasformResponse_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
