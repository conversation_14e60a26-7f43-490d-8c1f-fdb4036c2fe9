<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;fieldName&quot; : &quot;Text&quot;
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetrieveAnagCodeExt</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;712&apos;</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom3537</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;713&apos;</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom4701</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>$Vlocity.NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeExtCustom0jI9V000000zDTNUA2Item1</globalKey>
        <inputFieldName>mappingRecords:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;711&apos;</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom9890</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>fieldName</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom4709</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>$Vlocity.NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeExtCustom0jI9V000000zDTNUA2Item3</globalKey>
        <inputFieldName>mappingRecords:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>descrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>1.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>fieldName</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom2060</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>2.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>fieldName</filterValue>
        <globalKey>RetrieveAnagCodeExtCustom8788</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>$Vlocity.NULL</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeExtCustom0jI9V000000zDTNUA2Item2</globalKey>
        <inputFieldName>mappingRecords:Codice__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;fieldName&quot; : &quot;AtecoSintetico&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetrieveAnagCodeExt_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
