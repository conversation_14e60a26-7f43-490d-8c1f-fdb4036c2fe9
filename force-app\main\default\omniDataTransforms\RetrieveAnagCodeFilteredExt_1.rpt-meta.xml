<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;fieldName&quot; : &quot;Text&quot;
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetrieveAnagCodeFilteredExt</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:mappingRecords LIST &apos;Codice__c/\/\/==/\/\/&quot;704&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;705&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;706&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;707&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;708&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;709&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;713&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;714&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;715&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;717&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;718&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;724&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;725&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;726&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;727&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;728&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;729&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;746&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;747&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;753&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;754&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;755&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;756&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;757&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;758&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;759&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;763&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;764&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;765&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;766&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;767&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;768&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;769&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;770&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;771&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;772&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;773&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;774&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;775&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;776&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;778&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;779&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;782&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;783&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;784&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;785&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;790&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;791&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;794&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;800&quot;/\/\/OR/\/\/Codice__c/\/\/==/\/\/&quot;801&quot;&apos; FILTER</formulaConverted>
        <formulaExpression>FILTER(LIST(mappingRecords), &apos;Codice__c == &quot;704&quot; OR Codice__c == &quot;705&quot; OR Codice__c == &quot;706&quot; OR Codice__c == &quot;707&quot; OR Codice__c == &quot;708&quot; OR Codice__c == &quot;709&quot; OR Codice__c == &quot;713&quot; OR Codice__c == &quot;714&quot; OR Codice__c == &quot;715&quot; OR Codice__c == &quot;717&quot; OR Codice__c == &quot;718&quot; OR Codice__c == &quot;724&quot; OR Codice__c == &quot;725&quot; OR Codice__c == &quot;726&quot; OR Codice__c == &quot;727&quot; OR Codice__c == &quot;728&quot; OR Codice__c == &quot;729&quot; OR Codice__c == &quot;746&quot; OR Codice__c == &quot;747&quot; OR Codice__c == &quot;753&quot; OR Codice__c == &quot;754&quot; OR Codice__c == &quot;755&quot; OR Codice__c == &quot;756&quot; OR Codice__c == &quot;757&quot; OR Codice__c == &quot;758&quot; OR Codice__c == &quot;759&quot; OR Codice__c == &quot;763&quot; OR Codice__c == &quot;764&quot; OR Codice__c == &quot;765&quot; OR Codice__c == &quot;766&quot; OR Codice__c == &quot;767&quot; OR Codice__c == &quot;768&quot; OR Codice__c == &quot;769&quot; OR Codice__c == &quot;770&quot; OR Codice__c == &quot;771&quot; OR Codice__c == &quot;772&quot; OR Codice__c == &quot;773&quot; OR Codice__c == &quot;774&quot; OR Codice__c == &quot;775&quot; OR Codice__c == &quot;776&quot; OR Codice__c == &quot;778&quot; OR Codice__c == &quot;779&quot; OR Codice__c == &quot;782&quot; OR Codice__c == &quot;783&quot; OR Codice__c == &quot;784&quot; OR Codice__c == &quot;785&quot; OR Codice__c == &quot;790&quot; OR Codice__c == &quot;791&quot; OR Codice__c == &quot;794&quot; OR Codice__c == &quot;800&quot; OR Codice__c == &quot;801&quot;&apos;)</formulaExpression>
        <formulaResultPath>resultList</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>RetrieveAnagCodeFilteredExtCustom3178</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeFilteredExtCustom9917</globalKey>
        <inputFieldName>resultList:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>descrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeFilteredExtCustom4281</globalKey>
        <inputFieldName>resultList:Codice__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>codice</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&apos;&apos;</filterValue>
        <globalKey>RetrieveAnagCodeFilteredExtCustom8214</globalKey>
        <inputFieldName>Codice__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetrieveAnagCodeFilteredExtCustom9701</globalKey>
        <inputFieldName>resultList:Valore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>fieldName</filterValue>
        <globalKey>RetrieveAnagCodeFilteredExtCustom0jI9V000000zLHFUA2Item0</globalKey>
        <inputFieldName>Field__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&apos;&apos;</filterValue>
        <globalKey>RetrieveAnagCodeFilteredExtCustom1886</globalKey>
        <inputFieldName>Valore__c</inputFieldName>
        <inputObjectName>AnagMappingCodDesc__mdt</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetrieveAnagCodeFilteredExt</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>mappingRecords</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;fieldName&quot; : &quot;AtecoSae&quot;,
  &quot;codeList&quot; : &quot;000&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetrieveAnagCodeFilteredExt_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
