<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;LoadDetail&quot; : null,
    &quot;InsertGroup&quot; : null,
    &quot;PreAddGroup&quot; : null,
    &quot;LoadData&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null,
  &quot;LoadDetail&quot; : {
    &quot;TryCatchBlock4&quot; : { }
  },
  &quot;InsertGroup&quot; : {
    &quot;TryCatchBlock3&quot; : { }
  },
  &quot;PreAddGroup&quot; : {
    &quot;TryCatchBlock2&quot; : { }
  },
  &quot;LoadData&quot; : {
    &quot;TryCatchBlock1&quot; : { }
  }
}</expectedInputJson>
    <expectedOutputJson>{
  &quot;isCollaborativoAgenda&quot; : true,
  &quot;Users&quot; : [ { } ],
  &quot;NumeroUtenti&quot; : &quot;Text&quot;,
  &quot;CollaborativoId&quot; : &quot;Text&quot;,
  &quot;DataCreazione&quot; : &quot;Text&quot;,
  &quot;Nome&quot; : &quot;Text&quot;,
  &quot;Descrizione&quot; : &quot;Text&quot;
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetriveCollaborativoDetail</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom271</globalKey>
        <inputFieldName>Collaborativo:Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Nome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom3562</globalKey>
        <inputFieldName>Collaborativo:CreatedDate</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Date(MM/dd/yyyy)</outputFieldFormat>
        <outputFieldName>DataCreazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecordId</filterValue>
        <globalKey>RetriveCollaborativoDetailCustom7579</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Collaborativo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom5034</globalKey>
        <inputFieldName>Collaborativo:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CollaborativoId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom2622</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Users</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom8969</globalKey>
        <inputFieldName>Collaborativo:Description__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Descrizione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Utenti LISTSIZE</formulaConverted>
        <formulaExpression>LISTSIZE(Utenti)</formulaExpression>
        <formulaResultPath>NumberUser</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>RetriveCollaborativoDetailCustom2726</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom4214</globalKey>
        <inputFieldName>Collaborativo:CollaborativeAgendaActivation__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>isCollaborativoAgenda</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Group:Id</filterValue>
        <globalKey>RetriveCollaborativoDetailCustom8072</globalKey>
        <inputFieldName>GroupId</inputFieldName>
        <inputObjectName>GroupMember</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Utenti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Collaborativo:PublicGroupId__c</filterValue>
        <globalKey>RetriveCollaborativoDetailCustom7888</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Group</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Group</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom9116</globalKey>
        <inputFieldName>NumberUser</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>NumeroUtenti</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveCollaborativoDetailCustom703</globalKey>
        <inputFieldName>Utenti:UserOrGroup.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveCollaborativoDetail</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Users:Nome</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;RecordId&quot; : &quot;a1j9O000034CBWvQAO&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetriveCollaborativoDetail_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
