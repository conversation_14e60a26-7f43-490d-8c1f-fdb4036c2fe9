<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;LoadEntity&quot; : null,
    &quot;ModifyGroup&quot; : null,
    &quot;DeleteGroup&quot; : null,
    &quot;LoadDetail&quot; : null,
    &quot;InsertGroup&quot; : null,
    &quot;PreAddGroup&quot; : null,
    &quot;LoadData&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null,
  &quot;LoadEntity&quot; : {
    &quot;TryCatchBlock7&quot; : { }
  },
  &quot;ModifyGroup&quot; : {
    &quot;TryCatchBlock6&quot; : { }
  },
  &quot;DeleteGroup&quot; : {
    &quot;TryCatchBlock5&quot; : { }
  },
  &quot;LoadDetail&quot; : {
    &quot;TryCatchBlock4&quot; : { }
  },
  &quot;InsertGroup&quot; : {
    &quot;TryCatchBlock3&quot; : { }
  },
  &quot;PreAddGroup&quot; : {
    &quot;TryCatchBlock2&quot; : { }
  },
  &quot;LoadData&quot; : {
    &quot;TryCatchBlock1&quot; : { }
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>RetriveEntytyByGroup</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom5380</globalKey>
        <inputFieldName>OppShare:Opportunity.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ListOpportunity:Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom618</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>ListOpportunity</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom2085</globalKey>
        <inputFieldName>EventList:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ListEvent:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom8810</globalKey>
        <inputFieldName>OppShare:OpportunityId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ListOpportunity:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom4334</globalKey>
        <inputFieldName>CaseShare:CaseId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ListCase:Id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom5267</globalKey>
        <inputFieldName>EventList:Subject</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ListEvent:Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom2187</globalKey>
        <inputFieldName>CaseShare:Case.CaseNumber</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ListCase:Name</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom8591</globalKey>
        <inputFieldName>SizeGroup</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Number</outputFieldFormat>
        <outputFieldName>EventsSizeGroup</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Collaborativo:PublicGroupId__c</filterValue>
        <globalKey>RetriveEntytyByGroupCustom7008</globalKey>
        <inputFieldName>UserOrGroupId</inputFieldName>
        <inputObjectName>OpportunityShare</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>OppShare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom1836</globalKey>
        <inputFieldName>SizeOpp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Number</outputFieldFormat>
        <outputFieldName>SizeOpp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | | var:CaseShare ISBLANK 0 | var:CaseShare LISTSIZE IF | | var:OppShare ISBLANK 0 | var:OppShare LISTSIZE IF | | var:EventList ISBLANK 0 | var:EventList LISTSIZE IF SUM</formulaConverted>
        <formulaExpression>SUM(IF(ISBLANK(CaseShare), 0, LISTSIZE(CaseShare)), IF(ISBLANK(OppShare), 0, LISTSIZE(OppShare)),IF(ISBLANK(EventList), 0, LISTSIZE(EventList)))</formulaExpression>
        <formulaResultPath>Total</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>RetriveEntytyByGroupCustom7222</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>&lt;&gt;</filterOperator>
        <filterValue>&apos;Prodotto&apos;</filterValue>
        <globalKey>RetriveEntytyByGroupCustom5586</globalKey>
        <inputFieldName>Opportunity.RecordType.DeveloperName</inputFieldName>
        <inputObjectName>OpportunityShare</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>OppShare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Collaborativo:PublicGroupId__c</filterValue>
        <globalKey>RetriveEntytyByGroupCustom7796</globalKey>
        <inputFieldName>UserOrGroupId</inputFieldName>
        <inputObjectName>CaseShare</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>CaseShare</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom7224</globalKey>
        <inputFieldName>SizeCase</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Number</outputFieldFormat>
        <outputFieldName>SizeCase</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom7832</globalKey>
        <inputFieldName>Total</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Number</outputFieldFormat>
        <outputFieldName>Totale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom2367</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>ListCase</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>RetriveEntytyByGroupCustom637</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>ListEvent</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:OppShare ISBLANK 0 | var:OppShare LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(OppShare), 0, LISTSIZE(OppShare))</formulaExpression>
        <formulaResultPath>SizeOpp</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>RetriveEntytyByGroupCustom3155</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:CaseShare ISBLANK 0 | var:CaseShare LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(CaseShare), 0, LISTSIZE(CaseShare))</formulaExpression>
        <formulaResultPath>SizeCase</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>RetriveEntytyByGroupCustom4912</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>RecordId</filterValue>
        <globalKey>RetriveEntytyByGroupCustom3236</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>Group__c</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Collaborativo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>Collaborativo:Id</filterValue>
        <globalKey>RetriveEntytyByGroupCustom660</globalKey>
        <inputFieldName>Group__c</inputFieldName>
        <inputObjectName>Event</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>EventList</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:EventList ISBLANK 0 | var:EventList LISTSIZE IF</formulaConverted>
        <formulaExpression>IF(ISBLANK(EventList), 0, LISTSIZE(EventList))</formulaExpression>
        <formulaResultPath>SizeGroup</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>RetriveEntytyByGroupCustom4482</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>RetriveEntytyByGroup</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;RecordId&quot; : &quot;a1j9O000034CBWvQAO&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>RetriveEntytyByGroup_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
