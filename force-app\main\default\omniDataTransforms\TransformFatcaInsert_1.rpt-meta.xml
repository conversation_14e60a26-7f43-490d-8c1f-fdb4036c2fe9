<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Cittadinanze&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AFGHANA&quot;,
    &quot;codice&quot; : &quot;301&quot;,
    &quot;id&quot; : 1
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ALBANESE&quot;,
    &quot;codice&quot; : &quot;201&quot;,
    &quot;id&quot; : 2
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ALGERINA&quot;,
    &quot;codice&quot; : &quot;401&quot;,
    &quot;id&quot; : 3
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ANDORRANA&quot;,
    &quot;codice&quot; : &quot;202&quot;,
    &quot;id&quot; : 4
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ANGOLANA&quot;,
    &quot;codice&quot; : &quot;402&quot;,
    &quot;id&quot; : 5
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;codice&quot; : &quot;602&quot;,
    &quot;id&quot; : 6
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ARMENA&quot;,
    &quot;codice&quot; : &quot;358&quot;,
    &quot;id&quot; : 7
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRALIANA&quot;,
    &quot;codice&quot; : &quot;701&quot;,
    &quot;id&quot; : 8
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRIACA&quot;,
    &quot;codice&quot; : &quot;203&quot;,
    &quot;id&quot; : 9
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AZERBAIGIAN&quot;,
    &quot;codice&quot; : &quot;359&quot;,
    &quot;id&quot; : 10
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BAHAMAS&quot;,
    &quot;codice&quot; : &quot;505&quot;,
    &quot;id&quot; : 11
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BAHREIN&quot;,
    &quot;codice&quot; : &quot;304&quot;,
    &quot;id&quot; : 12
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BANGLADESH&quot;,
    &quot;codice&quot; : &quot;305&quot;,
    &quot;id&quot; : 13
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BARBADOS&quot;,
    &quot;codice&quot; : &quot;506&quot;,
    &quot;id&quot; : 14
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BELGA&quot;,
    &quot;codice&quot; : &quot;206&quot;,
    &quot;id&quot; : 15
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BELIZE&quot;,
    &quot;codice&quot; : &quot;507&quot;,
    &quot;id&quot; : 16
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BENIN&quot;,
    &quot;codice&quot; : &quot;406&quot;,
    &quot;id&quot; : 17
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BHUTANESE&quot;,
    &quot;codice&quot; : &quot;306&quot;,
    &quot;id&quot; : 18
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BIELORUSSA&quot;,
    &quot;codice&quot; : &quot;256&quot;,
    &quot;id&quot; : 19
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BIRMANA&quot;,
    &quot;codice&quot; : &quot;307&quot;,
    &quot;id&quot; : 20
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOLIVIANA&quot;,
    &quot;codice&quot; : &quot;604&quot;,
    &quot;id&quot; : 21
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOSNIACA&quot;,
    &quot;codice&quot; : &quot;252&quot;,
    &quot;id&quot; : 22
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOTSWANA&quot;,
    &quot;codice&quot; : &quot;408&quot;,
    &quot;id&quot; : 23
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRASILIANA&quot;,
    &quot;codice&quot; : &quot;605&quot;,
    &quot;id&quot; : 24
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRITANNICA&quot;,
    &quot;codice&quot; : &quot;219&quot;,
    &quot;id&quot; : 25
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRUNEI&quot;,
    &quot;codice&quot; : &quot;309&quot;,
    &quot;id&quot; : 26
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BULGARA&quot;,
    &quot;codice&quot; : &quot;209&quot;,
    &quot;id&quot; : 27
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BURKINA FASO&quot;,
    &quot;codice&quot; : &quot;409&quot;,
    &quot;id&quot; : 28
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BURUNDI&quot;,
    &quot;codice&quot; : &quot;410&quot;,
    &quot;id&quot; : 29
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAMBOGIANA&quot;,
    &quot;codice&quot; : &quot;310&quot;,
    &quot;id&quot; : 30
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAMERUNENSE&quot;,
    &quot;codice&quot; : &quot;411&quot;,
    &quot;id&quot; : 31
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CANADESE&quot;,
    &quot;codice&quot; : &quot;509&quot;,
    &quot;id&quot; : 32
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAPOVERDIANA&quot;,
    &quot;codice&quot; : &quot;413&quot;,
    &quot;id&quot; : 33
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CECA&quot;,
    &quot;codice&quot; : &quot;257&quot;,
    &quot;id&quot; : 34
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CENTRAFRICANA, REPUBBLICA&quot;,
    &quot;codice&quot; : &quot;414&quot;,
    &quot;id&quot; : 35
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CIAD&quot;,
    &quot;codice&quot; : &quot;415&quot;,
    &quot;id&quot; : 36
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CILENA&quot;,
    &quot;codice&quot; : &quot;606&quot;,
    &quot;id&quot; : 37
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CINGALESE&quot;,
    &quot;codice&quot; : &quot;311&quot;,
    &quot;id&quot; : 38
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CIPRIOTA&quot;,
    &quot;codice&quot; : &quot;315&quot;,
    &quot;id&quot; : 39
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COLOMBIANA&quot;,
    &quot;codice&quot; : &quot;608&quot;,
    &quot;id&quot; : 40
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COMORE&quot;,
    &quot;codice&quot; : &quot;417&quot;,
    &quot;id&quot; : 41
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CONGO, REP.DEMOCRATICA&quot;,
    &quot;codice&quot; : &quot;463&quot;,
    &quot;id&quot; : 42
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CONGOLESE&quot;,
    &quot;codice&quot; : &quot;418&quot;,
    &quot;id&quot; : 43
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COSTATICANA&quot;,
    &quot;codice&quot; : &quot;513&quot;,
    &quot;id&quot; : 44
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CROATA&quot;,
    &quot;codice&quot; : &quot;250&quot;,
    &quot;id&quot; : 45
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CUBANA&quot;,
    &quot;codice&quot; : &quot;514&quot;,
    &quot;id&quot; : 46
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;DANESE&quot;,
    &quot;codice&quot; : &quot;212&quot;,
    &quot;id&quot; : 47
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;DOMINICA&quot;,
    &quot;codice&quot; : &quot;515&quot;,
    &quot;id&quot; : 48
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ECUADOREGNA&quot;,
    &quot;codice&quot; : &quot;609&quot;,
    &quot;id&quot; : 49
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EGIZIANA&quot;,
    &quot;codice&quot; : &quot;419&quot;,
    &quot;id&quot; : 50
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;codice&quot; : &quot;322&quot;,
    &quot;id&quot; : 51
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ERITREA&quot;,
    &quot;codice&quot; : &quot;466&quot;,
    &quot;id&quot; : 52
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ESTONE&quot;,
    &quot;codice&quot; : &quot;247&quot;,
    &quot;id&quot; : 53
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ETIOPE&quot;,
    &quot;codice&quot; : &quot;420&quot;,
    &quot;id&quot; : 54
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EX REP. JUGOSLAVIA MACEDONE&quot;,
    &quot;codice&quot; : &quot;253&quot;,
    &quot;id&quot; : 55
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FEDERAZIONE RUSSA&quot;,
    &quot;codice&quot; : &quot;245&quot;,
    &quot;id&quot; : 56
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FIGI&quot;,
    &quot;codice&quot; : &quot;703&quot;,
    &quot;id&quot; : 57
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FILIPPINA&quot;,
    &quot;codice&quot; : &quot;323&quot;,
    &quot;id&quot; : 58
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FINLANDESE&quot;,
    &quot;codice&quot; : &quot;214&quot;,
    &quot;id&quot; : 59
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FRANCESE&quot;,
    &quot;codice&quot; : &quot;215&quot;,
    &quot;id&quot; : 60
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GABON&quot;,
    &quot;codice&quot; : &quot;421&quot;,
    &quot;id&quot; : 61
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GAMBIA&quot;,
    &quot;codice&quot; : &quot;422&quot;,
    &quot;id&quot; : 62
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GEORGIANA&quot;,
    &quot;codice&quot; : &quot;360&quot;,
    &quot;id&quot; : 63
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GHANESE&quot;,
    &quot;codice&quot; : &quot;423&quot;,
    &quot;id&quot; : 64
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIAMAICANA&quot;,
    &quot;codice&quot; : &quot;518&quot;,
    &quot;id&quot; : 65
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIAPPONESE&quot;,
    &quot;codice&quot; : &quot;326&quot;,
    &quot;id&quot; : 66
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIBUTI&quot;,
    &quot;codice&quot; : &quot;424&quot;,
    &quot;id&quot; : 67
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIORDANA&quot;,
    &quot;codice&quot; : &quot;327&quot;,
    &quot;id&quot; : 68
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GRECA&quot;,
    &quot;codice&quot; : &quot;220&quot;,
    &quot;id&quot; : 69
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUATEMALTECA&quot;,
    &quot;codice&quot; : &quot;523&quot;,
    &quot;id&quot; : 70
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA&quot;,
    &quot;codice&quot; : &quot;425&quot;,
    &quot;id&quot; : 71
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA BISSAU&quot;,
    &quot;codice&quot; : &quot;426&quot;,
    &quot;id&quot; : 72
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA EQUATORIALE&quot;,
    &quot;codice&quot; : &quot;427&quot;,
    &quot;id&quot; : 73
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUYANA&quot;,
    &quot;codice&quot; : &quot;612&quot;,
    &quot;id&quot; : 74
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;HAITIANA&quot;,
    &quot;codice&quot; : &quot;524&quot;,
    &quot;id&quot; : 75
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;HONDUREGNA&quot;,
    &quot;codice&quot; : &quot;525&quot;,
    &quot;id&quot; : 76
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;INDIANA&quot;,
    &quot;codice&quot; : &quot;330&quot;,
    &quot;id&quot; : 77
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;INDONESIANA&quot;,
    &quot;codice&quot; : &quot;331&quot;,
    &quot;id&quot; : 78
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRACHENA&quot;,
    &quot;codice&quot; : &quot;333&quot;,
    &quot;id&quot; : 79
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRANIANA&quot;,
    &quot;codice&quot; : &quot;332&quot;,
    &quot;id&quot; : 80
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRLANDESE&quot;,
    &quot;codice&quot; : &quot;221&quot;,
    &quot;id&quot; : 81
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ISLANDESE&quot;,
    &quot;codice&quot; : &quot;223&quot;,
    &quot;id&quot; : 82
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ISRAELIANA&quot;,
    &quot;codice&quot; : &quot;334&quot;,
    &quot;id&quot; : 83
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ITALIANA&quot;,
    &quot;codice&quot; : &quot;0&quot;,
    &quot;id&quot; : 84
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IVORIANA&quot;,
    &quot;codice&quot; : &quot;404&quot;,
    &quot;id&quot; : 85
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KAZAKA&quot;,
    &quot;codice&quot; : &quot;356&quot;,
    &quot;id&quot; : 86
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KENIANA&quot;,
    &quot;codice&quot; : &quot;428&quot;,
    &quot;id&quot; : 87
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KIRGISA&quot;,
    &quot;codice&quot; : &quot;361&quot;,
    &quot;id&quot; : 88
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KOSOVARA&quot;,
    &quot;codice&quot; : &quot;272&quot;,
    &quot;id&quot; : 89
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KUATIANA&quot;,
    &quot;codice&quot; : &quot;335&quot;,
    &quot;id&quot; : 90
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LAOTIANA&quot;,
    &quot;codice&quot; : &quot;336&quot;,
    &quot;id&quot; : 91
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LESOTHO&quot;,
    &quot;codice&quot; : &quot;429&quot;,
    &quot;id&quot; : 92
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LETTONE&quot;,
    &quot;codice&quot; : &quot;248&quot;,
    &quot;id&quot; : 93
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBANESE&quot;,
    &quot;codice&quot; : &quot;337&quot;,
    &quot;id&quot; : 94
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBERIANA&quot;,
    &quot;codice&quot; : &quot;430&quot;,
    &quot;id&quot; : 95
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBICA&quot;,
    &quot;codice&quot; : &quot;431&quot;,
    &quot;id&quot; : 96
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIECHTENSTEIN&quot;,
    &quot;codice&quot; : &quot;225&quot;,
    &quot;id&quot; : 97
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LITUANA&quot;,
    &quot;codice&quot; : &quot;249&quot;,
    &quot;id&quot; : 98
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LUSSEMBURGHESE&quot;,
    &quot;codice&quot; : &quot;226&quot;,
    &quot;id&quot; : 99
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MADAGASCAR&quot;,
    &quot;codice&quot; : &quot;432&quot;,
    &quot;id&quot; : 100
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALAWI&quot;,
    &quot;codice&quot; : &quot;434&quot;,
    &quot;id&quot; : 101
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALDIVE&quot;,
    &quot;codice&quot; : &quot;339&quot;,
    &quot;id&quot; : 102
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALESIANA&quot;,
    &quot;codice&quot; : &quot;340&quot;,
    &quot;id&quot; : 103
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALI&quot;,
    &quot;codice&quot; : &quot;435&quot;,
    &quot;id&quot; : 104
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALTESE&quot;,
    &quot;codice&quot; : &quot;227&quot;,
    &quot;id&quot; : 105
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAROCCHINA&quot;,
    &quot;codice&quot; : &quot;436&quot;,
    &quot;id&quot; : 106
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MARSHALL, ISOLE&quot;,
    &quot;codice&quot; : &quot;712&quot;,
    &quot;id&quot; : 107
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAURITANIA&quot;,
    &quot;codice&quot; : &quot;437&quot;,
    &quot;id&quot; : 108
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAURITIUS&quot;,
    &quot;codice&quot; : &quot;438&quot;,
    &quot;id&quot; : 109
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MESSICANA&quot;,
    &quot;codice&quot; : &quot;527&quot;,
    &quot;id&quot; : 110
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MOLDOVA&quot;,
    &quot;codice&quot; : &quot;254&quot;,
    &quot;id&quot; : 111
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONEGASCA&quot;,
    &quot;codice&quot; : &quot;229&quot;,
    &quot;id&quot; : 112
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONGOLA&quot;,
    &quot;codice&quot; : &quot;341&quot;,
    &quot;id&quot; : 113
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONTENEGRINA&quot;,
    &quot;codice&quot; : &quot;270&quot;,
    &quot;id&quot; : 114
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MOZAMBICO&quot;,
    &quot;codice&quot; : &quot;440&quot;,
    &quot;id&quot; : 115
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NAMIBIA&quot;,
    &quot;codice&quot; : &quot;441&quot;,
    &quot;id&quot; : 116
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NAURU&quot;,
    &quot;codice&quot; : &quot;715&quot;,
    &quot;id&quot; : 117
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NEOZELANDESE&quot;,
    &quot;codice&quot; : &quot;719&quot;,
    &quot;id&quot; : 118
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NEPALESE&quot;,
    &quot;codice&quot; : &quot;342&quot;,
    &quot;id&quot; : 119
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NICARACUENSE&quot;,
    &quot;codice&quot; : &quot;529&quot;,
    &quot;id&quot; : 120
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NIGER&quot;,
    &quot;codice&quot; : &quot;442&quot;,
    &quot;id&quot; : 121
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NIGERIANA&quot;,
    &quot;codice&quot; : &quot;443&quot;,
    &quot;id&quot; : 122
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NORVEGESE&quot;,
    &quot;codice&quot; : &quot;231&quot;,
    &quot;id&quot; : 123
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;OLANDESE&quot;,
    &quot;codice&quot; : &quot;232&quot;,
    &quot;id&quot; : 124
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;OMAN&quot;,
    &quot;codice&quot; : &quot;343&quot;,
    &quot;id&quot; : 125
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PACHISTANA&quot;,
    &quot;codice&quot; : &quot;344&quot;,
    &quot;id&quot; : 126
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PALESTINESE&quot;,
    &quot;codice&quot; : &quot;324&quot;,
    &quot;id&quot; : 127
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PANAMENSE&quot;,
    &quot;codice&quot; : &quot;530&quot;,
    &quot;id&quot; : 128
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
    &quot;codice&quot; : &quot;721&quot;,
    &quot;id&quot; : 129
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PARAGUAIANA&quot;,
    &quot;codice&quot; : &quot;614&quot;,
    &quot;id&quot; : 130
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PERUVIANA&quot;,
    &quot;codice&quot; : &quot;615&quot;,
    &quot;id&quot; : 131
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;POLACCA&quot;,
    &quot;codice&quot; : &quot;233&quot;,
    &quot;id&quot; : 132
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PORTOGHESE&quot;,
    &quot;codice&quot; : &quot;234&quot;,
    &quot;id&quot; : 133
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;QATAR&quot;,
    &quot;codice&quot; : &quot;345&quot;,
    &quot;id&quot; : 134
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
    &quot;codice&quot; : &quot;516&quot;,
    &quot;id&quot; : 135
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE CINESE&quot;,
    &quot;codice&quot; : &quot;314&quot;,
    &quot;id&quot; : 136
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE NORDCOREAN&quot;,
    &quot;codice&quot; : &quot;319&quot;,
    &quot;id&quot; : 137
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA SUDCOREANA&quot;,
    &quot;codice&quot; : &quot;320&quot;,
    &quot;id&quot; : 138
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ROMENA&quot;,
    &quot;codice&quot; : &quot;235&quot;,
    &quot;id&quot; : 139
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;RUANDA&quot;,
    &quot;codice&quot; : &quot;446&quot;,
    &quot;id&quot; : 140
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAINT KITTS E NEVIS&quot;,
    &quot;codice&quot; : &quot;534&quot;,
    &quot;id&quot; : 141
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SALOMONE, ISOLE&quot;,
    &quot;codice&quot; : &quot;725&quot;,
    &quot;id&quot; : 142
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SALVADOREGNA&quot;,
    &quot;codice&quot; : &quot;517&quot;,
    &quot;id&quot; : 143
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAMOA&quot;,
    &quot;codice&quot; : &quot;727&quot;,
    &quot;id&quot; : 144
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SANMARINESE&quot;,
    &quot;codice&quot; : &quot;236&quot;,
    &quot;id&quot; : 145
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SANTA SEDE&quot;,
    &quot;codice&quot; : &quot;246&quot;,
    &quot;id&quot; : 146
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAO TOME E PRINCIPE&quot;,
    &quot;codice&quot; : &quot;448&quot;,
    &quot;id&quot; : 147
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAUDITA&quot;,
    &quot;codice&quot; : &quot;302&quot;,
    &quot;id&quot; : 148
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SENEGALESE&quot;,
    &quot;codice&quot; : &quot;450&quot;,
    &quot;id&quot; : 149
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SERBA&quot;,
    &quot;codice&quot; : &quot;271&quot;,
    &quot;id&quot; : 150
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SERBA / MONTENEGRINA&quot;,
    &quot;codice&quot; : &quot;224&quot;,
    &quot;id&quot; : 151
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SEYCELLES&quot;,
    &quot;codice&quot; : &quot;449&quot;,
    &quot;id&quot; : 152
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SIERRA LEONE&quot;,
    &quot;codice&quot; : &quot;451&quot;,
    &quot;id&quot; : 153
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SINGAPORE&quot;,
    &quot;codice&quot; : &quot;346&quot;,
    &quot;id&quot; : 154
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SIRIANA&quot;,
    &quot;codice&quot; : &quot;348&quot;,
    &quot;id&quot; : 155
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVACCA&quot;,
    &quot;codice&quot; : &quot;255&quot;,
    &quot;id&quot; : 156
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVENA&quot;,
    &quot;codice&quot; : &quot;251&quot;,
    &quot;id&quot; : 157
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SOMALA&quot;,
    &quot;codice&quot; : &quot;453&quot;,
    &quot;id&quot; : 158
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SPAGNOLA&quot;,
    &quot;codice&quot; : &quot;239&quot;,
    &quot;id&quot; : 159
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;STATUNITENSE&quot;,
    &quot;codice&quot; : &quot;536&quot;,
    &quot;id&quot; : 160
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SUDAFRICANA&quot;,
    &quot;codice&quot; : &quot;454&quot;,
    &quot;id&quot; : 161
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SUDANESE&quot;,
    &quot;codice&quot; : &quot;455&quot;,
    &quot;id&quot; : 162
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SURINAME&quot;,
    &quot;codice&quot; : &quot;616&quot;,
    &quot;id&quot; : 163
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SVEDESE&quot;,
    &quot;codice&quot; : &quot;240&quot;,
    &quot;id&quot; : 164
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SVIZZERA&quot;,
    &quot;codice&quot; : &quot;241&quot;,
    &quot;id&quot; : 165
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SWAZILAND&quot;,
    &quot;codice&quot; : &quot;456&quot;,
    &quot;id&quot; : 166
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAGIKA&quot;,
    &quot;codice&quot; : &quot;362&quot;,
    &quot;id&quot; : 167
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAILANDESE&quot;,
    &quot;codice&quot; : &quot;349&quot;,
    &quot;id&quot; : 168
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAIWAN&quot;,
    &quot;codice&quot; : &quot;363&quot;,
    &quot;id&quot; : 169
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TANZANIANA&quot;,
    &quot;codice&quot; : &quot;457&quot;,
    &quot;id&quot; : 170
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TEDESCA&quot;,
    &quot;codice&quot; : &quot;216&quot;,
    &quot;id&quot; : 171
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TIMOR ORIENTALE&quot;,
    &quot;codice&quot; : &quot;338&quot;,
    &quot;id&quot; : 172
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TOGOLESE&quot;,
    &quot;codice&quot; : &quot;458&quot;,
    &quot;id&quot; : 173
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TONGA&quot;,
    &quot;codice&quot; : &quot;730&quot;,
    &quot;id&quot; : 174
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TRINIDAD E TOBAGO&quot;,
    &quot;codice&quot; : &quot;617&quot;,
    &quot;id&quot; : 175
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TUNISINA&quot;,
    &quot;codice&quot; : &quot;460&quot;,
    &quot;id&quot; : 176
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TURCA&quot;,
    &quot;codice&quot; : &quot;351&quot;,
    &quot;id&quot; : 177
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TURKMENA&quot;,
    &quot;codice&quot; : &quot;364&quot;,
    &quot;id&quot; : 178
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UCRAINA&quot;,
    &quot;codice&quot; : &quot;243&quot;,
    &quot;id&quot; : 179
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UGANDESE&quot;,
    &quot;codice&quot; : &quot;461&quot;,
    &quot;id&quot; : 180
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UNGHERESE&quot;,
    &quot;codice&quot; : &quot;244&quot;,
    &quot;id&quot; : 181
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;URUGUAIANA&quot;,
    &quot;codice&quot; : &quot;618&quot;,
    &quot;id&quot; : 182
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UZBEKA&quot;,
    &quot;codice&quot; : &quot;357&quot;,
    &quot;id&quot; : 183
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VANUATU&quot;,
    &quot;codice&quot; : &quot;732&quot;,
    &quot;id&quot; : 184
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VENZUELANA&quot;,
    &quot;codice&quot; : &quot;619&quot;,
    &quot;id&quot; : 185
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VIETNAMITA&quot;,
    &quot;codice&quot; : &quot;353&quot;,
    &quot;id&quot; : 186
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;YEMENITA&quot;,
    &quot;codice&quot; : &quot;354&quot;,
    &quot;id&quot; : 187
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ZAMBESE&quot;,
    &quot;codice&quot; : &quot;464&quot;,
    &quot;id&quot; : 188
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ZIMBABWE&quot;,
    &quot;codice&quot; : &quot;465&quot;,
    &quot;id&quot; : 189
  } ],
  &quot;AllStati&quot; : {
    &quot;Options&quot; : [ {
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmdUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmgUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmhUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmoUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmtUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmwUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CILE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnBUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnKUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCndUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCneUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCngUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnhUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCniUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnlUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnmUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnoUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCntUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnuUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GABON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo7UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo8UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoBUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCobUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCocUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCodUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCofUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z000&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCogUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCokUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jColUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jComUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jConUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCooUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCorUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCosUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCotUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCouUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCovUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCowUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCozUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp8UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpgUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCphUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCplUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCppUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCprUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCptUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpwUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqKUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqdUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqmUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqtUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCquUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr7UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;
    } ]
  },
  &quot;PaeseDiNascitaUSA&quot; : false,
  &quot;CodiceFiscale&quot; : &quot;****************&quot;,
  &quot;RecordId&quot; : &quot;a1i9X000006nIjVQAU&quot;,
  &quot;PaeseDiResidenza&quot; : &quot;N/D&quot;,
  &quot;SourceSystemIdentifier&quot; : &quot;42800445&quot;,
  &quot;Societa&quot; : &quot;UnipolSai&quot;,
  &quot;PaeseDiNascita&quot; : &quot;086&quot;
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TransformFatcaInsert</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>DisableResidenzaEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem6</globalKey>
        <inputFieldName>CodiceFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CodiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PaeseDiNascitaUSA true == var:CodiceFiscale &quot;Z404&quot; LIKE || false true IF</formulaConverted>
        <formulaExpression>IF(PaeseDiNascitaUSA == true || CodiceFiscale LIKE &quot;Z404&quot; , false, true)</formulaExpression>
        <formulaResultPath>DisableCittUSA</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem4</globalKey>
        <inputFieldName>Cittadinanze:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem9</globalKey>
        <inputFieldName>CittUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>CittadinanzaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem10</globalKey>
        <inputFieldName>ResidenzaFisUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem7</globalKey>
        <inputFieldName>Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem8</globalKey>
        <inputFieldName>AllStati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>DisableCittadinanzaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem12</globalKey>
        <inputFieldName>PaeseDiNascitaUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PaeseDiNascitaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem14</globalKey>
        <inputFieldName>Cittadinanze:codice</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem15</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>DisableResidenzaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>&quot;&quot;</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>ActualCittadinanzeString</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem18</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PoteriFirmaUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem19</globalKey>
        <inputFieldName>AllStati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem16</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsStati</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem17</globalKey>
        <inputFieldName>SourceSystemIdentifier</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem22</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>PoteriFirmaEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem23</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>TinUSA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem20</globalKey>
        <inputFieldName>RecordId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RecordId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem21</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsCittadinanze</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem24</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PaeseDiResidenza &quot;US&quot; == false true IF</formulaConverted>
        <formulaExpression>IF(PaeseDiResidenza == &quot;US&quot; , false, true)</formulaExpression>
        <formulaResultPath>DisableResidenzaFisUSA</formulaResultPath>
        <formulaSequence>4.0</formulaSequence>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PaeseDiNascitaUSA true == var:CodiceFiscale &quot;Z404&quot; LIKE || true false IF</formulaConverted>
        <formulaExpression>IF(PaeseDiNascitaUSA == true || CodiceFiscale LIKE &quot;Z404&quot; , true, false)</formulaExpression>
        <formulaResultPath>CittUSA</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem2</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:PaeseDiResidenza &quot;US&quot; == true false IF</formulaConverted>
        <formulaExpression>IF(PaeseDiResidenza == &quot;US&quot; , true, false)</formulaExpression>
        <formulaResultPath>ResidenzaFisUSA</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>TransformFatcaInsertCustom0jI9V000000ytmPUAQItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformFatcaInsert</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Cittadinanze&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AFGHANA&quot;,
    &quot;codice&quot; : &quot;301&quot;,
    &quot;id&quot; : 1
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ALBANESE&quot;,
    &quot;codice&quot; : &quot;201&quot;,
    &quot;id&quot; : 2
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ALGERINA&quot;,
    &quot;codice&quot; : &quot;401&quot;,
    &quot;id&quot; : 3
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ANDORRANA&quot;,
    &quot;codice&quot; : &quot;202&quot;,
    &quot;id&quot; : 4
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ANGOLANA&quot;,
    &quot;codice&quot; : &quot;402&quot;,
    &quot;id&quot; : 5
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;codice&quot; : &quot;602&quot;,
    &quot;id&quot; : 6
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ARMENA&quot;,
    &quot;codice&quot; : &quot;358&quot;,
    &quot;id&quot; : 7
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRALIANA&quot;,
    &quot;codice&quot; : &quot;701&quot;,
    &quot;id&quot; : 8
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRIACA&quot;,
    &quot;codice&quot; : &quot;203&quot;,
    &quot;id&quot; : 9
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;AZERBAIGIAN&quot;,
    &quot;codice&quot; : &quot;359&quot;,
    &quot;id&quot; : 10
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BAHAMAS&quot;,
    &quot;codice&quot; : &quot;505&quot;,
    &quot;id&quot; : 11
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BAHREIN&quot;,
    &quot;codice&quot; : &quot;304&quot;,
    &quot;id&quot; : 12
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BANGLADESH&quot;,
    &quot;codice&quot; : &quot;305&quot;,
    &quot;id&quot; : 13
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BARBADOS&quot;,
    &quot;codice&quot; : &quot;506&quot;,
    &quot;id&quot; : 14
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BELGA&quot;,
    &quot;codice&quot; : &quot;206&quot;,
    &quot;id&quot; : 15
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BELIZE&quot;,
    &quot;codice&quot; : &quot;507&quot;,
    &quot;id&quot; : 16
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BENIN&quot;,
    &quot;codice&quot; : &quot;406&quot;,
    &quot;id&quot; : 17
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BHUTANESE&quot;,
    &quot;codice&quot; : &quot;306&quot;,
    &quot;id&quot; : 18
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BIELORUSSA&quot;,
    &quot;codice&quot; : &quot;256&quot;,
    &quot;id&quot; : 19
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BIRMANA&quot;,
    &quot;codice&quot; : &quot;307&quot;,
    &quot;id&quot; : 20
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOLIVIANA&quot;,
    &quot;codice&quot; : &quot;604&quot;,
    &quot;id&quot; : 21
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOSNIACA&quot;,
    &quot;codice&quot; : &quot;252&quot;,
    &quot;id&quot; : 22
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BOTSWANA&quot;,
    &quot;codice&quot; : &quot;408&quot;,
    &quot;id&quot; : 23
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRASILIANA&quot;,
    &quot;codice&quot; : &quot;605&quot;,
    &quot;id&quot; : 24
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRITANNICA&quot;,
    &quot;codice&quot; : &quot;219&quot;,
    &quot;id&quot; : 25
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BRUNEI&quot;,
    &quot;codice&quot; : &quot;309&quot;,
    &quot;id&quot; : 26
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BULGARA&quot;,
    &quot;codice&quot; : &quot;209&quot;,
    &quot;id&quot; : 27
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BURKINA FASO&quot;,
    &quot;codice&quot; : &quot;409&quot;,
    &quot;id&quot; : 28
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;BURUNDI&quot;,
    &quot;codice&quot; : &quot;410&quot;,
    &quot;id&quot; : 29
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAMBOGIANA&quot;,
    &quot;codice&quot; : &quot;310&quot;,
    &quot;id&quot; : 30
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAMERUNENSE&quot;,
    &quot;codice&quot; : &quot;411&quot;,
    &quot;id&quot; : 31
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CANADESE&quot;,
    &quot;codice&quot; : &quot;509&quot;,
    &quot;id&quot; : 32
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CAPOVERDIANA&quot;,
    &quot;codice&quot; : &quot;413&quot;,
    &quot;id&quot; : 33
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CECA&quot;,
    &quot;codice&quot; : &quot;257&quot;,
    &quot;id&quot; : 34
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CENTRAFRICANA, REPUBBLICA&quot;,
    &quot;codice&quot; : &quot;414&quot;,
    &quot;id&quot; : 35
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CIAD&quot;,
    &quot;codice&quot; : &quot;415&quot;,
    &quot;id&quot; : 36
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CILENA&quot;,
    &quot;codice&quot; : &quot;606&quot;,
    &quot;id&quot; : 37
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CINGALESE&quot;,
    &quot;codice&quot; : &quot;311&quot;,
    &quot;id&quot; : 38
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CIPRIOTA&quot;,
    &quot;codice&quot; : &quot;315&quot;,
    &quot;id&quot; : 39
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COLOMBIANA&quot;,
    &quot;codice&quot; : &quot;608&quot;,
    &quot;id&quot; : 40
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COMORE&quot;,
    &quot;codice&quot; : &quot;417&quot;,
    &quot;id&quot; : 41
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CONGO, REP.DEMOCRATICA&quot;,
    &quot;codice&quot; : &quot;463&quot;,
    &quot;id&quot; : 42
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CONGOLESE&quot;,
    &quot;codice&quot; : &quot;418&quot;,
    &quot;id&quot; : 43
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;COSTATICANA&quot;,
    &quot;codice&quot; : &quot;513&quot;,
    &quot;id&quot; : 44
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CROATA&quot;,
    &quot;codice&quot; : &quot;250&quot;,
    &quot;id&quot; : 45
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;CUBANA&quot;,
    &quot;codice&quot; : &quot;514&quot;,
    &quot;id&quot; : 46
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;DANESE&quot;,
    &quot;codice&quot; : &quot;212&quot;,
    &quot;id&quot; : 47
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;DOMINICA&quot;,
    &quot;codice&quot; : &quot;515&quot;,
    &quot;id&quot; : 48
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ECUADOREGNA&quot;,
    &quot;codice&quot; : &quot;609&quot;,
    &quot;id&quot; : 49
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EGIZIANA&quot;,
    &quot;codice&quot; : &quot;419&quot;,
    &quot;id&quot; : 50
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;codice&quot; : &quot;322&quot;,
    &quot;id&quot; : 51
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ERITREA&quot;,
    &quot;codice&quot; : &quot;466&quot;,
    &quot;id&quot; : 52
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ESTONE&quot;,
    &quot;codice&quot; : &quot;247&quot;,
    &quot;id&quot; : 53
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ETIOPE&quot;,
    &quot;codice&quot; : &quot;420&quot;,
    &quot;id&quot; : 54
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;EX REP. JUGOSLAVIA MACEDONE&quot;,
    &quot;codice&quot; : &quot;253&quot;,
    &quot;id&quot; : 55
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FEDERAZIONE RUSSA&quot;,
    &quot;codice&quot; : &quot;245&quot;,
    &quot;id&quot; : 56
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FIGI&quot;,
    &quot;codice&quot; : &quot;703&quot;,
    &quot;id&quot; : 57
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FILIPPINA&quot;,
    &quot;codice&quot; : &quot;323&quot;,
    &quot;id&quot; : 58
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FINLANDESE&quot;,
    &quot;codice&quot; : &quot;214&quot;,
    &quot;id&quot; : 59
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;FRANCESE&quot;,
    &quot;codice&quot; : &quot;215&quot;,
    &quot;id&quot; : 60
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GABON&quot;,
    &quot;codice&quot; : &quot;421&quot;,
    &quot;id&quot; : 61
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GAMBIA&quot;,
    &quot;codice&quot; : &quot;422&quot;,
    &quot;id&quot; : 62
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GEORGIANA&quot;,
    &quot;codice&quot; : &quot;360&quot;,
    &quot;id&quot; : 63
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GHANESE&quot;,
    &quot;codice&quot; : &quot;423&quot;,
    &quot;id&quot; : 64
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIAMAICANA&quot;,
    &quot;codice&quot; : &quot;518&quot;,
    &quot;id&quot; : 65
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIAPPONESE&quot;,
    &quot;codice&quot; : &quot;326&quot;,
    &quot;id&quot; : 66
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIBUTI&quot;,
    &quot;codice&quot; : &quot;424&quot;,
    &quot;id&quot; : 67
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GIORDANA&quot;,
    &quot;codice&quot; : &quot;327&quot;,
    &quot;id&quot; : 68
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GRECA&quot;,
    &quot;codice&quot; : &quot;220&quot;,
    &quot;id&quot; : 69
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUATEMALTECA&quot;,
    &quot;codice&quot; : &quot;523&quot;,
    &quot;id&quot; : 70
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA&quot;,
    &quot;codice&quot; : &quot;425&quot;,
    &quot;id&quot; : 71
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA BISSAU&quot;,
    &quot;codice&quot; : &quot;426&quot;,
    &quot;id&quot; : 72
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUINEA EQUATORIALE&quot;,
    &quot;codice&quot; : &quot;427&quot;,
    &quot;id&quot; : 73
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;GUYANA&quot;,
    &quot;codice&quot; : &quot;612&quot;,
    &quot;id&quot; : 74
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;HAITIANA&quot;,
    &quot;codice&quot; : &quot;524&quot;,
    &quot;id&quot; : 75
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;HONDUREGNA&quot;,
    &quot;codice&quot; : &quot;525&quot;,
    &quot;id&quot; : 76
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;INDIANA&quot;,
    &quot;codice&quot; : &quot;330&quot;,
    &quot;id&quot; : 77
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;INDONESIANA&quot;,
    &quot;codice&quot; : &quot;331&quot;,
    &quot;id&quot; : 78
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRACHENA&quot;,
    &quot;codice&quot; : &quot;333&quot;,
    &quot;id&quot; : 79
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRANIANA&quot;,
    &quot;codice&quot; : &quot;332&quot;,
    &quot;id&quot; : 80
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IRLANDESE&quot;,
    &quot;codice&quot; : &quot;221&quot;,
    &quot;id&quot; : 81
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ISLANDESE&quot;,
    &quot;codice&quot; : &quot;223&quot;,
    &quot;id&quot; : 82
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ISRAELIANA&quot;,
    &quot;codice&quot; : &quot;334&quot;,
    &quot;id&quot; : 83
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ITALIANA&quot;,
    &quot;codice&quot; : &quot;0&quot;,
    &quot;id&quot; : 84
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;IVORIANA&quot;,
    &quot;codice&quot; : &quot;404&quot;,
    &quot;id&quot; : 85
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KAZAKA&quot;,
    &quot;codice&quot; : &quot;356&quot;,
    &quot;id&quot; : 86
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KENIANA&quot;,
    &quot;codice&quot; : &quot;428&quot;,
    &quot;id&quot; : 87
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KIRGISA&quot;,
    &quot;codice&quot; : &quot;361&quot;,
    &quot;id&quot; : 88
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KOSOVARA&quot;,
    &quot;codice&quot; : &quot;272&quot;,
    &quot;id&quot; : 89
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;KUATIANA&quot;,
    &quot;codice&quot; : &quot;335&quot;,
    &quot;id&quot; : 90
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LAOTIANA&quot;,
    &quot;codice&quot; : &quot;336&quot;,
    &quot;id&quot; : 91
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LESOTHO&quot;,
    &quot;codice&quot; : &quot;429&quot;,
    &quot;id&quot; : 92
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LETTONE&quot;,
    &quot;codice&quot; : &quot;248&quot;,
    &quot;id&quot; : 93
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBANESE&quot;,
    &quot;codice&quot; : &quot;337&quot;,
    &quot;id&quot; : 94
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBERIANA&quot;,
    &quot;codice&quot; : &quot;430&quot;,
    &quot;id&quot; : 95
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIBICA&quot;,
    &quot;codice&quot; : &quot;431&quot;,
    &quot;id&quot; : 96
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LIECHTENSTEIN&quot;,
    &quot;codice&quot; : &quot;225&quot;,
    &quot;id&quot; : 97
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LITUANA&quot;,
    &quot;codice&quot; : &quot;249&quot;,
    &quot;id&quot; : 98
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;LUSSEMBURGHESE&quot;,
    &quot;codice&quot; : &quot;226&quot;,
    &quot;id&quot; : 99
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MADAGASCAR&quot;,
    &quot;codice&quot; : &quot;432&quot;,
    &quot;id&quot; : 100
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALAWI&quot;,
    &quot;codice&quot; : &quot;434&quot;,
    &quot;id&quot; : 101
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALDIVE&quot;,
    &quot;codice&quot; : &quot;339&quot;,
    &quot;id&quot; : 102
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALESIANA&quot;,
    &quot;codice&quot; : &quot;340&quot;,
    &quot;id&quot; : 103
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALI&quot;,
    &quot;codice&quot; : &quot;435&quot;,
    &quot;id&quot; : 104
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MALTESE&quot;,
    &quot;codice&quot; : &quot;227&quot;,
    &quot;id&quot; : 105
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAROCCHINA&quot;,
    &quot;codice&quot; : &quot;436&quot;,
    &quot;id&quot; : 106
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MARSHALL, ISOLE&quot;,
    &quot;codice&quot; : &quot;712&quot;,
    &quot;id&quot; : 107
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAURITANIA&quot;,
    &quot;codice&quot; : &quot;437&quot;,
    &quot;id&quot; : 108
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MAURITIUS&quot;,
    &quot;codice&quot; : &quot;438&quot;,
    &quot;id&quot; : 109
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MESSICANA&quot;,
    &quot;codice&quot; : &quot;527&quot;,
    &quot;id&quot; : 110
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MOLDOVA&quot;,
    &quot;codice&quot; : &quot;254&quot;,
    &quot;id&quot; : 111
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONEGASCA&quot;,
    &quot;codice&quot; : &quot;229&quot;,
    &quot;id&quot; : 112
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONGOLA&quot;,
    &quot;codice&quot; : &quot;341&quot;,
    &quot;id&quot; : 113
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MONTENEGRINA&quot;,
    &quot;codice&quot; : &quot;270&quot;,
    &quot;id&quot; : 114
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;MOZAMBICO&quot;,
    &quot;codice&quot; : &quot;440&quot;,
    &quot;id&quot; : 115
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NAMIBIA&quot;,
    &quot;codice&quot; : &quot;441&quot;,
    &quot;id&quot; : 116
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NAURU&quot;,
    &quot;codice&quot; : &quot;715&quot;,
    &quot;id&quot; : 117
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NEOZELANDESE&quot;,
    &quot;codice&quot; : &quot;719&quot;,
    &quot;id&quot; : 118
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NEPALESE&quot;,
    &quot;codice&quot; : &quot;342&quot;,
    &quot;id&quot; : 119
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NICARACUENSE&quot;,
    &quot;codice&quot; : &quot;529&quot;,
    &quot;id&quot; : 120
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NIGER&quot;,
    &quot;codice&quot; : &quot;442&quot;,
    &quot;id&quot; : 121
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NIGERIANA&quot;,
    &quot;codice&quot; : &quot;443&quot;,
    &quot;id&quot; : 122
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;NORVEGESE&quot;,
    &quot;codice&quot; : &quot;231&quot;,
    &quot;id&quot; : 123
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;OLANDESE&quot;,
    &quot;codice&quot; : &quot;232&quot;,
    &quot;id&quot; : 124
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;OMAN&quot;,
    &quot;codice&quot; : &quot;343&quot;,
    &quot;id&quot; : 125
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PACHISTANA&quot;,
    &quot;codice&quot; : &quot;344&quot;,
    &quot;id&quot; : 126
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PALESTINESE&quot;,
    &quot;codice&quot; : &quot;324&quot;,
    &quot;id&quot; : 127
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PANAMENSE&quot;,
    &quot;codice&quot; : &quot;530&quot;,
    &quot;id&quot; : 128
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
    &quot;codice&quot; : &quot;721&quot;,
    &quot;id&quot; : 129
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PARAGUAIANA&quot;,
    &quot;codice&quot; : &quot;614&quot;,
    &quot;id&quot; : 130
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PERUVIANA&quot;,
    &quot;codice&quot; : &quot;615&quot;,
    &quot;id&quot; : 131
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;POLACCA&quot;,
    &quot;codice&quot; : &quot;233&quot;,
    &quot;id&quot; : 132
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;PORTOGHESE&quot;,
    &quot;codice&quot; : &quot;234&quot;,
    &quot;id&quot; : 133
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;QATAR&quot;,
    &quot;codice&quot; : &quot;345&quot;,
    &quot;id&quot; : 134
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
    &quot;codice&quot; : &quot;516&quot;,
    &quot;id&quot; : 135
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE CINESE&quot;,
    &quot;codice&quot; : &quot;314&quot;,
    &quot;id&quot; : 136
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA POPOLARE NORDCOREAN&quot;,
    &quot;codice&quot; : &quot;319&quot;,
    &quot;id&quot; : 137
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA SUDCOREANA&quot;,
    &quot;codice&quot; : &quot;320&quot;,
    &quot;id&quot; : 138
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ROMENA&quot;,
    &quot;codice&quot; : &quot;235&quot;,
    &quot;id&quot; : 139
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;RUANDA&quot;,
    &quot;codice&quot; : &quot;446&quot;,
    &quot;id&quot; : 140
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAINT KITTS E NEVIS&quot;,
    &quot;codice&quot; : &quot;534&quot;,
    &quot;id&quot; : 141
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SALOMONE, ISOLE&quot;,
    &quot;codice&quot; : &quot;725&quot;,
    &quot;id&quot; : 142
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SALVADOREGNA&quot;,
    &quot;codice&quot; : &quot;517&quot;,
    &quot;id&quot; : 143
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAMOA&quot;,
    &quot;codice&quot; : &quot;727&quot;,
    &quot;id&quot; : 144
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SANMARINESE&quot;,
    &quot;codice&quot; : &quot;236&quot;,
    &quot;id&quot; : 145
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SANTA SEDE&quot;,
    &quot;codice&quot; : &quot;246&quot;,
    &quot;id&quot; : 146
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAO TOME E PRINCIPE&quot;,
    &quot;codice&quot; : &quot;448&quot;,
    &quot;id&quot; : 147
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SAUDITA&quot;,
    &quot;codice&quot; : &quot;302&quot;,
    &quot;id&quot; : 148
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SENEGALESE&quot;,
    &quot;codice&quot; : &quot;450&quot;,
    &quot;id&quot; : 149
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SERBA&quot;,
    &quot;codice&quot; : &quot;271&quot;,
    &quot;id&quot; : 150
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SERBA / MONTENEGRINA&quot;,
    &quot;codice&quot; : &quot;224&quot;,
    &quot;id&quot; : 151
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SEYCELLES&quot;,
    &quot;codice&quot; : &quot;449&quot;,
    &quot;id&quot; : 152
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SIERRA LEONE&quot;,
    &quot;codice&quot; : &quot;451&quot;,
    &quot;id&quot; : 153
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SINGAPORE&quot;,
    &quot;codice&quot; : &quot;346&quot;,
    &quot;id&quot; : 154
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SIRIANA&quot;,
    &quot;codice&quot; : &quot;348&quot;,
    &quot;id&quot; : 155
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVACCA&quot;,
    &quot;codice&quot; : &quot;255&quot;,
    &quot;id&quot; : 156
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVENA&quot;,
    &quot;codice&quot; : &quot;251&quot;,
    &quot;id&quot; : 157
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SOMALA&quot;,
    &quot;codice&quot; : &quot;453&quot;,
    &quot;id&quot; : 158
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SPAGNOLA&quot;,
    &quot;codice&quot; : &quot;239&quot;,
    &quot;id&quot; : 159
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;STATUNITENSE&quot;,
    &quot;codice&quot; : &quot;536&quot;,
    &quot;id&quot; : 160
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SUDAFRICANA&quot;,
    &quot;codice&quot; : &quot;454&quot;,
    &quot;id&quot; : 161
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SUDANESE&quot;,
    &quot;codice&quot; : &quot;455&quot;,
    &quot;id&quot; : 162
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SURINAME&quot;,
    &quot;codice&quot; : &quot;616&quot;,
    &quot;id&quot; : 163
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SVEDESE&quot;,
    &quot;codice&quot; : &quot;240&quot;,
    &quot;id&quot; : 164
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SVIZZERA&quot;,
    &quot;codice&quot; : &quot;241&quot;,
    &quot;id&quot; : 165
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;SWAZILAND&quot;,
    &quot;codice&quot; : &quot;456&quot;,
    &quot;id&quot; : 166
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAGIKA&quot;,
    &quot;codice&quot; : &quot;362&quot;,
    &quot;id&quot; : 167
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAILANDESE&quot;,
    &quot;codice&quot; : &quot;349&quot;,
    &quot;id&quot; : 168
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TAIWAN&quot;,
    &quot;codice&quot; : &quot;363&quot;,
    &quot;id&quot; : 169
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TANZANIANA&quot;,
    &quot;codice&quot; : &quot;457&quot;,
    &quot;id&quot; : 170
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TEDESCA&quot;,
    &quot;codice&quot; : &quot;216&quot;,
    &quot;id&quot; : 171
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TIMOR ORIENTALE&quot;,
    &quot;codice&quot; : &quot;338&quot;,
    &quot;id&quot; : 172
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TOGOLESE&quot;,
    &quot;codice&quot; : &quot;458&quot;,
    &quot;id&quot; : 173
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TONGA&quot;,
    &quot;codice&quot; : &quot;730&quot;,
    &quot;id&quot; : 174
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TRINIDAD E TOBAGO&quot;,
    &quot;codice&quot; : &quot;617&quot;,
    &quot;id&quot; : 175
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TUNISINA&quot;,
    &quot;codice&quot; : &quot;460&quot;,
    &quot;id&quot; : 176
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TURCA&quot;,
    &quot;codice&quot; : &quot;351&quot;,
    &quot;id&quot; : 177
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;TURKMENA&quot;,
    &quot;codice&quot; : &quot;364&quot;,
    &quot;id&quot; : 178
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UCRAINA&quot;,
    &quot;codice&quot; : &quot;243&quot;,
    &quot;id&quot; : 179
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UGANDESE&quot;,
    &quot;codice&quot; : &quot;461&quot;,
    &quot;id&quot; : 180
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UNGHERESE&quot;,
    &quot;codice&quot; : &quot;244&quot;,
    &quot;id&quot; : 181
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;URUGUAIANA&quot;,
    &quot;codice&quot; : &quot;618&quot;,
    &quot;id&quot; : 182
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;UZBEKA&quot;,
    &quot;codice&quot; : &quot;357&quot;,
    &quot;id&quot; : 183
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VANUATU&quot;,
    &quot;codice&quot; : &quot;732&quot;,
    &quot;id&quot; : 184
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VENZUELANA&quot;,
    &quot;codice&quot; : &quot;619&quot;,
    &quot;id&quot; : 185
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;VIETNAMITA&quot;,
    &quot;codice&quot; : &quot;353&quot;,
    &quot;id&quot; : 186
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;YEMENITA&quot;,
    &quot;codice&quot; : &quot;354&quot;,
    &quot;id&quot; : 187
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ZAMBESE&quot;,
    &quot;codice&quot; : &quot;464&quot;,
    &quot;id&quot; : 188
  }, {
    &quot;StatusCode&quot; : null,
    &quot;flagAttivo&quot; : true,
    &quot;descrizione&quot; : &quot;ZIMBABWE&quot;,
    &quot;codice&quot; : &quot;465&quot;,
    &quot;id&quot; : 189
  } ],
  &quot;AllStati&quot; : {
    &quot;Options&quot; : [ {
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmdUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmgUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmhUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmoUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmtUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmwUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCmzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCn9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CILE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnBUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnKUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCndUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCneUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCngUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnhUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCniUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnlUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnmUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnoUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCntUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnuUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GABON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCnzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo7UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo8UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCo9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoBUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCobUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCocUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCodUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCofUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;
    }, {
      &quot;Id&quot; : &quot;m0Z9X000006jCogUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCokUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jColUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jComUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jConUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCooUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCorUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCosUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCotUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCouUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCovUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCowUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCoyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCozUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp8UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCp9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpPUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpUUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpgUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCphUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpiUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCplUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpnUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCppUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCprUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpsUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCptUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpwUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCpzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq0UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq6UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCq9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqDUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqEUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqGUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqIUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqKUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqMUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqNUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqOUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqQUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqRUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqSUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqTUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqVUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqWUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqXUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqYUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqZUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqaUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqbUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqcUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqdUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqeUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqfUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqjUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqkUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqmUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqpUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqqUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqrUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqtUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCquUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqvUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqxUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqyUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCqzUAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr1UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr2UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr3UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr4UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr5UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr7UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCr9UAE&quot;,
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrAUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrCUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrFUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrHUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrJUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;
    }, {
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;,
      &quot;Id&quot; : &quot;m0Z9X000006jCrLUAU&quot;,
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;
    } ]
  },
  &quot;CodiceFiscale&quot; : &quot;N/D&quot;,
  &quot;PaeseDiNascitaUSA&quot; : false,
  &quot;CodiceStato&quot; : &quot;Z103&quot;,
  &quot;PaeseDiResidenza&quot; : &quot;N/D&quot;,
  &quot;SourceSystemIdentifier&quot; : &quot;7177868&quot;,
  &quot;Societa&quot; : &quot;unipolsai&quot;,
  &quot;PaeseDiNascita&quot; : &quot;N/D&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TransformFatcaInsert_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
