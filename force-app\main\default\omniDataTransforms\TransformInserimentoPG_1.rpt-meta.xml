<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Stati&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AFGHANISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;2&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z200&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ALBANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;87&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z100&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ALGERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;3&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z301&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AMERICAN SAMOA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;148&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z725&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANDORRA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;4&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z101&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANGOLA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;133&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z302&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANGUILLA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;209&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z529&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANTIGUA E BARBUDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;197&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z532&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARABIA SAUDITA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;5&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z203&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;6&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z600&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARMENIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;266&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z252&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARUBA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;212&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z501&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AUSTRALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;7&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z700&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;8&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z102&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AZERBAIJAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;268&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z253&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BAHAMAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;160&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z502&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BAHREIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;169&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z204&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BANGLADESH&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;130&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z249&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BARBADOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;118&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z522&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;BELGIO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;9&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z103&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BELIZE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;198&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z512&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BENIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;158&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z314&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BERMUDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;207&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z400&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BHUTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;97&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z205&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BIELORUSSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;264&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z139&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOLIVIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;10&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z601&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;274&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z153&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOTSWANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;98&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z358&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BRASILE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;11&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z602&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;BULGARIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;12&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z104&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BURKINA FASO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;142&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z354&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BURUNDI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;25&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z305&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CALEDONIA NUOVA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;253&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z716&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAMBOGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;135&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z208&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAMERUN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;119&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z306&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CANADA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;13&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z401&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAPO VERDE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;188&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z307&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CIAD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;144&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z309&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CILE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;15&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z603&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;16&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z210&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;CIPRO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;101&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z211&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;93&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z106&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COLOMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;17&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z604&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COMORE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;176&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z310&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CONGO REPUBBLICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;145&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z311&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;18&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z312&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COREA DEL NORD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;74&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z214&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COREA DEL SUD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;84&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z213&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COSTA D&apos;AVORIO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;146&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z313&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COSTA RICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;19&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z503&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;CROAZIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;261&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z149&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CUBA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;20&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z504&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;DANIMARCA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;21&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z107&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z900&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;140&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z901&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE CANADESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z800&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE FRANCESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;165&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z902&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z903&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z904&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z801&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE RUSSE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z802&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;172&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z905&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z906&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DJIBOUTI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;113&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z361&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DOMINICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;192&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z526&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ECUADOR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;24&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z605&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EGITTO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;23&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z336&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EL SALVADOR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;64&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z506&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;238&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;796&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;242&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;241&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;239&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;240&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;244&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;243&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ERITREA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;277&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z368&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ESTONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;257&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z144&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ESWATINI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;138&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z349&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ETIOPIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;26&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z315&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FAROE ISLANDS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;204&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z108&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FIJI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;161&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z704&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FILIPPINE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;27&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z216&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;FINLANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;28&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z109&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;FRANCIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;29&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z110&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GABON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;157&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z316&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GAMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;164&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z317&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GEORGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;267&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z254&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;GERMANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;94&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z112&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GERUSALEMME&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z260&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GHANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;112&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z318&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIAMAICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;82&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z507&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIAPPONE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;88&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z219&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIBILTERRA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;102&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z113&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIORDANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;122&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z220&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;GRECIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;32&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z115&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GRENADA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;156&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z524&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GROENLANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;200&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z402&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUADALUPE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;214&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z508&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUAM&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;154&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z706&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUATEMALA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;33&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z509&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;137&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z319&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA EQUATORIALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;167&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z321&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA-BISSAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;185&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z320&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUYANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;159&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z606&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUYANA FRANCESE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z607&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;HAITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;34&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z510&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;HONDURAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;35&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z511&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;INDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;114&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z222&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;INDONESIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;129&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z223&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;39&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z224&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRAQ&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;38&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z225&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRIAN OCCIDENTALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z707&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;IRLANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;40&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z116&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISLANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;41&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z117&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLA CHRISTMAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;282&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z702&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLA NORFOLK&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;285&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z715&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE CAYMAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;211&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z530&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE COCOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;281&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z212&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE COOK&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;237&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z703&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE FALKLAND&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;190&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z609&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE MARSHALL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;217&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z711&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE SOLOMON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;191&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z724&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;210&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z519&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;221&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z520&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;249&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z525&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;218&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z729&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISRAELE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;182&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z226&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ITALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;86&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z000&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;JERSEY (BALIATO DI)&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;202&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z124&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KAZAKHSTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;269&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z255&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KENYA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;116&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z322&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KIRIBATI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;194&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z731&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KOSOVO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;291&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z160&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KUWAIT&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;126&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z227&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KYRGYZSTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;270&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z256&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;136&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z228&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LESOTHO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;89&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z359&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LETTONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;258&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z145&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBANO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;95&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z229&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;44&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z325&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;45&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z326&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIECHTENSTEIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;90&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z119&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LITUANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;259&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z146&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LUSSEMBURGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;92&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z120&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MACAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;59&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z231&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MACEDONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;278&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z148&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MADAGASCAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;104&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z327&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALAWI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;56&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z328&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALAYSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;106&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z247&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALDIVE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;127&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z232&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;149&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z329&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;MALTA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;105&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z121&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;203&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z122&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MARIANNE DEL NORD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;219&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z710&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAROCCO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;107&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z330&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MARTINICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;213&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z513&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAURITANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;141&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z331&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAURITIUS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;128&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z332&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAYOTTE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;226&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z360&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MESSICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;46&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z514&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MICRONESIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;215&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z735&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MOLDAVIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;265&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z140&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONACO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;91&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z123&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONGOLIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;110&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z233&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONTENEGRO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;290&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z159&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONTSERRAT&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;208&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z531&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MOZAMBICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;134&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z333&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MYANMAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;83&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z206&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NAMIBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;206&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z300&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NAURU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;109&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z713&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NEPAL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;115&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z234&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NICARAGUA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;47&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z515&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIGER&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;150&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z334&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIGERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;117&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z335&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIUE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;205&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z714&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NORVEGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;48&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z125&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NUOVA ZELANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;49&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z719&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;OMAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;163&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z235&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;PAESI BASSI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;50&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z126&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PAKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;36&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z236&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PALAU REPUBBLICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;216&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z734&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PANAMA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;51&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z516&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;186&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z730&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PARAGUAY&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;52&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z610&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PERU&apos;&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;53&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z611&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PITCAIRN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;175&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z722&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;POLINESIA FRANCESE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;225&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z723&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;POLONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;54&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z127&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;PORTOGALLO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;55&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z128&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PUERTO RICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;220&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z518&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;QATAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;168&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z237&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REGNO UNITO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;31&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z114&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA CECA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;275&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z156&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;143&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z308&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;297&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z907&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;63&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z505&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REUNION&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;247&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z324&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ROMANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;61&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z129&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;RUANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;151&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z338&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;RUSSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;262&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z154&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAHARA OCCIDENTALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;166&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z339&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;195&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z533&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT LUCIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;199&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z527&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;196&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z528&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAMOA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;131&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z726&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAN MARINO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;37&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z130&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SANT&apos;ELENA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;254&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z340&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;187&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z341&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SENEGAL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;152&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z343&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SERBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;289&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z158&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SEYCHELLES&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;189&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z342&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SIERRA LEONE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;153&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z344&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SINGAPORE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;147&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z248&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SIRIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;65&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z240&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVACCHIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;276&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z155&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVENIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;260&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z150&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SOMALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;66&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z345&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SPAGNA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;67&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z131&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SRI LANKA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;85&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z209&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;248&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z403&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;69&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z404&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SUD AFRICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;78&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z347&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SUDAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;70&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z348&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;125&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z207&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SURINAME&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;124&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z608&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SVEZIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;68&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z132&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SVIZZERA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;71&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z133&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TAGIKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;272&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z257&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TAIWAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;22&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z217&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TANZANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;57&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z357&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TERRITORI PALESTINESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;279&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z161&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;THAILANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;72&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z241&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TIMOR-LESTE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;287&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z242&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TOGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;155&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z351&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TOKELAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;236&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z727&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TONGA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;162&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z728&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TRINIDAD E TOBAGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;120&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z612&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TUNISIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;75&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z352&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TURCHIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;76&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z243&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TURKMENISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;273&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z258&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TUVALU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;193&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z732&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UCRAINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;263&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z138&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UGANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;132&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z353&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;UNGHERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;77&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z134&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;URUGUAY&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;80&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z613&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UZBEKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;271&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z259&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VANUATU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;121&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z733&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VENEZUELA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;81&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z614&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VIETNAM&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;62&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z251&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;YEMEN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;42&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z246&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ZAMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;58&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z355&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ZIMBABWE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;73&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z337&quot;
  } ],
  &quot;Province&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;92100&quot;,
    &quot;descrizione&quot; : &quot;AGRIGENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;019084&quot;,
    &quot;sigla&quot; : &quot;AG&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;15100&quot;,
    &quot;descrizione&quot; : &quot;ALESSANDRIA&quot;,
    &quot;codiceISTAT&quot; : &quot;001006&quot;,
    &quot;sigla&quot; : &quot;AL&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;60100&quot;,
    &quot;descrizione&quot; : &quot;ANCONA&quot;,
    &quot;codiceISTAT&quot; : &quot;011042&quot;,
    &quot;sigla&quot; : &quot;AN&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;11100&quot;,
    &quot;descrizione&quot; : &quot;AOSTA&quot;,
    &quot;codiceISTAT&quot; : &quot;002007&quot;,
    &quot;sigla&quot; : &quot;AO&quot;,
    &quot;descrizioneRegione&quot; : &quot;VALLE D&apos;AOSTA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;02&quot;,
    &quot;codiceISTATRegione&quot; : &quot;002&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;63100&quot;,
    &quot;descrizione&quot; : &quot;ASCOLI PICENO&quot;,
    &quot;codiceISTAT&quot; : &quot;011044&quot;,
    &quot;sigla&quot; : &quot;AP&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;67100&quot;,
    &quot;descrizione&quot; : &quot;L&apos;AQUILA&quot;,
    &quot;codiceISTAT&quot; : &quot;013066&quot;,
    &quot;sigla&quot; : &quot;AQ&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;52100&quot;,
    &quot;descrizione&quot; : &quot;AREZZO&quot;,
    &quot;codiceISTAT&quot; : &quot;009051&quot;,
    &quot;sigla&quot; : &quot;AR&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;14100&quot;,
    &quot;descrizione&quot; : &quot;ASTI&quot;,
    &quot;codiceISTAT&quot; : &quot;001005&quot;,
    &quot;sigla&quot; : &quot;AT&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;83100&quot;,
    &quot;descrizione&quot; : &quot;AVELLINO&quot;,
    &quot;codiceISTAT&quot; : &quot;015064&quot;,
    &quot;sigla&quot; : &quot;AV&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;70100&quot;,
    &quot;descrizione&quot; : &quot;BARI&quot;,
    &quot;codiceISTAT&quot; : &quot;016072&quot;,
    &quot;sigla&quot; : &quot;BA&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;24100&quot;,
    &quot;descrizione&quot; : &quot;BERGAMO&quot;,
    &quot;codiceISTAT&quot; : &quot;003016&quot;,
    &quot;sigla&quot; : &quot;BG&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;13900&quot;,
    &quot;descrizione&quot; : &quot;BIELLA&quot;,
    &quot;codiceISTAT&quot; : &quot;001096&quot;,
    &quot;sigla&quot; : &quot;BI&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;32100&quot;,
    &quot;descrizione&quot; : &quot;BELLUNO&quot;,
    &quot;codiceISTAT&quot; : &quot;005025&quot;,
    &quot;sigla&quot; : &quot;BL&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;82100&quot;,
    &quot;descrizione&quot; : &quot;BENEVENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;015062&quot;,
    &quot;sigla&quot; : &quot;BN&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;40100&quot;,
    &quot;descrizione&quot; : &quot;BOLOGNA&quot;,
    &quot;codiceISTAT&quot; : &quot;008037&quot;,
    &quot;sigla&quot; : &quot;BO&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;72100&quot;,
    &quot;descrizione&quot; : &quot;BRINDISI&quot;,
    &quot;codiceISTAT&quot; : &quot;016074&quot;,
    &quot;sigla&quot; : &quot;BR&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;25100&quot;,
    &quot;descrizione&quot; : &quot;BRESCIA&quot;,
    &quot;codiceISTAT&quot; : &quot;003017&quot;,
    &quot;sigla&quot; : &quot;BS&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;76121&quot;,
    &quot;descrizione&quot; : &quot;BARLETTA-ANDRIA-TRANI&quot;,
    &quot;codiceISTAT&quot; : &quot;016110&quot;,
    &quot;sigla&quot; : &quot;BT&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;39100&quot;,
    &quot;descrizione&quot; : &quot;BOLZANO&quot;,
    &quot;codiceISTAT&quot; : &quot;004021&quot;,
    &quot;sigla&quot; : &quot;BZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;codiceISTATRegione&quot; : &quot;004&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09100&quot;,
    &quot;descrizione&quot; : &quot;CAGLIARI&quot;,
    &quot;codiceISTAT&quot; : &quot;020092&quot;,
    &quot;sigla&quot; : &quot;CA&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;86100&quot;,
    &quot;descrizione&quot; : &quot;CAMPOBASSO&quot;,
    &quot;codiceISTAT&quot; : &quot;014070&quot;,
    &quot;sigla&quot; : &quot;CB&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;codiceISTATRegione&quot; : &quot;014&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;81100&quot;,
    &quot;descrizione&quot; : &quot;CASERTA&quot;,
    &quot;codiceISTAT&quot; : &quot;015061&quot;,
    &quot;sigla&quot; : &quot;CE&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;66100&quot;,
    &quot;descrizione&quot; : &quot;CHIETI&quot;,
    &quot;codiceISTAT&quot; : &quot;013069&quot;,
    &quot;sigla&quot; : &quot;CH&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;93100&quot;,
    &quot;descrizione&quot; : &quot;CALTANISSETTA&quot;,
    &quot;codiceISTAT&quot; : &quot;019085&quot;,
    &quot;sigla&quot; : &quot;CL&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;12100&quot;,
    &quot;descrizione&quot; : &quot;CUNEO&quot;,
    &quot;codiceISTAT&quot; : &quot;001004&quot;,
    &quot;sigla&quot; : &quot;CN&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;22100&quot;,
    &quot;descrizione&quot; : &quot;COMO&quot;,
    &quot;codiceISTAT&quot; : &quot;003013&quot;,
    &quot;sigla&quot; : &quot;CO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;26100&quot;,
    &quot;descrizione&quot; : &quot;CREMONA&quot;,
    &quot;codiceISTAT&quot; : &quot;003019&quot;,
    &quot;sigla&quot; : &quot;CR&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;87100&quot;,
    &quot;descrizione&quot; : &quot;COSENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;018078&quot;,
    &quot;sigla&quot; : &quot;CS&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;95100&quot;,
    &quot;descrizione&quot; : &quot;CATANIA&quot;,
    &quot;codiceISTAT&quot; : &quot;019087&quot;,
    &quot;sigla&quot; : &quot;CT&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;88100&quot;,
    &quot;descrizione&quot; : &quot;CATANZARO&quot;,
    &quot;codiceISTAT&quot; : &quot;018079&quot;,
    &quot;sigla&quot; : &quot;CZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;&quot;,
    &quot;descrizione&quot; : &quot;ESTERO&quot;,
    &quot;codiceISTAT&quot; : &quot;000000&quot;,
    &quot;sigla&quot; : &quot;EE&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;00&quot;,
    &quot;codiceISTATRegione&quot; : &quot;000&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;94100&quot;,
    &quot;descrizione&quot; : &quot;ENNA&quot;,
    &quot;codiceISTAT&quot; : &quot;019086&quot;,
    &quot;sigla&quot; : &quot;EN&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;47100&quot;,
    &quot;descrizione&quot; : &quot;FORLI&apos; CESENA&quot;,
    &quot;codiceISTAT&quot; : &quot;008040&quot;,
    &quot;sigla&quot; : &quot;FC&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;44100&quot;,
    &quot;descrizione&quot; : &quot;FERRARA&quot;,
    &quot;codiceISTAT&quot; : &quot;008038&quot;,
    &quot;sigla&quot; : &quot;FE&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;71100&quot;,
    &quot;descrizione&quot; : &quot;FOGGIA&quot;,
    &quot;codiceISTAT&quot; : &quot;016071&quot;,
    &quot;sigla&quot; : &quot;FG&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;50100&quot;,
    &quot;descrizione&quot; : &quot;FIRENZE&quot;,
    &quot;codiceISTAT&quot; : &quot;009048&quot;,
    &quot;sigla&quot; : &quot;FI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;63900&quot;,
    &quot;descrizione&quot; : &quot;FERMO&quot;,
    &quot;codiceISTAT&quot; : &quot;011109&quot;,
    &quot;sigla&quot; : &quot;FM&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;03100&quot;,
    &quot;descrizione&quot; : &quot;FROSINONE&quot;,
    &quot;codiceISTAT&quot; : &quot;012060&quot;,
    &quot;sigla&quot; : &quot;FR&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;16100&quot;,
    &quot;descrizione&quot; : &quot;GENOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;007010&quot;,
    &quot;sigla&quot; : &quot;GE&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;34170&quot;,
    &quot;descrizione&quot; : &quot;GORIZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;006031&quot;,
    &quot;sigla&quot; : &quot;GO&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;58100&quot;,
    &quot;descrizione&quot; : &quot;GROSSETO&quot;,
    &quot;codiceISTAT&quot; : &quot;009053&quot;,
    &quot;sigla&quot; : &quot;GR&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;18100&quot;,
    &quot;descrizione&quot; : &quot;IMPERIA&quot;,
    &quot;codiceISTAT&quot; : &quot;007008&quot;,
    &quot;sigla&quot; : &quot;IM&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;86170&quot;,
    &quot;descrizione&quot; : &quot;ISERNIA&quot;,
    &quot;codiceISTAT&quot; : &quot;014094&quot;,
    &quot;sigla&quot; : &quot;IS&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;codiceISTATRegione&quot; : &quot;014&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;88900&quot;,
    &quot;descrizione&quot; : &quot;CROTONE&quot;,
    &quot;codiceISTAT&quot; : &quot;018101&quot;,
    &quot;sigla&quot; : &quot;KR&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;23900&quot;,
    &quot;descrizione&quot; : &quot;LECCO&quot;,
    &quot;codiceISTAT&quot; : &quot;003097&quot;,
    &quot;sigla&quot; : &quot;LC&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;73100&quot;,
    &quot;descrizione&quot; : &quot;LECCE&quot;,
    &quot;codiceISTAT&quot; : &quot;016075&quot;,
    &quot;sigla&quot; : &quot;LE&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;57100&quot;,
    &quot;descrizione&quot; : &quot;LIVORNO&quot;,
    &quot;codiceISTAT&quot; : &quot;009049&quot;,
    &quot;sigla&quot; : &quot;LI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;26900&quot;,
    &quot;descrizione&quot; : &quot;LODI&quot;,
    &quot;codiceISTAT&quot; : &quot;003098&quot;,
    &quot;sigla&quot; : &quot;LO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;04100&quot;,
    &quot;descrizione&quot; : &quot;LATINA&quot;,
    &quot;codiceISTAT&quot; : &quot;012059&quot;,
    &quot;sigla&quot; : &quot;LT&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;55100&quot;,
    &quot;descrizione&quot; : &quot;LUCCA&quot;,
    &quot;codiceISTAT&quot; : &quot;009046&quot;,
    &quot;sigla&quot; : &quot;LU&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;20900&quot;,
    &quot;descrizione&quot; : &quot;MONZA E DELLA BRIANZA&quot;,
    &quot;codiceISTAT&quot; : &quot;003108&quot;,
    &quot;sigla&quot; : &quot;MB&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;62100&quot;,
    &quot;descrizione&quot; : &quot;MACERATA&quot;,
    &quot;codiceISTAT&quot; : &quot;011043&quot;,
    &quot;sigla&quot; : &quot;MC&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;98100&quot;,
    &quot;descrizione&quot; : &quot;MESSINA&quot;,
    &quot;codiceISTAT&quot; : &quot;019083&quot;,
    &quot;sigla&quot; : &quot;ME&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;46100&quot;,
    &quot;descrizione&quot; : &quot;MANTOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;003020&quot;,
    &quot;sigla&quot; : &quot;MN&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;41100&quot;,
    &quot;descrizione&quot; : &quot;MODENA&quot;,
    &quot;codiceISTAT&quot; : &quot;008036&quot;,
    &quot;sigla&quot; : &quot;MO&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;54100&quot;,
    &quot;descrizione&quot; : &quot;MASSA&quot;,
    &quot;codiceISTAT&quot; : &quot;009045&quot;,
    &quot;sigla&quot; : &quot;MS&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;75100&quot;,
    &quot;descrizione&quot; : &quot;MATERA&quot;,
    &quot;codiceISTAT&quot; : &quot;017077&quot;,
    &quot;sigla&quot; : &quot;MT&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;codiceISTATRegione&quot; : &quot;017&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;80100&quot;,
    &quot;descrizione&quot; : &quot;NAPOLI&quot;,
    &quot;codiceISTAT&quot; : &quot;015063&quot;,
    &quot;sigla&quot; : &quot;NA&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;28100&quot;,
    &quot;descrizione&quot; : &quot;NOVARA&quot;,
    &quot;codiceISTAT&quot; : &quot;001003&quot;,
    &quot;sigla&quot; : &quot;NO&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;08100&quot;,
    &quot;descrizione&quot; : &quot;NUORO&quot;,
    &quot;codiceISTAT&quot; : &quot;020091&quot;,
    &quot;sigla&quot; : &quot;NU&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09170&quot;,
    &quot;descrizione&quot; : &quot;ORISTANO&quot;,
    &quot;codiceISTAT&quot; : &quot;020095&quot;,
    &quot;sigla&quot; : &quot;OR&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;90100&quot;,
    &quot;descrizione&quot; : &quot;PALERMO&quot;,
    &quot;codiceISTAT&quot; : &quot;019082&quot;,
    &quot;sigla&quot; : &quot;PA&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;29100&quot;,
    &quot;descrizione&quot; : &quot;PIACENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;008033&quot;,
    &quot;sigla&quot; : &quot;PC&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;35100&quot;,
    &quot;descrizione&quot; : &quot;PADOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;005028&quot;,
    &quot;sigla&quot; : &quot;PD&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;65100&quot;,
    &quot;descrizione&quot; : &quot;PESCARA&quot;,
    &quot;codiceISTAT&quot; : &quot;013068&quot;,
    &quot;sigla&quot; : &quot;PE&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;06100&quot;,
    &quot;descrizione&quot; : &quot;PERUGIA&quot;,
    &quot;codiceISTAT&quot; : &quot;010054&quot;,
    &quot;sigla&quot; : &quot;PG&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;codiceISTATRegione&quot; : &quot;010&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;56100&quot;,
    &quot;descrizione&quot; : &quot;PISA&quot;,
    &quot;codiceISTAT&quot; : &quot;009050&quot;,
    &quot;sigla&quot; : &quot;PI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;33170&quot;,
    &quot;descrizione&quot; : &quot;PORDENONE&quot;,
    &quot;codiceISTAT&quot; : &quot;006093&quot;,
    &quot;sigla&quot; : &quot;PN&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;59100&quot;,
    &quot;descrizione&quot; : &quot;PRATO&quot;,
    &quot;codiceISTAT&quot; : &quot;009100&quot;,
    &quot;sigla&quot; : &quot;PO&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;43100&quot;,
    &quot;descrizione&quot; : &quot;PARMA&quot;,
    &quot;codiceISTAT&quot; : &quot;008034&quot;,
    &quot;sigla&quot; : &quot;PR&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;51100&quot;,
    &quot;descrizione&quot; : &quot;PISTOIA&quot;,
    &quot;codiceISTAT&quot; : &quot;009047&quot;,
    &quot;sigla&quot; : &quot;PT&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;61100&quot;,
    &quot;descrizione&quot; : &quot;PESARO URBINO&quot;,
    &quot;codiceISTAT&quot; : &quot;011041&quot;,
    &quot;sigla&quot; : &quot;PU&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;27100&quot;,
    &quot;descrizione&quot; : &quot;PAVIA&quot;,
    &quot;codiceISTAT&quot; : &quot;003018&quot;,
    &quot;sigla&quot; : &quot;PV&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;85100&quot;,
    &quot;descrizione&quot; : &quot;POTENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;017076&quot;,
    &quot;sigla&quot; : &quot;PZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;codiceISTATRegione&quot; : &quot;017&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;48100&quot;,
    &quot;descrizione&quot; : &quot;RAVENNA&quot;,
    &quot;codiceISTAT&quot; : &quot;008039&quot;,
    &quot;sigla&quot; : &quot;RA&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;89100&quot;,
    &quot;descrizione&quot; : &quot;REGGIO DI CALABRIA&quot;,
    &quot;codiceISTAT&quot; : &quot;018080&quot;,
    &quot;sigla&quot; : &quot;RC&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;42100&quot;,
    &quot;descrizione&quot; : &quot;REGGIO NELL&apos;EMILIA&quot;,
    &quot;codiceISTAT&quot; : &quot;008035&quot;,
    &quot;sigla&quot; : &quot;RE&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;97100&quot;,
    &quot;descrizione&quot; : &quot;RAGUSA&quot;,
    &quot;codiceISTAT&quot; : &quot;019088&quot;,
    &quot;sigla&quot; : &quot;RG&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;02100&quot;,
    &quot;descrizione&quot; : &quot;RIETI&quot;,
    &quot;codiceISTAT&quot; : &quot;012057&quot;,
    &quot;sigla&quot; : &quot;RI&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;00100&quot;,
    &quot;descrizione&quot; : &quot;ROMA&quot;,
    &quot;codiceISTAT&quot; : &quot;012058&quot;,
    &quot;sigla&quot; : &quot;RM&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;47900&quot;,
    &quot;descrizione&quot; : &quot;RIMINI&quot;,
    &quot;codiceISTAT&quot; : &quot;008099&quot;,
    &quot;sigla&quot; : &quot;RN&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;45100&quot;,
    &quot;descrizione&quot; : &quot;ROVIGO&quot;,
    &quot;codiceISTAT&quot; : &quot;005029&quot;,
    &quot;sigla&quot; : &quot;RO&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;84100&quot;,
    &quot;descrizione&quot; : &quot;SALERNO&quot;,
    &quot;codiceISTAT&quot; : &quot;015065&quot;,
    &quot;sigla&quot; : &quot;SA&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;53100&quot;,
    &quot;descrizione&quot; : &quot;SIENA&quot;,
    &quot;codiceISTAT&quot; : &quot;009052&quot;,
    &quot;sigla&quot; : &quot;SI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;23100&quot;,
    &quot;descrizione&quot; : &quot;SONDRIO&quot;,
    &quot;codiceISTAT&quot; : &quot;003014&quot;,
    &quot;sigla&quot; : &quot;SO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;19100&quot;,
    &quot;descrizione&quot; : &quot;LA SPEZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;007011&quot;,
    &quot;sigla&quot; : &quot;SP&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;96100&quot;,
    &quot;descrizione&quot; : &quot;SIRACUSA&quot;,
    &quot;codiceISTAT&quot; : &quot;019089&quot;,
    &quot;sigla&quot; : &quot;SR&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;07100&quot;,
    &quot;descrizione&quot; : &quot;SASSARI&quot;,
    &quot;codiceISTAT&quot; : &quot;020090&quot;,
    &quot;sigla&quot; : &quot;SS&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09013&quot;,
    &quot;descrizione&quot; : &quot;SUD SARDEGNA&quot;,
    &quot;codiceISTAT&quot; : &quot;020111&quot;,
    &quot;sigla&quot; : &quot;SU&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;17100&quot;,
    &quot;descrizione&quot; : &quot;SAVONA&quot;,
    &quot;codiceISTAT&quot; : &quot;007009&quot;,
    &quot;sigla&quot; : &quot;SV&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;74100&quot;,
    &quot;descrizione&quot; : &quot;TARANTO&quot;,
    &quot;codiceISTAT&quot; : &quot;016073&quot;,
    &quot;sigla&quot; : &quot;TA&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;64100&quot;,
    &quot;descrizione&quot; : &quot;TERAMO&quot;,
    &quot;codiceISTAT&quot; : &quot;013067&quot;,
    &quot;sigla&quot; : &quot;TE&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;38100&quot;,
    &quot;descrizione&quot; : &quot;TRENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;004022&quot;,
    &quot;sigla&quot; : &quot;TN&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;codiceISTATRegione&quot; : &quot;004&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;10100&quot;,
    &quot;descrizione&quot; : &quot;TORINO&quot;,
    &quot;codiceISTAT&quot; : &quot;001001&quot;,
    &quot;sigla&quot; : &quot;TO&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;91100&quot;,
    &quot;descrizione&quot; : &quot;TRAPANI&quot;,
    &quot;codiceISTAT&quot; : &quot;019081&quot;,
    &quot;sigla&quot; : &quot;TP&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;05100&quot;,
    &quot;descrizione&quot; : &quot;TERNI&quot;,
    &quot;codiceISTAT&quot; : &quot;010055&quot;,
    &quot;sigla&quot; : &quot;TR&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;codiceISTATRegione&quot; : &quot;010&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;34100&quot;,
    &quot;descrizione&quot; : &quot;TRIESTE&quot;,
    &quot;codiceISTAT&quot; : &quot;006032&quot;,
    &quot;sigla&quot; : &quot;TS&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;31100&quot;,
    &quot;descrizione&quot; : &quot;TREVISO&quot;,
    &quot;codiceISTAT&quot; : &quot;005026&quot;,
    &quot;sigla&quot; : &quot;TV&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;33100&quot;,
    &quot;descrizione&quot; : &quot;UDINE&quot;,
    &quot;codiceISTAT&quot; : &quot;006030&quot;,
    &quot;sigla&quot; : &quot;UD&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;21100&quot;,
    &quot;descrizione&quot; : &quot;VARESE&quot;,
    &quot;codiceISTAT&quot; : &quot;003012&quot;,
    &quot;sigla&quot; : &quot;VA&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;28900&quot;,
    &quot;descrizione&quot; : &quot;VERBANIA&quot;,
    &quot;codiceISTAT&quot; : &quot;001103&quot;,
    &quot;sigla&quot; : &quot;VB&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;13100&quot;,
    &quot;descrizione&quot; : &quot;VERCELLI&quot;,
    &quot;codiceISTAT&quot; : &quot;001002&quot;,
    &quot;sigla&quot; : &quot;VC&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;30100&quot;,
    &quot;descrizione&quot; : &quot;VENEZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;005027&quot;,
    &quot;sigla&quot; : &quot;VE&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;36100&quot;,
    &quot;descrizione&quot; : &quot;VICENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;005024&quot;,
    &quot;sigla&quot; : &quot;VI&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;37100&quot;,
    &quot;descrizione&quot; : &quot;VERONA&quot;,
    &quot;codiceISTAT&quot; : &quot;005023&quot;,
    &quot;sigla&quot; : &quot;VR&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;01100&quot;,
    &quot;descrizione&quot; : &quot;VITERBO&quot;,
    &quot;codiceISTAT&quot; : &quot;012056&quot;,
    &quot;sigla&quot; : &quot;VT&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;89900&quot;,
    &quot;descrizione&quot; : &quot;VIBO VALENTIA&quot;,
    &quot;codiceISTAT&quot; : &quot;018102&quot;,
    &quot;sigla&quot; : &quot;VV&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  } ],
  &quot;Data&quot; : {
    &quot;PaeseDiNascitaUSA&quot; : false,
    &quot;CodiceFiscale&quot; : &quot;****************&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;IT&quot;,
    &quot;SourceSystemIdentifier&quot; : &quot;10834735&quot;,
    &quot;Societa&quot; : &quot;unipolsai&quot;,
    &quot;PaeseDiNascita&quot; : &quot;086&quot;
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TransformInserimentoPG</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem13</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleUsa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem12</globalKey>
        <inputFieldName>Data:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionProvince</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:Data:CodiceStato &quot;Z000&quot; == var:Data:CodiceStato &quot;Z404&quot; == || false true IF</formulaConverted>
        <formulaExpression>IF((Data:CodiceStato == &quot;Z000&quot; || Data:CodiceStato == &quot;Z404&quot;) false, true)</formulaExpression>
        <formulaResultPath>ResEstera</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Options</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>0</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>TipologiaSocietaFatca</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>true</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>FlagSedeLegale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem2</globalKey>
        <inputFieldName>Province:sigla</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionProvince:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem9</globalKey>
        <inputFieldName>Province:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionProvince:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem8</globalKey>
        <inputFieldName>Data:SourceSystemIdentifier</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem7</globalKey>
        <inputFieldName>Stati:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Options:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem6</globalKey>
        <inputFieldName>Stati:codiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Options:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem10</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>FlagRequiredInd</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformInserimentoPGCustom0jI9V000000zBeTUAUItem11</globalKey>
        <inputFieldName>ResEstera</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformInserimentoPG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleStatoEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Stati&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AFGHANISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;2&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z200&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ALBANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;87&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z100&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ALGERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;3&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z301&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AMERICAN SAMOA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;148&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z725&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANDORRA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;4&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z101&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANGOLA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;133&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z302&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANGUILLA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;209&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z529&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ANTIGUA E BARBUDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;197&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z532&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARABIA SAUDITA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;5&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z203&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARGENTINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;6&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z600&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARMENIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;266&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z252&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ARUBA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;212&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z501&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AUSTRALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;7&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z700&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;AUSTRIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;8&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z102&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;AZERBAIJAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;268&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z253&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BAHAMAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;160&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z502&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BAHREIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;169&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z204&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BANGLADESH&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;130&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z249&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BARBADOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;118&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z522&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;BELGIO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;9&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z103&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BELIZE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;198&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z512&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BENIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;158&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z314&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BERMUDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;207&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z400&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BHUTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;97&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z205&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BIELORUSSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;264&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z139&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOLIVIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;10&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z601&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;274&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z153&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BOTSWANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;98&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z358&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BRASILE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;11&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z602&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;BULGARIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;12&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z104&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BURKINA FASO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;142&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z354&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;BURUNDI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;25&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z305&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CALEDONIA NUOVA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;253&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z716&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAMBOGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;135&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z208&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAMERUN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;119&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z306&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CANADA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;13&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z401&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CAPO VERDE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;188&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z307&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CIAD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;144&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z309&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CILE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;15&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z603&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;16&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z210&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;CIPRO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;101&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z211&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;93&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z106&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COLOMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;17&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z604&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COMORE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;176&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z310&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CONGO REPUBBLICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;145&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z311&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;18&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z312&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COREA DEL NORD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;74&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z214&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COREA DEL SUD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;84&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z213&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COSTA D&apos;AVORIO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;146&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z313&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;COSTA RICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;19&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z503&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;CROAZIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;261&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z149&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;CUBA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;20&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z504&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;DANIMARCA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;21&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z107&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z900&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;140&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z901&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE CANADESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z800&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE FRANCESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;165&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z902&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z903&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z904&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z801&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE RUSSE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z802&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;172&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z905&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z906&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DJIBOUTI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;113&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z361&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;DOMINICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;192&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z526&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ECUADOR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;24&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z605&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EGITTO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;23&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z336&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EL SALVADOR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;64&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z506&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;238&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;796&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;242&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;241&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;239&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;240&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;244&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;EMIRATI ARABI UNITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;243&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z215&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ERITREA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;277&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z368&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ESTONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;257&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z144&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ESWATINI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;138&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z349&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ETIOPIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;26&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z315&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FAROE ISLANDS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;204&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z108&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FIJI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;161&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z704&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;FILIPPINE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;27&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z216&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;FINLANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;28&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z109&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;FRANCIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;29&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z110&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GABON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;157&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z316&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GAMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;164&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z317&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GEORGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;267&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z254&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;GERMANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;94&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z112&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GERUSALEMME&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z260&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GHANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;112&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z318&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIAMAICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;82&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z507&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIAPPONE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;88&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z219&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIBILTERRA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;102&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z113&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GIORDANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;122&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z220&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;GRECIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;32&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z115&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GRENADA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;156&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z524&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GROENLANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;200&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z402&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUADALUPE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;214&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z508&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUAM&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;154&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z706&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUATEMALA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;33&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z509&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;137&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z319&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA EQUATORIALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;167&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z321&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUINEA-BISSAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;185&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z320&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUYANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;159&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z606&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;GUYANA FRANCESE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z607&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;HAITI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;34&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z510&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;HONDURAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;35&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z511&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;INDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;114&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z222&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;INDONESIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;129&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z223&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;39&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z224&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRAQ&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;38&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z225&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;IRIAN OCCIDENTALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;0&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z707&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;IRLANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;40&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z116&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISLANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;41&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z117&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLA CHRISTMAS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;282&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z702&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLA NORFOLK&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;285&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z715&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE CAYMAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;211&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z530&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE COCOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;281&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z212&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE COOK&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;237&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z703&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE FALKLAND&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;190&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z609&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE MARSHALL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;217&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z711&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE SOLOMON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;191&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z724&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;210&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z519&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;221&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z520&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;249&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z525&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;218&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z729&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ISRAELE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;182&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z226&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ITALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;86&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z000&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;JERSEY (BALIATO DI)&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;202&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z124&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KAZAKHSTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;269&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z255&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KENYA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;116&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z322&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KIRIBATI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;194&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z731&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KOSOVO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;291&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z160&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KUWAIT&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;126&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z227&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;KYRGYZSTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;270&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z256&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;136&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z228&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LESOTHO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;89&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z359&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LETTONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;258&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z145&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBANO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;95&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z229&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;44&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z325&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;45&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z326&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;LIECHTENSTEIN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;90&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z119&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LITUANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;259&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z146&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;LUSSEMBURGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;92&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z120&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MACAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;59&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z231&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MACEDONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;278&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z148&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MADAGASCAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;104&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z327&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALAWI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;56&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z328&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALAYSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;106&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z247&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALDIVE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;127&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z232&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MALI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;149&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z329&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;MALTA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;105&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z121&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;203&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z122&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MARIANNE DEL NORD&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;219&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z710&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAROCCO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;107&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z330&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MARTINICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;213&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z513&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAURITANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;141&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z331&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAURITIUS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;128&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z332&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MAYOTTE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;226&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z360&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MESSICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;46&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z514&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MICRONESIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;215&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z735&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MOLDAVIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;265&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z140&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONACO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;91&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z123&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONGOLIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;110&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z233&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONTENEGRO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;290&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z159&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MONTSERRAT&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;208&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z531&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MOZAMBICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;134&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z333&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;MYANMAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;83&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z206&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NAMIBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;206&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z300&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NAURU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;109&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z713&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NEPAL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;115&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z234&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NICARAGUA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;47&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z515&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIGER&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;150&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z334&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIGERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;117&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z335&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NIUE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;205&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z714&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NORVEGIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;48&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z125&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;NUOVA ZELANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;49&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z719&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;OMAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;163&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z235&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;PAESI BASSI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;50&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z126&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PAKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;36&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z236&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PALAU REPUBBLICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;216&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z734&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PANAMA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;51&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z516&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;186&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z730&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PARAGUAY&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;52&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z610&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PERU&apos;&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;53&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z611&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PITCAIRN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;175&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z722&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;POLINESIA FRANCESE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;225&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z723&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;POLONIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;54&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z127&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;PORTOGALLO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;55&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z128&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;PUERTO RICO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;220&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z518&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;QATAR&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;168&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z237&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REGNO UNITO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;31&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z114&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;REPUBBLICA CECA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;275&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z156&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;143&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z308&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;297&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z907&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;63&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z505&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;REUNION&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;247&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z324&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;ROMANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;61&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z129&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;RUANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;151&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z338&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;RUSSIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;262&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z154&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAHARA OCCIDENTALE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;166&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z339&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;195&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z533&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT LUCIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;199&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z527&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;196&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z528&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAMOA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;131&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z726&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAN MARINO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;37&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z130&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SANT&apos;ELENA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;254&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z340&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;187&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z341&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SENEGAL&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;152&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z343&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SERBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;289&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z158&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SEYCHELLES&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;189&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z342&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SIERRA LEONE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;153&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z344&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SINGAPORE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;147&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z248&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SIRIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;65&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z240&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVACCHIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;276&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z155&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SLOVENIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;260&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z150&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SOMALIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;66&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z345&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SPAGNA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;67&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z131&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SRI LANKA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;85&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z209&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;248&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z403&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;69&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z404&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SUD AFRICA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;78&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z347&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SUDAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;70&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z348&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;125&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z207&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SURINAME&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;124&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z608&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;SVEZIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;68&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z132&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;SVIZZERA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;71&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z133&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TAGIKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;272&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z257&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TAIWAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;22&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z217&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TANZANIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;57&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z357&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TERRITORI PALESTINESI&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;279&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z161&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;THAILANDIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;72&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z241&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TIMOR-LESTE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;287&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z242&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TOGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;155&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z351&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TOKELAU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;236&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z727&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TONGA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;162&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z728&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TRINIDAD E TOBAGO&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;120&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z612&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TUNISIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;75&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z352&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TURCHIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;76&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z243&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TURKMENISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;273&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z258&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;TUVALU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;193&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z732&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UCRAINA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;263&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z138&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UGANDA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;132&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z353&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : true,
    &quot;descrizione&quot; : &quot;UNGHERIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;77&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z134&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;URUGUAY&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;80&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z613&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;UZBEKISTAN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;271&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z259&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VANUATU&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;121&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z733&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VENEZUELA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;81&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z614&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;VIETNAM&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;62&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z251&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;YEMEN&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;42&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z246&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ZAMBIA&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;58&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z355&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;ue&quot; : false,
    &quot;descrizione&quot; : &quot;ZIMBABWE&quot;,
    &quot;attivo&quot; : true,
    &quot;codiceABI&quot; : &quot;73&quot;,
    &quot;codiceBelfiore&quot; : &quot;Z337&quot;
  } ],
  &quot;Province&quot; : [ {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;92100&quot;,
    &quot;descrizione&quot; : &quot;AGRIGENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;019084&quot;,
    &quot;sigla&quot; : &quot;AG&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;15100&quot;,
    &quot;descrizione&quot; : &quot;ALESSANDRIA&quot;,
    &quot;codiceISTAT&quot; : &quot;001006&quot;,
    &quot;sigla&quot; : &quot;AL&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;60100&quot;,
    &quot;descrizione&quot; : &quot;ANCONA&quot;,
    &quot;codiceISTAT&quot; : &quot;011042&quot;,
    &quot;sigla&quot; : &quot;AN&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;11100&quot;,
    &quot;descrizione&quot; : &quot;AOSTA&quot;,
    &quot;codiceISTAT&quot; : &quot;002007&quot;,
    &quot;sigla&quot; : &quot;AO&quot;,
    &quot;descrizioneRegione&quot; : &quot;VALLE D&apos;AOSTA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;02&quot;,
    &quot;codiceISTATRegione&quot; : &quot;002&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;63100&quot;,
    &quot;descrizione&quot; : &quot;ASCOLI PICENO&quot;,
    &quot;codiceISTAT&quot; : &quot;011044&quot;,
    &quot;sigla&quot; : &quot;AP&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;67100&quot;,
    &quot;descrizione&quot; : &quot;L&apos;AQUILA&quot;,
    &quot;codiceISTAT&quot; : &quot;013066&quot;,
    &quot;sigla&quot; : &quot;AQ&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;52100&quot;,
    &quot;descrizione&quot; : &quot;AREZZO&quot;,
    &quot;codiceISTAT&quot; : &quot;009051&quot;,
    &quot;sigla&quot; : &quot;AR&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;14100&quot;,
    &quot;descrizione&quot; : &quot;ASTI&quot;,
    &quot;codiceISTAT&quot; : &quot;001005&quot;,
    &quot;sigla&quot; : &quot;AT&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;83100&quot;,
    &quot;descrizione&quot; : &quot;AVELLINO&quot;,
    &quot;codiceISTAT&quot; : &quot;015064&quot;,
    &quot;sigla&quot; : &quot;AV&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;70100&quot;,
    &quot;descrizione&quot; : &quot;BARI&quot;,
    &quot;codiceISTAT&quot; : &quot;016072&quot;,
    &quot;sigla&quot; : &quot;BA&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;24100&quot;,
    &quot;descrizione&quot; : &quot;BERGAMO&quot;,
    &quot;codiceISTAT&quot; : &quot;003016&quot;,
    &quot;sigla&quot; : &quot;BG&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;13900&quot;,
    &quot;descrizione&quot; : &quot;BIELLA&quot;,
    &quot;codiceISTAT&quot; : &quot;001096&quot;,
    &quot;sigla&quot; : &quot;BI&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;32100&quot;,
    &quot;descrizione&quot; : &quot;BELLUNO&quot;,
    &quot;codiceISTAT&quot; : &quot;005025&quot;,
    &quot;sigla&quot; : &quot;BL&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;82100&quot;,
    &quot;descrizione&quot; : &quot;BENEVENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;015062&quot;,
    &quot;sigla&quot; : &quot;BN&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;40100&quot;,
    &quot;descrizione&quot; : &quot;BOLOGNA&quot;,
    &quot;codiceISTAT&quot; : &quot;008037&quot;,
    &quot;sigla&quot; : &quot;BO&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;72100&quot;,
    &quot;descrizione&quot; : &quot;BRINDISI&quot;,
    &quot;codiceISTAT&quot; : &quot;016074&quot;,
    &quot;sigla&quot; : &quot;BR&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;25100&quot;,
    &quot;descrizione&quot; : &quot;BRESCIA&quot;,
    &quot;codiceISTAT&quot; : &quot;003017&quot;,
    &quot;sigla&quot; : &quot;BS&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;76121&quot;,
    &quot;descrizione&quot; : &quot;BARLETTA-ANDRIA-TRANI&quot;,
    &quot;codiceISTAT&quot; : &quot;016110&quot;,
    &quot;sigla&quot; : &quot;BT&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;39100&quot;,
    &quot;descrizione&quot; : &quot;BOLZANO&quot;,
    &quot;codiceISTAT&quot; : &quot;004021&quot;,
    &quot;sigla&quot; : &quot;BZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;codiceISTATRegione&quot; : &quot;004&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09100&quot;,
    &quot;descrizione&quot; : &quot;CAGLIARI&quot;,
    &quot;codiceISTAT&quot; : &quot;020092&quot;,
    &quot;sigla&quot; : &quot;CA&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;86100&quot;,
    &quot;descrizione&quot; : &quot;CAMPOBASSO&quot;,
    &quot;codiceISTAT&quot; : &quot;014070&quot;,
    &quot;sigla&quot; : &quot;CB&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;codiceISTATRegione&quot; : &quot;014&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;81100&quot;,
    &quot;descrizione&quot; : &quot;CASERTA&quot;,
    &quot;codiceISTAT&quot; : &quot;015061&quot;,
    &quot;sigla&quot; : &quot;CE&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;66100&quot;,
    &quot;descrizione&quot; : &quot;CHIETI&quot;,
    &quot;codiceISTAT&quot; : &quot;013069&quot;,
    &quot;sigla&quot; : &quot;CH&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;93100&quot;,
    &quot;descrizione&quot; : &quot;CALTANISSETTA&quot;,
    &quot;codiceISTAT&quot; : &quot;019085&quot;,
    &quot;sigla&quot; : &quot;CL&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;12100&quot;,
    &quot;descrizione&quot; : &quot;CUNEO&quot;,
    &quot;codiceISTAT&quot; : &quot;001004&quot;,
    &quot;sigla&quot; : &quot;CN&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;22100&quot;,
    &quot;descrizione&quot; : &quot;COMO&quot;,
    &quot;codiceISTAT&quot; : &quot;003013&quot;,
    &quot;sigla&quot; : &quot;CO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;26100&quot;,
    &quot;descrizione&quot; : &quot;CREMONA&quot;,
    &quot;codiceISTAT&quot; : &quot;003019&quot;,
    &quot;sigla&quot; : &quot;CR&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;87100&quot;,
    &quot;descrizione&quot; : &quot;COSENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;018078&quot;,
    &quot;sigla&quot; : &quot;CS&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;95100&quot;,
    &quot;descrizione&quot; : &quot;CATANIA&quot;,
    &quot;codiceISTAT&quot; : &quot;019087&quot;,
    &quot;sigla&quot; : &quot;CT&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;88100&quot;,
    &quot;descrizione&quot; : &quot;CATANZARO&quot;,
    &quot;codiceISTAT&quot; : &quot;018079&quot;,
    &quot;sigla&quot; : &quot;CZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;&quot;,
    &quot;descrizione&quot; : &quot;ESTERO&quot;,
    &quot;codiceISTAT&quot; : &quot;000000&quot;,
    &quot;sigla&quot; : &quot;EE&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;00&quot;,
    &quot;codiceISTATRegione&quot; : &quot;000&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;94100&quot;,
    &quot;descrizione&quot; : &quot;ENNA&quot;,
    &quot;codiceISTAT&quot; : &quot;019086&quot;,
    &quot;sigla&quot; : &quot;EN&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;47100&quot;,
    &quot;descrizione&quot; : &quot;FORLI&apos; CESENA&quot;,
    &quot;codiceISTAT&quot; : &quot;008040&quot;,
    &quot;sigla&quot; : &quot;FC&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;44100&quot;,
    &quot;descrizione&quot; : &quot;FERRARA&quot;,
    &quot;codiceISTAT&quot; : &quot;008038&quot;,
    &quot;sigla&quot; : &quot;FE&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;71100&quot;,
    &quot;descrizione&quot; : &quot;FOGGIA&quot;,
    &quot;codiceISTAT&quot; : &quot;016071&quot;,
    &quot;sigla&quot; : &quot;FG&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;50100&quot;,
    &quot;descrizione&quot; : &quot;FIRENZE&quot;,
    &quot;codiceISTAT&quot; : &quot;009048&quot;,
    &quot;sigla&quot; : &quot;FI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;63900&quot;,
    &quot;descrizione&quot; : &quot;FERMO&quot;,
    &quot;codiceISTAT&quot; : &quot;011109&quot;,
    &quot;sigla&quot; : &quot;FM&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;03100&quot;,
    &quot;descrizione&quot; : &quot;FROSINONE&quot;,
    &quot;codiceISTAT&quot; : &quot;012060&quot;,
    &quot;sigla&quot; : &quot;FR&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;16100&quot;,
    &quot;descrizione&quot; : &quot;GENOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;007010&quot;,
    &quot;sigla&quot; : &quot;GE&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;34170&quot;,
    &quot;descrizione&quot; : &quot;GORIZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;006031&quot;,
    &quot;sigla&quot; : &quot;GO&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;58100&quot;,
    &quot;descrizione&quot; : &quot;GROSSETO&quot;,
    &quot;codiceISTAT&quot; : &quot;009053&quot;,
    &quot;sigla&quot; : &quot;GR&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;18100&quot;,
    &quot;descrizione&quot; : &quot;IMPERIA&quot;,
    &quot;codiceISTAT&quot; : &quot;007008&quot;,
    &quot;sigla&quot; : &quot;IM&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;86170&quot;,
    &quot;descrizione&quot; : &quot;ISERNIA&quot;,
    &quot;codiceISTAT&quot; : &quot;014094&quot;,
    &quot;sigla&quot; : &quot;IS&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;codiceISTATRegione&quot; : &quot;014&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;88900&quot;,
    &quot;descrizione&quot; : &quot;CROTONE&quot;,
    &quot;codiceISTAT&quot; : &quot;018101&quot;,
    &quot;sigla&quot; : &quot;KR&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;23900&quot;,
    &quot;descrizione&quot; : &quot;LECCO&quot;,
    &quot;codiceISTAT&quot; : &quot;003097&quot;,
    &quot;sigla&quot; : &quot;LC&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;73100&quot;,
    &quot;descrizione&quot; : &quot;LECCE&quot;,
    &quot;codiceISTAT&quot; : &quot;016075&quot;,
    &quot;sigla&quot; : &quot;LE&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;57100&quot;,
    &quot;descrizione&quot; : &quot;LIVORNO&quot;,
    &quot;codiceISTAT&quot; : &quot;009049&quot;,
    &quot;sigla&quot; : &quot;LI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;26900&quot;,
    &quot;descrizione&quot; : &quot;LODI&quot;,
    &quot;codiceISTAT&quot; : &quot;003098&quot;,
    &quot;sigla&quot; : &quot;LO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;04100&quot;,
    &quot;descrizione&quot; : &quot;LATINA&quot;,
    &quot;codiceISTAT&quot; : &quot;012059&quot;,
    &quot;sigla&quot; : &quot;LT&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;55100&quot;,
    &quot;descrizione&quot; : &quot;LUCCA&quot;,
    &quot;codiceISTAT&quot; : &quot;009046&quot;,
    &quot;sigla&quot; : &quot;LU&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;20900&quot;,
    &quot;descrizione&quot; : &quot;MONZA E DELLA BRIANZA&quot;,
    &quot;codiceISTAT&quot; : &quot;003108&quot;,
    &quot;sigla&quot; : &quot;MB&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;62100&quot;,
    &quot;descrizione&quot; : &quot;MACERATA&quot;,
    &quot;codiceISTAT&quot; : &quot;011043&quot;,
    &quot;sigla&quot; : &quot;MC&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;98100&quot;,
    &quot;descrizione&quot; : &quot;MESSINA&quot;,
    &quot;codiceISTAT&quot; : &quot;019083&quot;,
    &quot;sigla&quot; : &quot;ME&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;46100&quot;,
    &quot;descrizione&quot; : &quot;MANTOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;003020&quot;,
    &quot;sigla&quot; : &quot;MN&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;41100&quot;,
    &quot;descrizione&quot; : &quot;MODENA&quot;,
    &quot;codiceISTAT&quot; : &quot;008036&quot;,
    &quot;sigla&quot; : &quot;MO&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;54100&quot;,
    &quot;descrizione&quot; : &quot;MASSA&quot;,
    &quot;codiceISTAT&quot; : &quot;009045&quot;,
    &quot;sigla&quot; : &quot;MS&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;75100&quot;,
    &quot;descrizione&quot; : &quot;MATERA&quot;,
    &quot;codiceISTAT&quot; : &quot;017077&quot;,
    &quot;sigla&quot; : &quot;MT&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;codiceISTATRegione&quot; : &quot;017&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;80100&quot;,
    &quot;descrizione&quot; : &quot;NAPOLI&quot;,
    &quot;codiceISTAT&quot; : &quot;015063&quot;,
    &quot;sigla&quot; : &quot;NA&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;28100&quot;,
    &quot;descrizione&quot; : &quot;NOVARA&quot;,
    &quot;codiceISTAT&quot; : &quot;001003&quot;,
    &quot;sigla&quot; : &quot;NO&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;08100&quot;,
    &quot;descrizione&quot; : &quot;NUORO&quot;,
    &quot;codiceISTAT&quot; : &quot;020091&quot;,
    &quot;sigla&quot; : &quot;NU&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09170&quot;,
    &quot;descrizione&quot; : &quot;ORISTANO&quot;,
    &quot;codiceISTAT&quot; : &quot;020095&quot;,
    &quot;sigla&quot; : &quot;OR&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;90100&quot;,
    &quot;descrizione&quot; : &quot;PALERMO&quot;,
    &quot;codiceISTAT&quot; : &quot;019082&quot;,
    &quot;sigla&quot; : &quot;PA&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;29100&quot;,
    &quot;descrizione&quot; : &quot;PIACENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;008033&quot;,
    &quot;sigla&quot; : &quot;PC&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;35100&quot;,
    &quot;descrizione&quot; : &quot;PADOVA&quot;,
    &quot;codiceISTAT&quot; : &quot;005028&quot;,
    &quot;sigla&quot; : &quot;PD&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;65100&quot;,
    &quot;descrizione&quot; : &quot;PESCARA&quot;,
    &quot;codiceISTAT&quot; : &quot;013068&quot;,
    &quot;sigla&quot; : &quot;PE&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;06100&quot;,
    &quot;descrizione&quot; : &quot;PERUGIA&quot;,
    &quot;codiceISTAT&quot; : &quot;010054&quot;,
    &quot;sigla&quot; : &quot;PG&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;codiceISTATRegione&quot; : &quot;010&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;56100&quot;,
    &quot;descrizione&quot; : &quot;PISA&quot;,
    &quot;codiceISTAT&quot; : &quot;009050&quot;,
    &quot;sigla&quot; : &quot;PI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;33170&quot;,
    &quot;descrizione&quot; : &quot;PORDENONE&quot;,
    &quot;codiceISTAT&quot; : &quot;006093&quot;,
    &quot;sigla&quot; : &quot;PN&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;59100&quot;,
    &quot;descrizione&quot; : &quot;PRATO&quot;,
    &quot;codiceISTAT&quot; : &quot;009100&quot;,
    &quot;sigla&quot; : &quot;PO&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;43100&quot;,
    &quot;descrizione&quot; : &quot;PARMA&quot;,
    &quot;codiceISTAT&quot; : &quot;008034&quot;,
    &quot;sigla&quot; : &quot;PR&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;51100&quot;,
    &quot;descrizione&quot; : &quot;PISTOIA&quot;,
    &quot;codiceISTAT&quot; : &quot;009047&quot;,
    &quot;sigla&quot; : &quot;PT&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;61100&quot;,
    &quot;descrizione&quot; : &quot;PESARO URBINO&quot;,
    &quot;codiceISTAT&quot; : &quot;011041&quot;,
    &quot;sigla&quot; : &quot;PU&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;codiceISTATRegione&quot; : &quot;011&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;27100&quot;,
    &quot;descrizione&quot; : &quot;PAVIA&quot;,
    &quot;codiceISTAT&quot; : &quot;003018&quot;,
    &quot;sigla&quot; : &quot;PV&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;85100&quot;,
    &quot;descrizione&quot; : &quot;POTENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;017076&quot;,
    &quot;sigla&quot; : &quot;PZ&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;codiceISTATRegione&quot; : &quot;017&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;48100&quot;,
    &quot;descrizione&quot; : &quot;RAVENNA&quot;,
    &quot;codiceISTAT&quot; : &quot;008039&quot;,
    &quot;sigla&quot; : &quot;RA&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;89100&quot;,
    &quot;descrizione&quot; : &quot;REGGIO DI CALABRIA&quot;,
    &quot;codiceISTAT&quot; : &quot;018080&quot;,
    &quot;sigla&quot; : &quot;RC&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;42100&quot;,
    &quot;descrizione&quot; : &quot;REGGIO NELL&apos;EMILIA&quot;,
    &quot;codiceISTAT&quot; : &quot;008035&quot;,
    &quot;sigla&quot; : &quot;RE&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;97100&quot;,
    &quot;descrizione&quot; : &quot;RAGUSA&quot;,
    &quot;codiceISTAT&quot; : &quot;019088&quot;,
    &quot;sigla&quot; : &quot;RG&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;02100&quot;,
    &quot;descrizione&quot; : &quot;RIETI&quot;,
    &quot;codiceISTAT&quot; : &quot;012057&quot;,
    &quot;sigla&quot; : &quot;RI&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;00100&quot;,
    &quot;descrizione&quot; : &quot;ROMA&quot;,
    &quot;codiceISTAT&quot; : &quot;012058&quot;,
    &quot;sigla&quot; : &quot;RM&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;47900&quot;,
    &quot;descrizione&quot; : &quot;RIMINI&quot;,
    &quot;codiceISTAT&quot; : &quot;008099&quot;,
    &quot;sigla&quot; : &quot;RN&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;codiceISTATRegione&quot; : &quot;008&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;45100&quot;,
    &quot;descrizione&quot; : &quot;ROVIGO&quot;,
    &quot;codiceISTAT&quot; : &quot;005029&quot;,
    &quot;sigla&quot; : &quot;RO&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;84100&quot;,
    &quot;descrizione&quot; : &quot;SALERNO&quot;,
    &quot;codiceISTAT&quot; : &quot;015065&quot;,
    &quot;sigla&quot; : &quot;SA&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;codiceISTATRegione&quot; : &quot;015&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;53100&quot;,
    &quot;descrizione&quot; : &quot;SIENA&quot;,
    &quot;codiceISTAT&quot; : &quot;009052&quot;,
    &quot;sigla&quot; : &quot;SI&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;codiceISTATRegione&quot; : &quot;009&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;23100&quot;,
    &quot;descrizione&quot; : &quot;SONDRIO&quot;,
    &quot;codiceISTAT&quot; : &quot;003014&quot;,
    &quot;sigla&quot; : &quot;SO&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;19100&quot;,
    &quot;descrizione&quot; : &quot;LA SPEZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;007011&quot;,
    &quot;sigla&quot; : &quot;SP&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;96100&quot;,
    &quot;descrizione&quot; : &quot;SIRACUSA&quot;,
    &quot;codiceISTAT&quot; : &quot;019089&quot;,
    &quot;sigla&quot; : &quot;SR&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;07100&quot;,
    &quot;descrizione&quot; : &quot;SASSARI&quot;,
    &quot;codiceISTAT&quot; : &quot;020090&quot;,
    &quot;sigla&quot; : &quot;SS&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;09013&quot;,
    &quot;descrizione&quot; : &quot;SUD SARDEGNA&quot;,
    &quot;codiceISTAT&quot; : &quot;020111&quot;,
    &quot;sigla&quot; : &quot;SU&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;codiceISTATRegione&quot; : &quot;020&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;17100&quot;,
    &quot;descrizione&quot; : &quot;SAVONA&quot;,
    &quot;codiceISTAT&quot; : &quot;007009&quot;,
    &quot;sigla&quot; : &quot;SV&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;codiceISTATRegione&quot; : &quot;007&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;74100&quot;,
    &quot;descrizione&quot; : &quot;TARANTO&quot;,
    &quot;codiceISTAT&quot; : &quot;016073&quot;,
    &quot;sigla&quot; : &quot;TA&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;codiceISTATRegione&quot; : &quot;016&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;64100&quot;,
    &quot;descrizione&quot; : &quot;TERAMO&quot;,
    &quot;codiceISTAT&quot; : &quot;013067&quot;,
    &quot;sigla&quot; : &quot;TE&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;codiceISTATRegione&quot; : &quot;013&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;38100&quot;,
    &quot;descrizione&quot; : &quot;TRENTO&quot;,
    &quot;codiceISTAT&quot; : &quot;004022&quot;,
    &quot;sigla&quot; : &quot;TN&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;codiceISTATRegione&quot; : &quot;004&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;10100&quot;,
    &quot;descrizione&quot; : &quot;TORINO&quot;,
    &quot;codiceISTAT&quot; : &quot;001001&quot;,
    &quot;sigla&quot; : &quot;TO&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;91100&quot;,
    &quot;descrizione&quot; : &quot;TRAPANI&quot;,
    &quot;codiceISTAT&quot; : &quot;019081&quot;,
    &quot;sigla&quot; : &quot;TP&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;codiceISTATRegione&quot; : &quot;019&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;05100&quot;,
    &quot;descrizione&quot; : &quot;TERNI&quot;,
    &quot;codiceISTAT&quot; : &quot;010055&quot;,
    &quot;sigla&quot; : &quot;TR&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;codiceISTATRegione&quot; : &quot;010&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;34100&quot;,
    &quot;descrizione&quot; : &quot;TRIESTE&quot;,
    &quot;codiceISTAT&quot; : &quot;006032&quot;,
    &quot;sigla&quot; : &quot;TS&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;31100&quot;,
    &quot;descrizione&quot; : &quot;TREVISO&quot;,
    &quot;codiceISTAT&quot; : &quot;005026&quot;,
    &quot;sigla&quot; : &quot;TV&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;33100&quot;,
    &quot;descrizione&quot; : &quot;UDINE&quot;,
    &quot;codiceISTAT&quot; : &quot;006030&quot;,
    &quot;sigla&quot; : &quot;UD&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;codiceISTATRegione&quot; : &quot;006&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;21100&quot;,
    &quot;descrizione&quot; : &quot;VARESE&quot;,
    &quot;codiceISTAT&quot; : &quot;003012&quot;,
    &quot;sigla&quot; : &quot;VA&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;codiceISTATRegione&quot; : &quot;003&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;28900&quot;,
    &quot;descrizione&quot; : &quot;VERBANIA&quot;,
    &quot;codiceISTAT&quot; : &quot;001103&quot;,
    &quot;sigla&quot; : &quot;VB&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;13100&quot;,
    &quot;descrizione&quot; : &quot;VERCELLI&quot;,
    &quot;codiceISTAT&quot; : &quot;001002&quot;,
    &quot;sigla&quot; : &quot;VC&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;codiceISTATRegione&quot; : &quot;001&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;30100&quot;,
    &quot;descrizione&quot; : &quot;VENEZIA&quot;,
    &quot;codiceISTAT&quot; : &quot;005027&quot;,
    &quot;sigla&quot; : &quot;VE&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;36100&quot;,
    &quot;descrizione&quot; : &quot;VICENZA&quot;,
    &quot;codiceISTAT&quot; : &quot;005024&quot;,
    &quot;sigla&quot; : &quot;VI&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;37100&quot;,
    &quot;descrizione&quot; : &quot;VERONA&quot;,
    &quot;codiceISTAT&quot; : &quot;005023&quot;,
    &quot;sigla&quot; : &quot;VR&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;codiceISTATRegione&quot; : &quot;005&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;01100&quot;,
    &quot;descrizione&quot; : &quot;VITERBO&quot;,
    &quot;codiceISTAT&quot; : &quot;012056&quot;,
    &quot;sigla&quot; : &quot;VT&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;codiceISTATRegione&quot; : &quot;012&quot;
  }, {
    &quot;StatusCode&quot; : null,
    &quot;codiceTerritoriale&quot; : &quot;89900&quot;,
    &quot;descrizione&quot; : &quot;VIBO VALENTIA&quot;,
    &quot;codiceISTAT&quot; : &quot;018102&quot;,
    &quot;sigla&quot; : &quot;VV&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;codiceISTATRegione&quot; : &quot;018&quot;
  } ],
  &quot;Data&quot; : {
    &quot;CodiceFiscale&quot; : &quot;N/D&quot;,
    &quot;PaeseDiNascitaUSA&quot; : false,
    &quot;CodiceStato&quot; : &quot;Z200&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;N/D&quot;,
    &quot;SourceSystemIdentifier&quot; : &quot;7177868&quot;,
    &quot;Societa&quot; : &quot;unipolsai&quot;,
    &quot;PaeseDiNascita&quot; : &quot;N/D&quot;
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TransformInserimentoPG_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
