<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;Comune&quot; : {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;siglaProvincia&quot; : &quot;MI&quot;,
    &quot;codiceISTATProvincia&quot; : &quot;003015&quot;,
    &quot;descrizioneProvincia&quot; : &quot;MILANO&quot;,
    &quot;codiceBelfiore&quot; : &quot;F205&quot;,
    &quot;codiceCAB&quot; : &quot;01600-6&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;,
    &quot;codiceISTAT&quot; : &quot;003015146&quot;,
    &quot;attivo&quot; : true,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;descrizioneAbbreviata&quot; : &quot;MILANO&quot;,
    &quot;descrizioneCompleta&quot; : &quot;MILANO&quot;
  },
  &quot;DettagliModifica&quot; : {
    &quot;PaeseDiNascita&quot; : &quot;N/D&quot;,
    &quot;Societa&quot; : &quot;unipolsai&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;Tanzania&quot;,
    &quot;SourceSystemIdentifier&quot; : &quot;7174861&quot;,
    &quot;PaeseDiNascitaUSA&quot; : false,
    &quot;CodiceFiscale&quot; : &quot;N/D&quot;,
    &quot;RecordId&quot; : &quot;AAA&quot;
  },
  &quot;Provincia&quot; : {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;
  },
  &quot;Stato&quot; : {
    &quot;Ue&quot; : false,
    &quot;Attivo&quot; : false,
    &quot;Descrizione&quot; : &quot;N/D&quot;,
    &quot;CodiceABI&quot; : &quot;N/D&quot;
  },
  &quot;Loop&quot; : {
    &quot;LoopBlockIterationIndex&quot; : 1,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiStatus&quot; : true,
    &quot;NumeroIdentificazioneFiscale&quot; : 11111111112,
    &quot;ResidenzaFiscale&quot; : {
      &quot;CodiceABI&quot; : &quot;29&quot;,
      &quot;Descrizione&quot; : &quot;FRANCIA&quot;,
      &quot;CodiceBelfiore&quot; : &quot;Z110&quot;,
      &quot;Attivo&quot; : true,
      &quot;Ue&quot; : true
    },
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z110&quot;
  },
  &quot;Stati&quot; : {
    &quot;Options&quot; : [ {
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlpQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql7QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql8QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qljQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH0QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlMQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm2QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlVQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm1QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlkQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlHQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qklQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHcQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql5QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm4QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlmQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qksQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH1QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHZQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm5QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlDQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CILE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlBQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlPQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkuQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlhQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlaQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlnQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlQQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlvQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlwQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlyQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qloQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGNQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlJQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkoQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qllQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGMQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkzQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGcQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGiQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHNQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlsQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GABON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHXQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlSQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlTQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkxQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql3QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGUQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qluQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHRQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlYQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qktQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql6QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkqQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkpQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql4QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlrQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGzQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGqQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlNQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qknQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGhQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql2QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlfQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHMQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm3QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlGQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlbQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG9QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGlQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP7QAJ&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6nwYQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm0QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlxQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHfQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlKQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlOQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGjQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qltQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGXQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGmQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm8QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHEQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkmQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkkQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlcQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql1QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm6QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlRQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qldQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkyQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm7QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHGQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlXQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGkQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlZQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGfQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qleQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkrQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlCQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQZQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR1QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRGQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmRQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qREQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmUQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR2QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ2QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmPQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPvQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ9QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPmQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQqQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmLQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmMQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPnQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQhQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPEQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQRQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQjQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR0QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQnQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQiQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQvQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;
    } ]
  },
  &quot;Allprovince&quot; : [ {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;AG&quot;,
    &quot;codiceISTAT&quot; : &quot;019084&quot;,
    &quot;descrizione&quot; : &quot;AGRIGENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;92100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;AL&quot;,
    &quot;codiceISTAT&quot; : &quot;001006&quot;,
    &quot;descrizione&quot; : &quot;ALESSANDRIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;15100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;AN&quot;,
    &quot;codiceISTAT&quot; : &quot;011042&quot;,
    &quot;descrizione&quot; : &quot;ANCONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;60100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;002&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;02&quot;,
    &quot;descrizioneRegione&quot; : &quot;VALLE D&apos;AOSTA&quot;,
    &quot;sigla&quot; : &quot;AO&quot;,
    &quot;codiceISTAT&quot; : &quot;002007&quot;,
    &quot;descrizione&quot; : &quot;AOSTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;11100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;AP&quot;,
    &quot;codiceISTAT&quot; : &quot;011044&quot;,
    &quot;descrizione&quot; : &quot;ASCOLI PICENO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;63100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;AQ&quot;,
    &quot;codiceISTAT&quot; : &quot;013066&quot;,
    &quot;descrizione&quot; : &quot;L&apos;AQUILA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;67100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;AR&quot;,
    &quot;codiceISTAT&quot; : &quot;009051&quot;,
    &quot;descrizione&quot; : &quot;AREZZO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;52100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;AT&quot;,
    &quot;codiceISTAT&quot; : &quot;001005&quot;,
    &quot;descrizione&quot; : &quot;ASTI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;14100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;AV&quot;,
    &quot;codiceISTAT&quot; : &quot;015064&quot;,
    &quot;descrizione&quot; : &quot;AVELLINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;83100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BA&quot;,
    &quot;codiceISTAT&quot; : &quot;016072&quot;,
    &quot;descrizione&quot; : &quot;BARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;70100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;BG&quot;,
    &quot;codiceISTAT&quot; : &quot;003016&quot;,
    &quot;descrizione&quot; : &quot;BERGAMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;24100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;BI&quot;,
    &quot;codiceISTAT&quot; : &quot;001096&quot;,
    &quot;descrizione&quot; : &quot;BIELLA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;13900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;BL&quot;,
    &quot;codiceISTAT&quot; : &quot;005025&quot;,
    &quot;descrizione&quot; : &quot;BELLUNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;32100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;BN&quot;,
    &quot;codiceISTAT&quot; : &quot;015062&quot;,
    &quot;descrizione&quot; : &quot;BENEVENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;82100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;BO&quot;,
    &quot;codiceISTAT&quot; : &quot;008037&quot;,
    &quot;descrizione&quot; : &quot;BOLOGNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;40100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BR&quot;,
    &quot;codiceISTAT&quot; : &quot;016074&quot;,
    &quot;descrizione&quot; : &quot;BRINDISI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;72100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;BS&quot;,
    &quot;codiceISTAT&quot; : &quot;003017&quot;,
    &quot;descrizione&quot; : &quot;BRESCIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;25100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BT&quot;,
    &quot;codiceISTAT&quot; : &quot;016110&quot;,
    &quot;descrizione&quot; : &quot;BARLETTA-ANDRIA-TRANI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;76121&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;004&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;sigla&quot; : &quot;BZ&quot;,
    &quot;codiceISTAT&quot; : &quot;004021&quot;,
    &quot;descrizione&quot; : &quot;BOLZANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;39100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;CA&quot;,
    &quot;codiceISTAT&quot; : &quot;020092&quot;,
    &quot;descrizione&quot; : &quot;CAGLIARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;014&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;sigla&quot; : &quot;CB&quot;,
    &quot;codiceISTAT&quot; : &quot;014070&quot;,
    &quot;descrizione&quot; : &quot;CAMPOBASSO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;86100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;CE&quot;,
    &quot;codiceISTAT&quot; : &quot;015061&quot;,
    &quot;descrizione&quot; : &quot;CASERTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;81100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;CH&quot;,
    &quot;codiceISTAT&quot; : &quot;013069&quot;,
    &quot;descrizione&quot; : &quot;CHIETI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;66100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;CL&quot;,
    &quot;codiceISTAT&quot; : &quot;019085&quot;,
    &quot;descrizione&quot; : &quot;CALTANISSETTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;93100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;CN&quot;,
    &quot;codiceISTAT&quot; : &quot;001004&quot;,
    &quot;descrizione&quot; : &quot;CUNEO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;12100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;CO&quot;,
    &quot;codiceISTAT&quot; : &quot;003013&quot;,
    &quot;descrizione&quot; : &quot;COMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;22100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;CR&quot;,
    &quot;codiceISTAT&quot; : &quot;003019&quot;,
    &quot;descrizione&quot; : &quot;CREMONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;26100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;CS&quot;,
    &quot;codiceISTAT&quot; : &quot;018078&quot;,
    &quot;descrizione&quot; : &quot;COSENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;87100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;CT&quot;,
    &quot;codiceISTAT&quot; : &quot;019087&quot;,
    &quot;descrizione&quot; : &quot;CATANIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;95100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;CZ&quot;,
    &quot;codiceISTAT&quot; : &quot;018079&quot;,
    &quot;descrizione&quot; : &quot;CATANZARO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;88100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;000&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;00&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;EE&quot;,
    &quot;codiceISTAT&quot; : &quot;000000&quot;,
    &quot;descrizione&quot; : &quot;ESTERO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;EN&quot;,
    &quot;codiceISTAT&quot; : &quot;019086&quot;,
    &quot;descrizione&quot; : &quot;ENNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;94100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;FC&quot;,
    &quot;codiceISTAT&quot; : &quot;008040&quot;,
    &quot;descrizione&quot; : &quot;FORLI&apos; CESENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;47100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;FE&quot;,
    &quot;codiceISTAT&quot; : &quot;008038&quot;,
    &quot;descrizione&quot; : &quot;FERRARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;44100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;FG&quot;,
    &quot;codiceISTAT&quot; : &quot;016071&quot;,
    &quot;descrizione&quot; : &quot;FOGGIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;71100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;FI&quot;,
    &quot;codiceISTAT&quot; : &quot;009048&quot;,
    &quot;descrizione&quot; : &quot;FIRENZE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;50100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;FM&quot;,
    &quot;codiceISTAT&quot; : &quot;011109&quot;,
    &quot;descrizione&quot; : &quot;FERMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;63900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;FR&quot;,
    &quot;codiceISTAT&quot; : &quot;012060&quot;,
    &quot;descrizione&quot; : &quot;FROSINONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;03100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;GE&quot;,
    &quot;codiceISTAT&quot; : &quot;007010&quot;,
    &quot;descrizione&quot; : &quot;GENOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;16100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;GO&quot;,
    &quot;codiceISTAT&quot; : &quot;006031&quot;,
    &quot;descrizione&quot; : &quot;GORIZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;34170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;GR&quot;,
    &quot;codiceISTAT&quot; : &quot;009053&quot;,
    &quot;descrizione&quot; : &quot;GROSSETO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;58100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;IM&quot;,
    &quot;codiceISTAT&quot; : &quot;007008&quot;,
    &quot;descrizione&quot; : &quot;IMPERIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;18100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;014&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;sigla&quot; : &quot;IS&quot;,
    &quot;codiceISTAT&quot; : &quot;014094&quot;,
    &quot;descrizione&quot; : &quot;ISERNIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;86170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;KR&quot;,
    &quot;codiceISTAT&quot; : &quot;018101&quot;,
    &quot;descrizione&quot; : &quot;CROTONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;88900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;LC&quot;,
    &quot;codiceISTAT&quot; : &quot;003097&quot;,
    &quot;descrizione&quot; : &quot;LECCO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;23900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;LE&quot;,
    &quot;codiceISTAT&quot; : &quot;016075&quot;,
    &quot;descrizione&quot; : &quot;LECCE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;73100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;LI&quot;,
    &quot;codiceISTAT&quot; : &quot;009049&quot;,
    &quot;descrizione&quot; : &quot;LIVORNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;57100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;LO&quot;,
    &quot;codiceISTAT&quot; : &quot;003098&quot;,
    &quot;descrizione&quot; : &quot;LODI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;26900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;LT&quot;,
    &quot;codiceISTAT&quot; : &quot;012059&quot;,
    &quot;descrizione&quot; : &quot;LATINA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;04100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;LU&quot;,
    &quot;codiceISTAT&quot; : &quot;009046&quot;,
    &quot;descrizione&quot; : &quot;LUCCA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;55100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MB&quot;,
    &quot;codiceISTAT&quot; : &quot;003108&quot;,
    &quot;descrizione&quot; : &quot;MONZA E DELLA BRIANZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;MC&quot;,
    &quot;codiceISTAT&quot; : &quot;011043&quot;,
    &quot;descrizione&quot; : &quot;MACERATA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;62100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;ME&quot;,
    &quot;codiceISTAT&quot; : &quot;019083&quot;,
    &quot;descrizione&quot; : &quot;MESSINA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;98100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MN&quot;,
    &quot;codiceISTAT&quot; : &quot;003020&quot;,
    &quot;descrizione&quot; : &quot;MANTOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;46100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;MO&quot;,
    &quot;codiceISTAT&quot; : &quot;008036&quot;,
    &quot;descrizione&quot; : &quot;MODENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;41100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;MS&quot;,
    &quot;codiceISTAT&quot; : &quot;009045&quot;,
    &quot;descrizione&quot; : &quot;MASSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;54100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;017&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;sigla&quot; : &quot;MT&quot;,
    &quot;codiceISTAT&quot; : &quot;017077&quot;,
    &quot;descrizione&quot; : &quot;MATERA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;75100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;NA&quot;,
    &quot;codiceISTAT&quot; : &quot;015063&quot;,
    &quot;descrizione&quot; : &quot;NAPOLI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;80100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;NO&quot;,
    &quot;codiceISTAT&quot; : &quot;001003&quot;,
    &quot;descrizione&quot; : &quot;NOVARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;28100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;NU&quot;,
    &quot;codiceISTAT&quot; : &quot;020091&quot;,
    &quot;descrizione&quot; : &quot;NUORO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;08100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;OR&quot;,
    &quot;codiceISTAT&quot; : &quot;020095&quot;,
    &quot;descrizione&quot; : &quot;ORISTANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;PA&quot;,
    &quot;codiceISTAT&quot; : &quot;019082&quot;,
    &quot;descrizione&quot; : &quot;PALERMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;90100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;PC&quot;,
    &quot;codiceISTAT&quot; : &quot;008033&quot;,
    &quot;descrizione&quot; : &quot;PIACENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;29100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;PD&quot;,
    &quot;codiceISTAT&quot; : &quot;005028&quot;,
    &quot;descrizione&quot; : &quot;PADOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;35100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;PE&quot;,
    &quot;codiceISTAT&quot; : &quot;013068&quot;,
    &quot;descrizione&quot; : &quot;PESCARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;65100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;010&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;sigla&quot; : &quot;PG&quot;,
    &quot;codiceISTAT&quot; : &quot;010054&quot;,
    &quot;descrizione&quot; : &quot;PERUGIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;06100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PI&quot;,
    &quot;codiceISTAT&quot; : &quot;009050&quot;,
    &quot;descrizione&quot; : &quot;PISA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;56100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;PN&quot;,
    &quot;codiceISTAT&quot; : &quot;006093&quot;,
    &quot;descrizione&quot; : &quot;PORDENONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;33170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PO&quot;,
    &quot;codiceISTAT&quot; : &quot;009100&quot;,
    &quot;descrizione&quot; : &quot;PRATO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;59100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;PR&quot;,
    &quot;codiceISTAT&quot; : &quot;008034&quot;,
    &quot;descrizione&quot; : &quot;PARMA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;43100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PT&quot;,
    &quot;codiceISTAT&quot; : &quot;009047&quot;,
    &quot;descrizione&quot; : &quot;PISTOIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;51100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;PU&quot;,
    &quot;codiceISTAT&quot; : &quot;011041&quot;,
    &quot;descrizione&quot; : &quot;PESARO URBINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;61100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;PV&quot;,
    &quot;codiceISTAT&quot; : &quot;003018&quot;,
    &quot;descrizione&quot; : &quot;PAVIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;27100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;017&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;sigla&quot; : &quot;PZ&quot;,
    &quot;codiceISTAT&quot; : &quot;017076&quot;,
    &quot;descrizione&quot; : &quot;POTENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;85100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RA&quot;,
    &quot;codiceISTAT&quot; : &quot;008039&quot;,
    &quot;descrizione&quot; : &quot;RAVENNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;48100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;RC&quot;,
    &quot;codiceISTAT&quot; : &quot;018080&quot;,
    &quot;descrizione&quot; : &quot;REGGIO DI CALABRIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;89100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RE&quot;,
    &quot;codiceISTAT&quot; : &quot;008035&quot;,
    &quot;descrizione&quot; : &quot;REGGIO NELL&apos;EMILIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;42100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;RG&quot;,
    &quot;codiceISTAT&quot; : &quot;019088&quot;,
    &quot;descrizione&quot; : &quot;RAGUSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;97100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;RI&quot;,
    &quot;codiceISTAT&quot; : &quot;012057&quot;,
    &quot;descrizione&quot; : &quot;RIETI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;02100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;RM&quot;,
    &quot;codiceISTAT&quot; : &quot;012058&quot;,
    &quot;descrizione&quot; : &quot;ROMA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;00100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RN&quot;,
    &quot;codiceISTAT&quot; : &quot;008099&quot;,
    &quot;descrizione&quot; : &quot;RIMINI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;47900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;RO&quot;,
    &quot;codiceISTAT&quot; : &quot;005029&quot;,
    &quot;descrizione&quot; : &quot;ROVIGO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;45100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;SA&quot;,
    &quot;codiceISTAT&quot; : &quot;015065&quot;,
    &quot;descrizione&quot; : &quot;SALERNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;84100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;SI&quot;,
    &quot;codiceISTAT&quot; : &quot;009052&quot;,
    &quot;descrizione&quot; : &quot;SIENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;53100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;SO&quot;,
    &quot;codiceISTAT&quot; : &quot;003014&quot;,
    &quot;descrizione&quot; : &quot;SONDRIO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;23100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;SP&quot;,
    &quot;codiceISTAT&quot; : &quot;007011&quot;,
    &quot;descrizione&quot; : &quot;LA SPEZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;19100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;SR&quot;,
    &quot;codiceISTAT&quot; : &quot;019089&quot;,
    &quot;descrizione&quot; : &quot;SIRACUSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;96100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;SS&quot;,
    &quot;codiceISTAT&quot; : &quot;020090&quot;,
    &quot;descrizione&quot; : &quot;SASSARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;07100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;SU&quot;,
    &quot;codiceISTAT&quot; : &quot;020111&quot;,
    &quot;descrizione&quot; : &quot;SUD SARDEGNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09013&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;SV&quot;,
    &quot;codiceISTAT&quot; : &quot;007009&quot;,
    &quot;descrizione&quot; : &quot;SAVONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;17100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;TA&quot;,
    &quot;codiceISTAT&quot; : &quot;016073&quot;,
    &quot;descrizione&quot; : &quot;TARANTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;74100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;TE&quot;,
    &quot;codiceISTAT&quot; : &quot;013067&quot;,
    &quot;descrizione&quot; : &quot;TERAMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;64100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;004&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;sigla&quot; : &quot;TN&quot;,
    &quot;codiceISTAT&quot; : &quot;004022&quot;,
    &quot;descrizione&quot; : &quot;TRENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;38100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;TO&quot;,
    &quot;codiceISTAT&quot; : &quot;001001&quot;,
    &quot;descrizione&quot; : &quot;TORINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;10100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;TP&quot;,
    &quot;codiceISTAT&quot; : &quot;019081&quot;,
    &quot;descrizione&quot; : &quot;TRAPANI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;91100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;010&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;sigla&quot; : &quot;TR&quot;,
    &quot;codiceISTAT&quot; : &quot;010055&quot;,
    &quot;descrizione&quot; : &quot;TERNI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;05100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;TS&quot;,
    &quot;codiceISTAT&quot; : &quot;006032&quot;,
    &quot;descrizione&quot; : &quot;TRIESTE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;34100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;TV&quot;,
    &quot;codiceISTAT&quot; : &quot;005026&quot;,
    &quot;descrizione&quot; : &quot;TREVISO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;31100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;UD&quot;,
    &quot;codiceISTAT&quot; : &quot;006030&quot;,
    &quot;descrizione&quot; : &quot;UDINE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;33100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;VA&quot;,
    &quot;codiceISTAT&quot; : &quot;003012&quot;,
    &quot;descrizione&quot; : &quot;VARESE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;21100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;VB&quot;,
    &quot;codiceISTAT&quot; : &quot;001103&quot;,
    &quot;descrizione&quot; : &quot;VERBANIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;28900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;VC&quot;,
    &quot;codiceISTAT&quot; : &quot;001002&quot;,
    &quot;descrizione&quot; : &quot;VERCELLI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;13100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VE&quot;,
    &quot;codiceISTAT&quot; : &quot;005027&quot;,
    &quot;descrizione&quot; : &quot;VENEZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;30100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VI&quot;,
    &quot;codiceISTAT&quot; : &quot;005024&quot;,
    &quot;descrizione&quot; : &quot;VICENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;36100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VR&quot;,
    &quot;codiceISTAT&quot; : &quot;005023&quot;,
    &quot;descrizione&quot; : &quot;VERONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;37100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;VT&quot;,
    &quot;codiceISTAT&quot; : &quot;012056&quot;,
    &quot;descrizione&quot; : &quot;VITERBO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;01100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;VV&quot;,
    &quot;codiceISTAT&quot; : &quot;018102&quot;,
    &quot;descrizione&quot; : &quot;VIBO VALENTIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;89900&quot;
  } ],
  &quot;id&quot; : 336,
  &quot;compagnia&quot; : &quot;unipolsai&quot;,
  &quot;ciu&quot; : 7174861,
  &quot;tipologiaSocietaFatca&quot; : &quot;0&quot;,
  &quot;residenzaFiscaleUSA&quot; : true,
  &quot;residenzaFiscale&quot; : true,
  &quot;cittadinanzaUSA&quot; : true,
  &quot;cittadinanze&quot; : [ ],
  &quot;poteriFirmaUSA&quot; : true,
  &quot;poteriFirma&quot; : true,
  &quot;potCittadinanzaUsa&quot; : false,
  &quot;tin&quot; : &quot;111111112&quot;,
  &quot;giin&quot; : null,
  &quot;indirizzo&quot; : {
    &quot;codiceBelfioreStato&quot; : &quot;F205&quot;,
    &quot;tipoIndirizzo&quot; : &quot;AMMI&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;000000215&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;MI&quot;,
    &quot;codiceIstatComune&quot; : &quot;000215&quot;,
    &quot;codicePostale&quot; : &quot;20019&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;F205&quot;,
    &quot;localitaBreve&quot; : null,
    &quot;localita&quot; : &quot;MILANO&quot;,
    &quot;dug&quot; : &quot;VIA&quot;,
    &quot;numeroCivico&quot; : &quot;20&quot;,
    &quot;dus&quot; : &quot;CAPPUCCINI&quot;,
    &quot;indirizzoCompleto&quot; : &quot;VIA CAPPUCCINI&quot;,
    &quot;tipoNormalizzato&quot; : &quot;N&quot;,
    &quot;indirizzoBreve&quot; : &quot;VIA CAPPUCCINI 20&quot;,
    &quot;presso&quot; : &quot;C/O BAR&quot;,
    &quot;longitudine&quot; : 9.204906,
    &quot;latitudine&quot; : 45.470623,
    &quot;cabStatoCod&quot; : null,
    &quot;distrettoCensuario&quot; : &quot;1&quot;,
    &quot;datiTracciatura&quot; : {
      &quot;dataCreazione&quot; : &quot;2025-02-26T12:58:58.980259&quot;,
      &quot;userIdCreazione&quot; : &quot;SFDC&quot;,
      &quot;usernameCreazione&quot; : &quot;DEFAULT DEFAULT&quot;,
      &quot;dataUltimoAggiornamento&quot; : null,
      &quot;userIdUltimoAggiornamento&quot; : null,
      &quot;usernameUltimoAggiornamento&quot; : null,
      &quot;canaleCreazione&quot; : &quot;A01&quot;,
      &quot;compagniaCreazione&quot; : &quot;1&quot;,
      &quot;sistemaCreazione&quot; : &quot;A17&quot;,
      &quot;sottosistemaCreazione&quot; : &quot;A0022&quot;,
      &quot;canaleAggiornamento&quot; : null,
      &quot;compagniaAggiornamento&quot; : null,
      &quot;sistemaAggiornamento&quot; : null,
      &quot;sottosistemaAggiornamento&quot; : null,
      &quot;applicazioneChiamante&quot; : &quot;PU&quot;,
      &quot;proceduraChiamante&quot; : &quot;PU00A&quot;
    },
    &quot;id&quot; : 5571782,
    &quot;compagnia&quot; : &quot;unipolsai&quot;,
    &quot;ciu&quot; : 7174861
  },
  &quot;residenzeFiscaliEstere&quot; : [ {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z110&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : &quot;11111111112&quot;
  } ]
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TrasformeResponsePG</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem2</globalKey>
        <inputFieldName>Stati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:indirizzo:cabStatoCod var:null == &quot;IT&quot; var:Stato:Descrizione IF</formulaConverted>
        <formulaExpression>IF((indirizzo:cabStatoCod) == null, &quot;IT&quot; , Stato:Descrizione)</formulaExpression>
        <formulaResultPath>Nazione</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem1</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:indirizzo:dug &quot;/\/\/&quot; var:indirizzo:dus CONCAT</formulaConverted>
        <formulaExpression>CONCAT(indirizzo:dug, &quot; &quot;,indirizzo:dus)</formulaExpression>
        <formulaResultPath>Indirizzo</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem4</globalKey>
        <inputFieldName>Loop:NumeroIdentificazioneFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Estero:NumeroIdentificazioneFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionProvince</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem8</globalKey>
        <inputFieldName>Allprovince:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionProvince:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem7</globalKey>
        <inputFieldName>DettagliModifica:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem6</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:CodiceBelfioreOld</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem5</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsLocalita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem12</globalKey>
        <inputFieldName>indirizzo:localita</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Localita</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem11</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsStati</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem10</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Options</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem9</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>OptionsComuni</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem16</globalKey>
        <inputFieldName>residenzaFiscaleUSA</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleUsa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem15</globalKey>
        <inputFieldName>Comune:descrizioneProvincia</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Provincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem14</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Ue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:UnioneEuropea</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem13</globalKey>
        <inputFieldName>ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem20</globalKey>
        <inputFieldName>indirizzo:latitudine</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Latitudine</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>IT</defaultValue>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem19</globalKey>
        <inputFieldName>Stati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Options:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem18</globalKey>
        <inputFieldName>indirizzo:id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>IndirizzoId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem17</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceABI</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:CodiceABI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem55</globalKey>
        <inputFieldName>Stati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:OptionsStati:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem24</globalKey>
        <inputFieldName>Provincia:sigla</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>SiglaProvincia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem23</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceABI</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Estero:CodiceABI</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem22</globalKey>
        <inputFieldName>indirizzo:tipoIndirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>TipoIndirizzo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem21</globalKey>
        <inputFieldName>tin</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Tin</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem28</globalKey>
        <inputFieldName>AllComuni:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsComuni:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem27</globalKey>
        <inputFieldName>AllLocalita:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsLocalita:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem26</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Estero:Nazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem25</globalKey>
        <inputFieldName>Stati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsStati:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem30</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Ue</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:Ue</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>-</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem29</globalKey>
        <inputFieldName>giin</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Giin</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem34</globalKey>
        <inputFieldName>indirizzo:numeroCivico</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>NumeroCivico</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem33</globalKey>
        <inputFieldName>Allprovince:sigla</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionProvince:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem32</globalKey>
        <inputFieldName>residenzaFiscale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>ResidenzaFiscaleStatoEstero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem31</globalKey>
        <inputFieldName>Stati:Options:CodiceBelfiore__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:OptionsStati:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem38</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:CodiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:CodiceBelfiore</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem37</globalKey>
        <inputFieldName>AllComuni:codiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsComuni:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>Z000</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem36</globalKey>
        <inputFieldName>Stato:CodiceBelfiore</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Stato</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem35</globalKey>
        <inputFieldName>Loop:ResidenzaFiscale:Attivo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:Attivo</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem42</globalKey>
        <inputFieldName>DettagliModifica:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:Compagnia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem41</globalKey>
        <inputFieldName>tipologiaSocietaFatca</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>TipologiaSocietaFatca</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem40</globalKey>
        <inputFieldName>indirizzo:longitudine</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Longitudine</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem39</globalKey>
        <inputFieldName>ciu</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Estero:Ciu</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem46</globalKey>
        <inputFieldName>indirizzo:codicePostale</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CAP</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem45</globalKey>
        <inputFieldName>AllLocalita:descrizioneAbbreviata</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>OptionsLocalita:value</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem44</globalKey>
        <inputFieldName>Loop:FlagIdentificazione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>Estero:FlagIdentificazione</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem43</globalKey>
        <inputFieldName>Stati:Options:Descrizione__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Options:label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem50</globalKey>
        <inputFieldName>Indirizzo</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>IndirizzoCompleto</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem49</globalKey>
        <inputFieldName>indirizzo:indirizzoBreve</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>IndirizzoBreve</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem48</globalKey>
        <inputFieldName>Comune:descrizione</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Comune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem47</globalKey>
        <inputFieldName>indirizzo:distrettoCensuario</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>CellaCensuaria</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem54</globalKey>
        <inputFieldName>indirizzo:codiceBelfioreComune</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CodiceBelfioreComune</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem53</globalKey>
        <inputFieldName>DettagliModifica:RecordId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>RecordId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem52</globalKey>
        <inputFieldName>DettagliModifica:Societa</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TrasformeResponsePGCustom0jI9V000000zBhhUAEItem51</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TrasformeResponsePG</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Estero</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;Comune&quot; : {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;siglaProvincia&quot; : &quot;MI&quot;,
    &quot;codiceISTATProvincia&quot; : &quot;003015&quot;,
    &quot;descrizioneProvincia&quot; : &quot;MILANO&quot;,
    &quot;codiceBelfiore&quot; : &quot;F205&quot;,
    &quot;codiceCAB&quot; : &quot;01600-6&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;,
    &quot;codiceISTAT&quot; : &quot;003015146&quot;,
    &quot;attivo&quot; : true,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;descrizioneAbbreviata&quot; : &quot;MILANO&quot;,
    &quot;descrizioneCompleta&quot; : &quot;MILANO&quot;
  },
  &quot;DettagliModifica&quot; : {
    &quot;PaeseDiNascita&quot; : &quot;N/D&quot;,
    &quot;Societa&quot; : &quot;unipolsai&quot;,
    &quot;PaeseDiResidenza&quot; : &quot;Tanzania&quot;,
    &quot;SourceSystemIdentifier&quot; : &quot;7174861&quot;,
    &quot;PaeseDiNascitaUSA&quot; : false,
    &quot;CodiceFiscale&quot; : &quot;N/D&quot;,
    &quot;RecordId&quot; : &quot;AAA&quot;
  },
  &quot;Provincia&quot; : {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;
  },
  &quot;Stato&quot; : {
    &quot;Ue&quot; : false,
    &quot;Attivo&quot; : false,
    &quot;Descrizione&quot; : &quot;N/D&quot;,
    &quot;CodiceABI&quot; : &quot;N/D&quot;
  },
  &quot;Loop&quot; : {
    &quot;LoopBlockIterationIndex&quot; : 1,
    &quot;LoopBlockIterationStatus&quot; : true,
    &quot;GetStatiStatus&quot; : true,
    &quot;NumeroIdentificazioneFiscale&quot; : 11111111112,
    &quot;ResidenzaFiscale&quot; : {
      &quot;CodiceABI&quot; : &quot;29&quot;,
      &quot;Descrizione&quot; : &quot;FRANCIA&quot;,
      &quot;CodiceBelfiore&quot; : &quot;Z110&quot;,
      &quot;Attivo&quot; : true,
      &quot;Ue&quot; : true
    },
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;Z110&quot;
  },
  &quot;Stati&quot; : {
    &quot;Options&quot; : [ {
      &quot;Descrizione__c&quot; : &quot;AFGHANISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z200&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALBANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlpQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z100&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ALGERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql7QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z301&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AMERICAN SAMOA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql8QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z725&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANDORRA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qljQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z101&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGOLA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z302&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANGUILLA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z529&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ANTIGUA E BARBUDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z532&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARABIA SAUDITA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z203&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARGENTINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH0QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z600&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARMENIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z252&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ARUBA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlMQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z501&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm2QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z700&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AUSTRIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlVQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z102&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;AZERBAIJAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm1QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z253&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHAMAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlkQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z502&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BAHREIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z204&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BANGLADESH&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlHQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z249&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BARBADOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qklQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z522&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELGIO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z103&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BELIZE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z512&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BENIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z314&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BERMUDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z400&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BHUTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z205&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BIELORUSSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z139&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOLIVIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHcQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z601&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOSNIA ED ERZEGOVINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql5QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z153&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BOTSWANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm4QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z358&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BRASILE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z602&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BULGARIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlmQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z104&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURKINA FASO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z354&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;BURUNDI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z305&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CALEDONIA NUOVA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z716&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMBOGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qksQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z208&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAMERUN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH1QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z306&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CANADA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHZQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z401&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CAPO VERDE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm5QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z307&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIAD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlDQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z309&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CILE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlBQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z603&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CINA REPUBBLICA POPOLARE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z210&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CIPRO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z211&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CITTA&apos; DEL VATICANO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z106&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COLOMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z604&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COMORE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlPQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z310&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA DEMOCRATICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z312&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CONGO REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkuQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z311&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlhQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z214&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COREA DEL SUD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlaQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z213&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA D&apos;AVORIO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z313&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;COSTA RICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlnQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z503&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CROAZIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlQQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z149&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;CUBA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlvQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z504&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DANIMARCA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z107&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE AUSTRALIANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z900&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z901&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE CANADESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlwQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z800&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE FRANCESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z902&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NEOZELANDESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z903&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ANTARTICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z904&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE NORVEGESI ARTICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlyQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z801&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE RUSSE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z802&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE STATUNITENSI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z905&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DIPENDENZE SUDAFRICANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qloQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z906&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DJIBOUTI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGNQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z361&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;DOMINICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlJQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z526&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ECUADOR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkoQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z605&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EGITTO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qllQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z336&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EL SALVADOR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z506&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;EMIRATI ARABI UNITI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGMQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z215&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ERITREA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkzQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z368&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESTONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGcQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z144&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ESWATINI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z349&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ETIOPIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z315&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FAROE ISLANDS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGiQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z108&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FIJI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z704&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FILIPPINE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHNQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z216&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FINLANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z109&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;FRANCIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlsQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z110&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GABON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHXQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z316&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GAMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlSQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z317&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GEORGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlTQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z254&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERMANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkxQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z112&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GERUSALEMME&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql3QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z260&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GHANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z318&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAMAICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGUQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z507&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIAPPONE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qluQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z219&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIBILTERRA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHRQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z113&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GIORDANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z220&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRECIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z115&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GRENADA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlYQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z524&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GROENLANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qktQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z402&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUADALUPE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z508&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUAM&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql6QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z706&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUATEMALA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkqQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z509&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA-BISSAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z320&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA EQUATORIALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkpQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z321&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUINEA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql4QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z319&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlrQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z607&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;GUYANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z606&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HAITI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z510&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;HONDURAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z511&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z222&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;INDONESIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGzQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z223&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGqQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z224&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRAQ&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlNQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z225&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRIAN OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qknQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z707&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;IRLANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z116&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISLANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGhQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z117&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA CHRISTMAS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql2QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z702&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLA NORFOLK&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z715&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE CAYMAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z530&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COCOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlfQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z212&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE COOK&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHMQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z703&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE FALKLAND&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm3QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z609&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE MARSHALL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z711&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE SOLOMON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlGQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z724&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE TURKS E CAICOS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z519&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI AMERICANE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlbQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z520&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE VERGINI BRITANNICHE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qG9QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z525&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISOLE WALLIS E FUTUNA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z729&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ISRAELE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGlQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z226&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ITALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP7QAJ&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;JERSEY (BALIATO DI)&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6nwYQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z124&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KAZAKHSTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm0QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z255&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KENYA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlxQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z322&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KIRIBATI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHfQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z731&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KOSOVO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlKQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z160&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KUWAIT&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlOQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z227&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;KYRGYZSTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGjQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z256&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LAO PEOPLE&apos;S DEMOCRATIC REPUBLIC&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z228&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LESOTHO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z359&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LETTONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qltQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z145&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBANO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z229&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGXQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z325&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z326&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LIECHTENSTEIN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGmQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z119&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LITUANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm8QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z146&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;LUSSEMBURGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z120&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHEQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z231&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MACEDONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkmQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z148&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MADAGASCAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkkQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z327&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAWI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z328&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALAYSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z247&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALDIVE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlcQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z232&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6ql1QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z329&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MALTA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm6QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z121&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlRQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z122&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARIANNE DEL NORD&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z710&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAROCCO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qldQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z330&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MARTINICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkyQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z513&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qm7QAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z331&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAURITIUS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qH3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z332&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MAYOTTE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z360&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MESSICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z514&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MICRONESIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qHGQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z735&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOLDAVIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlXQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z140&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONACO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGkQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z123&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONGOLIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlZQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z233&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTENEGRO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qGfQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z159&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MONTSERRAT&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qleQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z531&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MOZAMBICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qkrQAB&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z333&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;MYANMAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qlCQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z206&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAMIBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z300&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NAURU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z713&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NEPAL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z234&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NICARAGUA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQZQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z515&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z335&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIGER&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR1QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z334&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NIUE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z714&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NORVEGIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z125&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;NUOVA ZELANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQdQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z719&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;OMAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQaQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z235&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAESI BASSI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z126&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRGQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z236&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PALAU REPUBBLICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQbQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z734&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PANAMA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z516&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PAPUA NUOVA GUINEA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z730&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PARAGUAY&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmRQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z610&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PERU&apos;&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z611&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PITCAIRN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qREQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z722&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLINESIA FRANCESE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z723&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;POLONIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmUQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z127&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PORTOGALLO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR2QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z128&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;PUERTO RICO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQFQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z518&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;QATAR&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z237&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REGNO UNITO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ2QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z114&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CECA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z156&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA CENTRAFRICANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQWQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z308&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DEL SUD SUDAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQPQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z907&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REPUBBLICA DOMINICANA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQKQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z505&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;REUNION&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRLQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z324&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ROMANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z129&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z338&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;RUSSIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z154&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAHARA OCCIDENTALE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQyQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z339&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT CHRISTOPHER E NEVIS&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQCQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z533&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT LUCIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z527&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAINT VINCENT E GRANADINE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR5QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z528&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAMOA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmPQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z726&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAN MARINO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPvQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z130&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SANT&apos;ELENA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z340&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SAO TOME&apos; E PRINCIPE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP3QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z341&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SENEGAL&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z343&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SERBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRHQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z158&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SEYCHELLES&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ9QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z342&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIERRA LEONE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRJQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z344&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SINGAPORE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQpQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z248&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SIRIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPmQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z240&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVACCHIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQqQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z155&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SLOVENIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPoQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z150&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SOMALIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmLQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z345&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SPAGNA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z131&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SRI LANKA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQYQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z209&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;STATI UNITI D&apos;AMERICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPBQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z404&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ST. PIERRE AND MIQUELON&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rP8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z403&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUD AFRICA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQeQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z347&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SUDAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qmMQAR&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z348&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SULTANATO DEL BRUNEI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQQQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z207&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SURINAME&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ4QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z608&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVEZIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPnQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z132&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;SVIZZERA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQgQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z133&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAGIKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ8QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z257&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TAIWAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQVQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z217&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TANZANIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQSQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z357&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TERRITORI PALESTINESI&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQhQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z161&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;THAILANDIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRDQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z241&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TIMOR-LESTE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6rPEQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z242&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQTQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z351&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TOKELAU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQRQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z727&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TONGA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQ6QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z728&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TRINIDAD E TOBAGO&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z612&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUNISIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRAQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z352&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURCHIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQjQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z243&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TURKMENISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR7QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z258&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;TUVALU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQwQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z732&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UCRAINA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qPuQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z138&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UGANDA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQtQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z353&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UNGHERIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qR0QAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z134&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;URUGUAY&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQrQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z613&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;UZBEKISTAN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQOQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z259&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VANUATU&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQnQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z733&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VENEZUELA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQiQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z614&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;VIETNAM&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQvQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z251&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;YEMEN&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQxQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z246&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZAMBIA&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qRIQAZ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z355&quot;
    }, {
      &quot;Descrizione__c&quot; : &quot;ZIMBABWE&quot;,
      &quot;Id&quot; : &quot;m0a9Q00000I6qQsQAJ&quot;,
      &quot;CodiceBelfiore__c&quot; : &quot;Z337&quot;
    } ]
  },
  &quot;Allprovince&quot; : [ {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;AG&quot;,
    &quot;codiceISTAT&quot; : &quot;019084&quot;,
    &quot;descrizione&quot; : &quot;AGRIGENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;92100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;AL&quot;,
    &quot;codiceISTAT&quot; : &quot;001006&quot;,
    &quot;descrizione&quot; : &quot;ALESSANDRIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;15100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;AN&quot;,
    &quot;codiceISTAT&quot; : &quot;011042&quot;,
    &quot;descrizione&quot; : &quot;ANCONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;60100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;002&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;02&quot;,
    &quot;descrizioneRegione&quot; : &quot;VALLE D&apos;AOSTA&quot;,
    &quot;sigla&quot; : &quot;AO&quot;,
    &quot;codiceISTAT&quot; : &quot;002007&quot;,
    &quot;descrizione&quot; : &quot;AOSTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;11100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;AP&quot;,
    &quot;codiceISTAT&quot; : &quot;011044&quot;,
    &quot;descrizione&quot; : &quot;ASCOLI PICENO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;63100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;AQ&quot;,
    &quot;codiceISTAT&quot; : &quot;013066&quot;,
    &quot;descrizione&quot; : &quot;L&apos;AQUILA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;67100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;AR&quot;,
    &quot;codiceISTAT&quot; : &quot;009051&quot;,
    &quot;descrizione&quot; : &quot;AREZZO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;52100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;AT&quot;,
    &quot;codiceISTAT&quot; : &quot;001005&quot;,
    &quot;descrizione&quot; : &quot;ASTI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;14100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;AV&quot;,
    &quot;codiceISTAT&quot; : &quot;015064&quot;,
    &quot;descrizione&quot; : &quot;AVELLINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;83100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BA&quot;,
    &quot;codiceISTAT&quot; : &quot;016072&quot;,
    &quot;descrizione&quot; : &quot;BARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;70100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;BG&quot;,
    &quot;codiceISTAT&quot; : &quot;003016&quot;,
    &quot;descrizione&quot; : &quot;BERGAMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;24100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;BI&quot;,
    &quot;codiceISTAT&quot; : &quot;001096&quot;,
    &quot;descrizione&quot; : &quot;BIELLA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;13900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;BL&quot;,
    &quot;codiceISTAT&quot; : &quot;005025&quot;,
    &quot;descrizione&quot; : &quot;BELLUNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;32100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;BN&quot;,
    &quot;codiceISTAT&quot; : &quot;015062&quot;,
    &quot;descrizione&quot; : &quot;BENEVENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;82100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;BO&quot;,
    &quot;codiceISTAT&quot; : &quot;008037&quot;,
    &quot;descrizione&quot; : &quot;BOLOGNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;40100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BR&quot;,
    &quot;codiceISTAT&quot; : &quot;016074&quot;,
    &quot;descrizione&quot; : &quot;BRINDISI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;72100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;BS&quot;,
    &quot;codiceISTAT&quot; : &quot;003017&quot;,
    &quot;descrizione&quot; : &quot;BRESCIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;25100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;BT&quot;,
    &quot;codiceISTAT&quot; : &quot;016110&quot;,
    &quot;descrizione&quot; : &quot;BARLETTA-ANDRIA-TRANI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;76121&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;004&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;sigla&quot; : &quot;BZ&quot;,
    &quot;codiceISTAT&quot; : &quot;004021&quot;,
    &quot;descrizione&quot; : &quot;BOLZANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;39100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;CA&quot;,
    &quot;codiceISTAT&quot; : &quot;020092&quot;,
    &quot;descrizione&quot; : &quot;CAGLIARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;014&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;sigla&quot; : &quot;CB&quot;,
    &quot;codiceISTAT&quot; : &quot;014070&quot;,
    &quot;descrizione&quot; : &quot;CAMPOBASSO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;86100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;CE&quot;,
    &quot;codiceISTAT&quot; : &quot;015061&quot;,
    &quot;descrizione&quot; : &quot;CASERTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;81100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;CH&quot;,
    &quot;codiceISTAT&quot; : &quot;013069&quot;,
    &quot;descrizione&quot; : &quot;CHIETI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;66100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;CL&quot;,
    &quot;codiceISTAT&quot; : &quot;019085&quot;,
    &quot;descrizione&quot; : &quot;CALTANISSETTA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;93100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;CN&quot;,
    &quot;codiceISTAT&quot; : &quot;001004&quot;,
    &quot;descrizione&quot; : &quot;CUNEO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;12100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;CO&quot;,
    &quot;codiceISTAT&quot; : &quot;003013&quot;,
    &quot;descrizione&quot; : &quot;COMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;22100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;CR&quot;,
    &quot;codiceISTAT&quot; : &quot;003019&quot;,
    &quot;descrizione&quot; : &quot;CREMONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;26100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;CS&quot;,
    &quot;codiceISTAT&quot; : &quot;018078&quot;,
    &quot;descrizione&quot; : &quot;COSENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;87100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;CT&quot;,
    &quot;codiceISTAT&quot; : &quot;019087&quot;,
    &quot;descrizione&quot; : &quot;CATANIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;95100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;CZ&quot;,
    &quot;codiceISTAT&quot; : &quot;018079&quot;,
    &quot;descrizione&quot; : &quot;CATANZARO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;88100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;000&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;00&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;EE&quot;,
    &quot;codiceISTAT&quot; : &quot;000000&quot;,
    &quot;descrizione&quot; : &quot;ESTERO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;EN&quot;,
    &quot;codiceISTAT&quot; : &quot;019086&quot;,
    &quot;descrizione&quot; : &quot;ENNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;94100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;FC&quot;,
    &quot;codiceISTAT&quot; : &quot;008040&quot;,
    &quot;descrizione&quot; : &quot;FORLI&apos; CESENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;47100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;FE&quot;,
    &quot;codiceISTAT&quot; : &quot;008038&quot;,
    &quot;descrizione&quot; : &quot;FERRARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;44100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;FG&quot;,
    &quot;codiceISTAT&quot; : &quot;016071&quot;,
    &quot;descrizione&quot; : &quot;FOGGIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;71100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;FI&quot;,
    &quot;codiceISTAT&quot; : &quot;009048&quot;,
    &quot;descrizione&quot; : &quot;FIRENZE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;50100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;FM&quot;,
    &quot;codiceISTAT&quot; : &quot;011109&quot;,
    &quot;descrizione&quot; : &quot;FERMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;63900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;FR&quot;,
    &quot;codiceISTAT&quot; : &quot;012060&quot;,
    &quot;descrizione&quot; : &quot;FROSINONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;03100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;GE&quot;,
    &quot;codiceISTAT&quot; : &quot;007010&quot;,
    &quot;descrizione&quot; : &quot;GENOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;16100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;GO&quot;,
    &quot;codiceISTAT&quot; : &quot;006031&quot;,
    &quot;descrizione&quot; : &quot;GORIZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;34170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;GR&quot;,
    &quot;codiceISTAT&quot; : &quot;009053&quot;,
    &quot;descrizione&quot; : &quot;GROSSETO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;58100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;IM&quot;,
    &quot;codiceISTAT&quot; : &quot;007008&quot;,
    &quot;descrizione&quot; : &quot;IMPERIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;18100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;014&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;13&quot;,
    &quot;descrizioneRegione&quot; : &quot;MOLISE&quot;,
    &quot;sigla&quot; : &quot;IS&quot;,
    &quot;codiceISTAT&quot; : &quot;014094&quot;,
    &quot;descrizione&quot; : &quot;ISERNIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;86170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;KR&quot;,
    &quot;codiceISTAT&quot; : &quot;018101&quot;,
    &quot;descrizione&quot; : &quot;CROTONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;88900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;LC&quot;,
    &quot;codiceISTAT&quot; : &quot;003097&quot;,
    &quot;descrizione&quot; : &quot;LECCO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;23900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;LE&quot;,
    &quot;codiceISTAT&quot; : &quot;016075&quot;,
    &quot;descrizione&quot; : &quot;LECCE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;73100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;LI&quot;,
    &quot;codiceISTAT&quot; : &quot;009049&quot;,
    &quot;descrizione&quot; : &quot;LIVORNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;57100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;LO&quot;,
    &quot;codiceISTAT&quot; : &quot;003098&quot;,
    &quot;descrizione&quot; : &quot;LODI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;26900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;LT&quot;,
    &quot;codiceISTAT&quot; : &quot;012059&quot;,
    &quot;descrizione&quot; : &quot;LATINA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;04100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;LU&quot;,
    &quot;codiceISTAT&quot; : &quot;009046&quot;,
    &quot;descrizione&quot; : &quot;LUCCA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;55100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MB&quot;,
    &quot;codiceISTAT&quot; : &quot;003108&quot;,
    &quot;descrizione&quot; : &quot;MONZA E DELLA BRIANZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;MC&quot;,
    &quot;codiceISTAT&quot; : &quot;011043&quot;,
    &quot;descrizione&quot; : &quot;MACERATA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;62100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;ME&quot;,
    &quot;codiceISTAT&quot; : &quot;019083&quot;,
    &quot;descrizione&quot; : &quot;MESSINA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;98100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MI&quot;,
    &quot;codiceISTAT&quot; : &quot;003015&quot;,
    &quot;descrizione&quot; : &quot;MILANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;20100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;MN&quot;,
    &quot;codiceISTAT&quot; : &quot;003020&quot;,
    &quot;descrizione&quot; : &quot;MANTOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;46100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;MO&quot;,
    &quot;codiceISTAT&quot; : &quot;008036&quot;,
    &quot;descrizione&quot; : &quot;MODENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;41100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;MS&quot;,
    &quot;codiceISTAT&quot; : &quot;009045&quot;,
    &quot;descrizione&quot; : &quot;MASSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;54100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;017&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;sigla&quot; : &quot;MT&quot;,
    &quot;codiceISTAT&quot; : &quot;017077&quot;,
    &quot;descrizione&quot; : &quot;MATERA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;75100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;NA&quot;,
    &quot;codiceISTAT&quot; : &quot;015063&quot;,
    &quot;descrizione&quot; : &quot;NAPOLI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;80100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;NO&quot;,
    &quot;codiceISTAT&quot; : &quot;001003&quot;,
    &quot;descrizione&quot; : &quot;NOVARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;28100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;NU&quot;,
    &quot;codiceISTAT&quot; : &quot;020091&quot;,
    &quot;descrizione&quot; : &quot;NUORO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;08100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;OR&quot;,
    &quot;codiceISTAT&quot; : &quot;020095&quot;,
    &quot;descrizione&quot; : &quot;ORISTANO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;PA&quot;,
    &quot;codiceISTAT&quot; : &quot;019082&quot;,
    &quot;descrizione&quot; : &quot;PALERMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;90100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;PC&quot;,
    &quot;codiceISTAT&quot; : &quot;008033&quot;,
    &quot;descrizione&quot; : &quot;PIACENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;29100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;PD&quot;,
    &quot;codiceISTAT&quot; : &quot;005028&quot;,
    &quot;descrizione&quot; : &quot;PADOVA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;35100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;PE&quot;,
    &quot;codiceISTAT&quot; : &quot;013068&quot;,
    &quot;descrizione&quot; : &quot;PESCARA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;65100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;010&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;sigla&quot; : &quot;PG&quot;,
    &quot;codiceISTAT&quot; : &quot;010054&quot;,
    &quot;descrizione&quot; : &quot;PERUGIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;06100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PI&quot;,
    &quot;codiceISTAT&quot; : &quot;009050&quot;,
    &quot;descrizione&quot; : &quot;PISA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;56100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;PN&quot;,
    &quot;codiceISTAT&quot; : &quot;006093&quot;,
    &quot;descrizione&quot; : &quot;PORDENONE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;33170&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PO&quot;,
    &quot;codiceISTAT&quot; : &quot;009100&quot;,
    &quot;descrizione&quot; : &quot;PRATO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;59100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;PR&quot;,
    &quot;codiceISTAT&quot; : &quot;008034&quot;,
    &quot;descrizione&quot; : &quot;PARMA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;43100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;PT&quot;,
    &quot;codiceISTAT&quot; : &quot;009047&quot;,
    &quot;descrizione&quot; : &quot;PISTOIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;51100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;011&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;10&quot;,
    &quot;descrizioneRegione&quot; : &quot;MARCHE&quot;,
    &quot;sigla&quot; : &quot;PU&quot;,
    &quot;codiceISTAT&quot; : &quot;011041&quot;,
    &quot;descrizione&quot; : &quot;PESARO URBINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;61100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;PV&quot;,
    &quot;codiceISTAT&quot; : &quot;003018&quot;,
    &quot;descrizione&quot; : &quot;PAVIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;27100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;017&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;17&quot;,
    &quot;descrizioneRegione&quot; : &quot;BASILICATA&quot;,
    &quot;sigla&quot; : &quot;PZ&quot;,
    &quot;codiceISTAT&quot; : &quot;017076&quot;,
    &quot;descrizione&quot; : &quot;POTENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;85100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RA&quot;,
    &quot;codiceISTAT&quot; : &quot;008039&quot;,
    &quot;descrizione&quot; : &quot;RAVENNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;48100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;RC&quot;,
    &quot;codiceISTAT&quot; : &quot;018080&quot;,
    &quot;descrizione&quot; : &quot;REGGIO DI CALABRIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;89100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RE&quot;,
    &quot;codiceISTAT&quot; : &quot;008035&quot;,
    &quot;descrizione&quot; : &quot;REGGIO NELL&apos;EMILIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;42100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;RG&quot;,
    &quot;codiceISTAT&quot; : &quot;019088&quot;,
    &quot;descrizione&quot; : &quot;RAGUSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;97100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;RI&quot;,
    &quot;codiceISTAT&quot; : &quot;012057&quot;,
    &quot;descrizione&quot; : &quot;RIETI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;02100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;RM&quot;,
    &quot;codiceISTAT&quot; : &quot;012058&quot;,
    &quot;descrizione&quot; : &quot;ROMA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;00100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;008&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;08&quot;,
    &quot;descrizioneRegione&quot; : &quot;EMILIA ROMAGNA&quot;,
    &quot;sigla&quot; : &quot;RN&quot;,
    &quot;codiceISTAT&quot; : &quot;008099&quot;,
    &quot;descrizione&quot; : &quot;RIMINI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;47900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;RO&quot;,
    &quot;codiceISTAT&quot; : &quot;005029&quot;,
    &quot;descrizione&quot; : &quot;ROVIGO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;45100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;015&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;15&quot;,
    &quot;descrizioneRegione&quot; : &quot;CAMPANIA&quot;,
    &quot;sigla&quot; : &quot;SA&quot;,
    &quot;codiceISTAT&quot; : &quot;015065&quot;,
    &quot;descrizione&quot; : &quot;SALERNO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;84100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;009&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;09&quot;,
    &quot;descrizioneRegione&quot; : &quot;TOSCANA&quot;,
    &quot;sigla&quot; : &quot;SI&quot;,
    &quot;codiceISTAT&quot; : &quot;009052&quot;,
    &quot;descrizione&quot; : &quot;SIENA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;53100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;SO&quot;,
    &quot;codiceISTAT&quot; : &quot;003014&quot;,
    &quot;descrizione&quot; : &quot;SONDRIO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;23100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;SP&quot;,
    &quot;codiceISTAT&quot; : &quot;007011&quot;,
    &quot;descrizione&quot; : &quot;LA SPEZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;19100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;SR&quot;,
    &quot;codiceISTAT&quot; : &quot;019089&quot;,
    &quot;descrizione&quot; : &quot;SIRACUSA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;96100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;SS&quot;,
    &quot;codiceISTAT&quot; : &quot;020090&quot;,
    &quot;descrizione&quot; : &quot;SASSARI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;07100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;020&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;20&quot;,
    &quot;descrizioneRegione&quot; : &quot;SARDEGNA&quot;,
    &quot;sigla&quot; : &quot;SU&quot;,
    &quot;codiceISTAT&quot; : &quot;020111&quot;,
    &quot;descrizione&quot; : &quot;SUD SARDEGNA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;09013&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;007&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;03&quot;,
    &quot;descrizioneRegione&quot; : &quot;LIGURIA&quot;,
    &quot;sigla&quot; : &quot;SV&quot;,
    &quot;codiceISTAT&quot; : &quot;007009&quot;,
    &quot;descrizione&quot; : &quot;SAVONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;17100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;016&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;16&quot;,
    &quot;descrizioneRegione&quot; : &quot;PUGLIA&quot;,
    &quot;sigla&quot; : &quot;TA&quot;,
    &quot;codiceISTAT&quot; : &quot;016073&quot;,
    &quot;descrizione&quot; : &quot;TARANTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;74100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;013&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;14&quot;,
    &quot;descrizioneRegione&quot; : &quot;ABRUZZO&quot;,
    &quot;sigla&quot; : &quot;TE&quot;,
    &quot;codiceISTAT&quot; : &quot;013067&quot;,
    &quot;descrizione&quot; : &quot;TERAMO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;64100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;004&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;05&quot;,
    &quot;descrizioneRegione&quot; : &quot;TRENTINO ALTO ADIGE&quot;,
    &quot;sigla&quot; : &quot;TN&quot;,
    &quot;codiceISTAT&quot; : &quot;004022&quot;,
    &quot;descrizione&quot; : &quot;TRENTO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;38100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;TO&quot;,
    &quot;codiceISTAT&quot; : &quot;001001&quot;,
    &quot;descrizione&quot; : &quot;TORINO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;10100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;019&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;19&quot;,
    &quot;descrizioneRegione&quot; : &quot;SICILIA&quot;,
    &quot;sigla&quot; : &quot;TP&quot;,
    &quot;codiceISTAT&quot; : &quot;019081&quot;,
    &quot;descrizione&quot; : &quot;TRAPANI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;91100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;010&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;11&quot;,
    &quot;descrizioneRegione&quot; : &quot;UMBRIA&quot;,
    &quot;sigla&quot; : &quot;TR&quot;,
    &quot;codiceISTAT&quot; : &quot;010055&quot;,
    &quot;descrizione&quot; : &quot;TERNI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;05100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;TS&quot;,
    &quot;codiceISTAT&quot; : &quot;006032&quot;,
    &quot;descrizione&quot; : &quot;TRIESTE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;34100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;TV&quot;,
    &quot;codiceISTAT&quot; : &quot;005026&quot;,
    &quot;descrizione&quot; : &quot;TREVISO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;31100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;006&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;07&quot;,
    &quot;descrizioneRegione&quot; : &quot;FRIULI VENEZIA GIULIA&quot;,
    &quot;sigla&quot; : &quot;UD&quot;,
    &quot;codiceISTAT&quot; : &quot;006030&quot;,
    &quot;descrizione&quot; : &quot;UDINE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;33100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;003&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;04&quot;,
    &quot;descrizioneRegione&quot; : &quot;LOMBARDIA&quot;,
    &quot;sigla&quot; : &quot;VA&quot;,
    &quot;codiceISTAT&quot; : &quot;003012&quot;,
    &quot;descrizione&quot; : &quot;VARESE&quot;,
    &quot;codiceTerritoriale&quot; : &quot;21100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;VB&quot;,
    &quot;codiceISTAT&quot; : &quot;001103&quot;,
    &quot;descrizione&quot; : &quot;VERBANIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;28900&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;001&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;01&quot;,
    &quot;descrizioneRegione&quot; : &quot;PIEMONTE&quot;,
    &quot;sigla&quot; : &quot;VC&quot;,
    &quot;codiceISTAT&quot; : &quot;001002&quot;,
    &quot;descrizione&quot; : &quot;VERCELLI&quot;,
    &quot;codiceTerritoriale&quot; : &quot;13100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VE&quot;,
    &quot;codiceISTAT&quot; : &quot;005027&quot;,
    &quot;descrizione&quot; : &quot;VENEZIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;30100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VI&quot;,
    &quot;codiceISTAT&quot; : &quot;005024&quot;,
    &quot;descrizione&quot; : &quot;VICENZA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;36100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;005&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;06&quot;,
    &quot;descrizioneRegione&quot; : &quot;VENETO&quot;,
    &quot;sigla&quot; : &quot;VR&quot;,
    &quot;codiceISTAT&quot; : &quot;005023&quot;,
    &quot;descrizione&quot; : &quot;VERONA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;37100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;012&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;12&quot;,
    &quot;descrizioneRegione&quot; : &quot;LAZIO&quot;,
    &quot;sigla&quot; : &quot;VT&quot;,
    &quot;codiceISTAT&quot; : &quot;012056&quot;,
    &quot;descrizione&quot; : &quot;VITERBO&quot;,
    &quot;codiceTerritoriale&quot; : &quot;01100&quot;
  }, {
    &quot;codiceISTATRegione&quot; : &quot;018&quot;,
    &quot;codiceUnipolRegione&quot; : &quot;18&quot;,
    &quot;descrizioneRegione&quot; : &quot;CALABRIA&quot;,
    &quot;sigla&quot; : &quot;VV&quot;,
    &quot;codiceISTAT&quot; : &quot;018102&quot;,
    &quot;descrizione&quot; : &quot;VIBO VALENTIA&quot;,
    &quot;codiceTerritoriale&quot; : &quot;89900&quot;
  } ],
  &quot;id&quot; : 336,
  &quot;compagnia&quot; : &quot;unipolsai&quot;,
  &quot;ciu&quot; : 7174861,
  &quot;tipologiaSocietaFatca&quot; : &quot;0&quot;,
  &quot;residenzaFiscaleUSA&quot; : true,
  &quot;residenzaFiscale&quot; : true,
  &quot;cittadinanzaUSA&quot; : true,
  &quot;cittadinanze&quot; : [ ],
  &quot;poteriFirmaUSA&quot; : true,
  &quot;poteriFirma&quot; : true,
  &quot;potCittadinanzaUsa&quot; : false,
  &quot;tin&quot; : &quot;111111112&quot;,
  &quot;giin&quot; : null,
  &quot;indirizzo&quot; : {
    &quot;codiceBelfioreStato&quot; : &quot;F205&quot;,
    &quot;tipoIndirizzo&quot; : &quot;AMMI&quot;,
    &quot;codiceIstatAnag1&quot; : &quot;000000215&quot;,
    &quot;abbreviazioneProvincia&quot; : &quot;MI&quot;,
    &quot;codiceIstatComune&quot; : &quot;000215&quot;,
    &quot;codicePostale&quot; : &quot;20019&quot;,
    &quot;codiceBelfioreComune&quot; : &quot;F205&quot;,
    &quot;localitaBreve&quot; : null,
    &quot;localita&quot; : &quot;MILANO&quot;,
    &quot;dug&quot; : &quot;VIA&quot;,
    &quot;numeroCivico&quot; : &quot;20&quot;,
    &quot;dus&quot; : &quot;CAPPUCCINI&quot;,
    &quot;indirizzoCompleto&quot; : &quot;VIA CAPPUCCINI&quot;,
    &quot;tipoNormalizzato&quot; : &quot;N&quot;,
    &quot;indirizzoBreve&quot; : &quot;VIA CAPPUCCINI 20&quot;,
    &quot;presso&quot; : &quot;C/O BAR&quot;,
    &quot;longitudine&quot; : 9.204906,
    &quot;latitudine&quot; : 45.470623,
    &quot;cabStatoCod&quot; : null,
    &quot;distrettoCensuario&quot; : &quot;1&quot;,
    &quot;datiTracciatura&quot; : {
      &quot;dataCreazione&quot; : &quot;2025-02-26T12:58:58.980259&quot;,
      &quot;userIdCreazione&quot; : &quot;SFDC&quot;,
      &quot;usernameCreazione&quot; : &quot;DEFAULT DEFAULT&quot;,
      &quot;dataUltimoAggiornamento&quot; : null,
      &quot;userIdUltimoAggiornamento&quot; : null,
      &quot;usernameUltimoAggiornamento&quot; : null,
      &quot;canaleCreazione&quot; : &quot;A01&quot;,
      &quot;compagniaCreazione&quot; : &quot;1&quot;,
      &quot;sistemaCreazione&quot; : &quot;A17&quot;,
      &quot;sottosistemaCreazione&quot; : &quot;A0022&quot;,
      &quot;canaleAggiornamento&quot; : null,
      &quot;compagniaAggiornamento&quot; : null,
      &quot;sistemaAggiornamento&quot; : null,
      &quot;sottosistemaAggiornamento&quot; : null,
      &quot;applicazioneChiamante&quot; : &quot;PU&quot;,
      &quot;proceduraChiamante&quot; : &quot;PU00A&quot;
    },
    &quot;id&quot; : 5571782,
    &quot;compagnia&quot; : &quot;unipolsai&quot;,
    &quot;ciu&quot; : 7174861
  },
  &quot;residenzeFiscaliEstere&quot; : [ {
    &quot;residenzaFiscaleEstera&quot; : &quot;Z110&quot;,
    &quot;numeroIdentificazioneFiscale&quot; : &quot;11111111112&quot;
  } ]
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TrasformeResponsePG_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
