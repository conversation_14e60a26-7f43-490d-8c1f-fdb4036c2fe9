<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;request&quot;:[{&quot;activity&quot;:&quot;Trattativa&quot;,&quot;type&quot;:&quot;CallMeBack&quot;,&quot;need_scope&quot;:&quot;Tutti&quot;,&quot;rule_type&quot;:&quot;Generale&quot;},{&quot;activity&quot;:&quot;Trattativa&quot;,&quot;type&quot;:&quot;CallMeBack&quot;,&quot;need_scope&quot;:&quot;Tutti&quot;,&quot;rule_type&quot;:&quot;Generale&quot;},{&quot;activity&quot;:&quot;Trattativa&quot;,&quot;type&quot;:&quot;CallMeBack&quot;,&quot;need_scope&quot;:&quot;<PERSON><PERSON>&quot;,&quot;rule_type&quot;:&quot;Generale&quot;}]}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>English</language>
    <name>CC Motore SLA</name>
    <omniProcessElements>
        <description>Decision Matrix Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>decision_matrix</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;remoteOptions&quot; : {
    &quot;matrixName&quot; : &quot;Motore - CC - Gestione SLA&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;matrix Input Parameters&quot; : [ {
    &quot;value&quot; : &quot;activity&quot;,
    &quot;name&quot; : &quot;request:activity&quot;
  }, {
    &quot;value&quot; : &quot;need_scope&quot;,
    &quot;name&quot; : &quot;request:need_scope&quot;
  }, {
    &quot;value&quot; : &quot;rule_type&quot;,
    &quot;name&quot; : &quot;request:rule_type&quot;
  }, {
    &quot;value&quot; : &quot;type&quot;,
    &quot;name&quot; : &quot;request:type&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Matrix Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Response Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Response</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;decision_matrix&quot;,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessKey>CC_MotoreSla</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;deactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>MotoreSla</subType>
    <type>CC</type>
    <uniqueName>CC_MotoreSla_English_1</uniqueName>
    <versionNumber>1.0</versionNumber>
</OmniIntegrationProcedure>
