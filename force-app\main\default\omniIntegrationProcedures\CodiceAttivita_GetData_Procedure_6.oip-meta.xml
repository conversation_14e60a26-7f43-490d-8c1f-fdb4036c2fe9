<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;RecordId&quot;: &quot;a1i9X000007165oQAA&quot;,
    &quot;isOptionSetting&quot;: &quot;&quot;
}</customJavaScript>
    <description>Lorenzo Scrufari: 1287203 TER</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>CodiceAttivita</name>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ExtractCodActivity</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;CodiceAttivita&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;RecordId&quot;,
    &quot;element&quot; : &quot;RecordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetRAECodes</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;RAECodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoRae&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Debug&quot; : &quot;=\&quot;Sono nel ramo Individual\&quot;&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ExtractCodActivity&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Individual</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ExtractCodActivity:ClientType% == &apos;Individual&apos;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>17.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetActivityCodesEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ActivityCodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoTipoAttivita&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetAtecoCodesEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;atecoArray&quot; : &quot;=LIST(DESERIALIZE(&apos;{\&quot;codice\&quot;: \&quot;000\&quot;,\&quot;descrizione\&quot;: \&quot;SCONOSCIUTO\&quot;,\&quot;id\&quot;: \&quot;000\&quot;}&apos;))&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues18&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetSAECodesFiltered</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SAECodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeFilteredExt&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoSae&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetSummaryCodesEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SummaryCodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeExt&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoSintetico&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetOptionsEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : &quot;=%TemporarySAEValues%&quot;,
    &quot;ATECOValues&quot; : &quot;=LIST(ATECOValuesNotFormatted)&quot;,
    &quot;isOptionSetting&quot; : &quot;=&quot;,
    &quot;AtecoShort&quot; : &quot;&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformATECOCodesExt</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ATECOValuesNotFormatted&quot;,
  &quot;sendJSONPath&quot; : &quot;GetAtecoCodesEXT:atecoArray&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformRAECodesEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica:RAEValues&quot;,
  &quot;sendJSONPath&quot; : &quot;RAECodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformSAECodesEXT</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;TemporarySAEValues&quot;,
  &quot;sendJSONPath&quot; : &quot;SAECodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>isNotResidenceCountryItaly</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;=ExtractCodActivity:isResidenceCountryItaly == false&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetActivityCodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ActivityCodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoTipoAttivita&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetAtecoCodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ATECOCodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoBreve&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetSAECodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SAECodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoSae&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetSummaryCodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SummaryCodes&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetrieveAnagCodeDesc&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;fieldName&quot;,
    &quot;element&quot; : &quot;&apos;AtecoSintetico&apos;&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetOptions</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : &quot;=%TemporarySAEValues%&quot;,
    &quot;ATECOValues&quot; : &quot;=FILTER(LIST(FILTER(LIST(FILTER(LIST(FILTER(LIST(FILTER(LIST(%TemporaryATECOValues%), &apos;value != \&quot;990\&quot;&apos; )), &apos;value != \&quot;004\&quot;&apos; )), &apos;value != \&quot;003\&quot;&apos; )), &apos;value != \&quot;002\&quot;&apos; )), &apos;value != \&quot;001\&quot;&apos; )&quot;,
    &quot;SAEValuesProd&quot; : [ {
      &quot;value&quot; : &quot;614&quot;,
      &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;
    }, {
      &quot;value&quot; : &quot;615&quot;,
      &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;
    } ],
    &quot;SAEValuesCons&quot; : [ {
      &quot;value&quot; : &quot;600&quot;,
      &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
    } ],
    &quot;isOptionSetting&quot; : &quot;=&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformATECOCodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;TemporaryATECOValues&quot;,
  &quot;sendJSONPath&quot; : &quot;ATECOCodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformRAECodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica:RAEValues&quot;,
  &quot;sendJSONPath&quot; : &quot;RAECodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformSAECodes</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;TemporarySAEValues&quot;,
  &quot;sendJSONPath&quot; : &quot;SAECodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>isResidenceCountryItaly</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;=ExtractCodActivity:isResidenceCountryItaly == true&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Cons</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%isOptionSetting% == \&quot;Cons\&quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : [ {
      &quot;value&quot; : &quot;600&quot;,
      &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Prod</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%isOptionSetting% == \&quot;Prod\&quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%isOptionSetting% == \&quot;Prod\&quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : [ {
      &quot;value&quot; : &quot;614&quot;,
      &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;
    }, {
      &quot;value&quot; : &quot;615&quot;,
      &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction4</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;modifica&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ResponseForSAEValues</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;ISNOTBLANK(%isOptionSetting%)&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ReturnResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;ExtractCodActivity&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>18.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetATECOCodesBusiness</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ATECOValues&quot; : &quot;=%TemporaryATECOValues%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetSAECodesBusiness</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : &quot;=%TemporarySAEValues%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SAEBusiness</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;$Vlocity.TRUE&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetATECOCodesIndividual600</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ATECOValuesCons&quot; : [ {
      &quot;value&quot; : &quot;000&quot;,
      &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetATECOCodesIndividual601</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ATECOValuesProd&quot; : [ {
      &quot;value&quot; : &quot;001&quot;,
      &quot;label&quot; : &quot;001 - ONLUS&quot;
    }, {
      &quot;value&quot; : &quot;002&quot;,
      &quot;label&quot; : &quot;002 - CONDOMINI&quot;
    }, {
      &quot;value&quot; : &quot;003&quot;,
      &quot;label&quot; : &quot;003 - ENTE RELIGIOSO&quot;
    }, {
      &quot;value&quot; : &quot;004&quot;,
      &quot;label&quot; : &quot;004 - ALTRE ASSOCIAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;011&quot;,
      &quot;label&quot; : &quot;011 - Coltivazione di ortaggi e meloni. radici e tuberi&quot;
    }, {
      &quot;value&quot; : &quot;012&quot;,
      &quot;label&quot; : &quot;012 - Coltivazione di altri alberi da frutta. frutti di bosco e in guscio&quot;
    }, {
      &quot;value&quot; : &quot;013&quot;,
      &quot;label&quot; : &quot;013 - Riproduzione delle piante&quot;
    }, {
      &quot;value&quot; : &quot;014&quot;,
      &quot;label&quot; : &quot;014 - Allevamento di altri bovini e di bufalini&quot;
    }, {
      &quot;value&quot; : &quot;015&quot;,
      &quot;label&quot; : &quot;015 - Coltivazioni agricole associate allallevamento di animali: attività mista&quot;
    }, {
      &quot;value&quot; : &quot;016&quot;,
      &quot;label&quot; : &quot;016 - Attività di supporto alla produzione vegetale&quot;
    }, {
      &quot;value&quot; : &quot;017&quot;,
      &quot;label&quot; : &quot;017 - Caccia. cattura di animali e servizi connessi&quot;
    }, {
      &quot;value&quot; : &quot;021&quot;,
      &quot;label&quot; : &quot;021 - Silvicoltura ed altre attività forestali&quot;
    }, {
      &quot;value&quot; : &quot;022&quot;,
      &quot;label&quot; : &quot;022 - Utilizzo di aree forestali&quot;
    }, {
      &quot;value&quot; : &quot;023&quot;,
      &quot;label&quot; : &quot;023 - Raccolta di prodotti selvatici non legnosi&quot;
    }, {
      &quot;value&quot; : &quot;024&quot;,
      &quot;label&quot; : &quot;024 - Servizi di supporto per la silvicoltura&quot;
    }, {
      &quot;value&quot; : &quot;031&quot;,
      &quot;label&quot; : &quot;031 - Pesca marina&quot;
    }, {
      &quot;value&quot; : &quot;032&quot;,
      &quot;label&quot; : &quot;032 - Acquacoltura in acque dolci&quot;
    }, {
      &quot;value&quot; : &quot;051&quot;,
      &quot;label&quot; : &quot;051 - Estrazione di antracite&quot;
    }, {
      &quot;value&quot; : &quot;052&quot;,
      &quot;label&quot; : &quot;052 - Estrazione di lignite&quot;
    }, {
      &quot;value&quot; : &quot;061&quot;,
      &quot;label&quot; : &quot;061 - ESTRAZIONE DI PETROLIO GREGGIO&quot;
    }, {
      &quot;value&quot; : &quot;062&quot;,
      &quot;label&quot; : &quot;062 - Estrazione di gas naturale&quot;
    }, {
      &quot;value&quot; : &quot;071&quot;,
      &quot;label&quot; : &quot;071 - Estrazione di minerali metalliferi ferrosi&quot;
    }, {
      &quot;value&quot; : &quot;072&quot;,
      &quot;label&quot; : &quot;072 - ESTRAZIONE DI MINERALI METALLIFERI NON FERROSI&quot;
    }, {
      &quot;value&quot; : &quot;081&quot;,
      &quot;label&quot; : &quot;081 - Estrazione di pietre ornamentali e da costruzione. calcare. pietra da gesso. creta e ardesia&quot;
    }, {
      &quot;value&quot; : &quot;089&quot;,
      &quot;label&quot; : &quot;089 - ESTRAZIONE DI MINERALI DA CAVE E MINIERE NCA&quot;
    }, {
      &quot;value&quot; : &quot;091&quot;,
      &quot;label&quot; : &quot;091 - Attività di supporto allestrazione di petrolio e di gas naturale&quot;
    }, {
      &quot;value&quot; : &quot;099&quot;,
      &quot;label&quot; : &quot;099 - Attività di supporto per lestrazione da cave e miniere di altri minerali&quot;
    }, {
      &quot;value&quot; : &quot;101&quot;,
      &quot;label&quot; : &quot;101 - Lavorazione e conservazione di carne (escluso volatili)&quot;
    }, {
      &quot;value&quot; : &quot;102&quot;,
      &quot;label&quot; : &quot;102 - Lavorazione e conservazione di pesce. crostacei e molluschi&quot;
    }, {
      &quot;value&quot; : &quot;103&quot;,
      &quot;label&quot; : &quot;103 - Lavorazione e conservazione delle patate&quot;
    }, {
      &quot;value&quot; : &quot;104&quot;,
      &quot;label&quot; : &quot;104 - Produzione di margarina e di grassi commestibili simili&quot;
    }, {
      &quot;value&quot; : &quot;105&quot;,
      &quot;label&quot; : &quot;105 - Industria lattiero-casearia. trattamento igienico. conservazione del latte&quot;
    }, {
      &quot;value&quot; : &quot;106&quot;,
      &quot;label&quot; : &quot;106 - LAVORAZIONE DELLE GRANAGLIE. PRODUZIONE DI AMIDI E DI PRODOTTI AMIDACEI&quot;
    }, {
      &quot;value&quot; : &quot;107&quot;,
      &quot;label&quot; : &quot;107 - Produzione di fette biscottate e di biscotti&quot;
    }, {
      &quot;value&quot; : &quot;108&quot;,
      &quot;label&quot; : &quot;108 - Produzione di cacao. cioccolato. caramelle e confetterie&quot;
    }, {
      &quot;value&quot; : &quot;109&quot;,
      &quot;label&quot; : &quot;109 - PRODUZIONE DI PRODOTTI PER LALIMENTAZIONE DEGLI ANIMALI&quot;
    }, {
      &quot;value&quot; : &quot;110&quot;,
      &quot;label&quot; : &quot;110 - Produzione di vini da uve&quot;
    }, {
      &quot;value&quot; : &quot;120&quot;,
      &quot;label&quot; : &quot;120 - INDUSTRIA DEL TABACCO&quot;
    }, {
      &quot;value&quot; : &quot;131&quot;,
      &quot;label&quot; : &quot;131 - Preparazione e filatura di fibre tessili&quot;
    }, {
      &quot;value&quot; : &quot;132&quot;,
      &quot;label&quot; : &quot;132 - Tessitura&quot;
    }, {
      &quot;value&quot; : &quot;133&quot;,
      &quot;label&quot; : &quot;133 - Finissaggio dei tessili&quot;
    }, {
      &quot;value&quot; : &quot;139&quot;,
      &quot;label&quot; : &quot;139 - Fabbricazione di tessuti a maglia&quot;
    }, {
      &quot;value&quot; : &quot;141&quot;,
      &quot;label&quot; : &quot;141 - CONFEZIONE DI ARTICOLI DI ABBIGLIAMENTO (ESCLUSO ABBIGLIAMENTO IN PELLICCIA)&quot;
    }, {
      &quot;value&quot; : &quot;142&quot;,
      &quot;label&quot; : &quot;142 - Confezione di articoli in pelliccia&quot;
    }, {
      &quot;value&quot; : &quot;143&quot;,
      &quot;label&quot; : &quot;143 - Fabbricazione di altri articoli di maglieria&quot;
    }, {
      &quot;value&quot; : &quot;151&quot;,
      &quot;label&quot; : &quot;151 - Preparazione e concia del cuoio&quot;
    }, {
      &quot;value&quot; : &quot;152&quot;,
      &quot;label&quot; : &quot;152 - Fabbricazione di calzature&quot;
    }, {
      &quot;value&quot; : &quot;161&quot;,
      &quot;label&quot; : &quot;161 - Taglio e piallatura del legno&quot;
    }, {
      &quot;value&quot; : &quot;162&quot;,
      &quot;label&quot; : &quot;162 - Fabbricazione di altri prodotti in legno. sughero. paglia e materiali da intreccio&quot;
    }, {
      &quot;value&quot; : &quot;171&quot;,
      &quot;label&quot; : &quot;171 - FABBRICAZIONE DI PASTA-CARTA. CARTA E CARTONE&quot;
    }, {
      &quot;value&quot; : &quot;172&quot;,
      &quot;label&quot; : &quot;172 - FABBRICAZIONE DI ARTICOLI DI CARTA E CARTONE&quot;
    }, {
      &quot;value&quot; : &quot;181&quot;,
      &quot;label&quot; : &quot;181 - Stampa di giornali&quot;
    }, {
      &quot;value&quot; : &quot;182&quot;,
      &quot;label&quot; : &quot;182 - Riproduzione di supporti registrati&quot;
    }, {
      &quot;value&quot; : &quot;191&quot;,
      &quot;label&quot; : &quot;191 - Fabbricazione di prodotti di cokeria&quot;
    }, {
      &quot;value&quot; : &quot;192&quot;,
      &quot;label&quot; : &quot;192 - Fabbricazione di prodotti derivanti dalla raffinazione del petrolio&quot;
    }, {
      &quot;value&quot; : &quot;201&quot;,
      &quot;label&quot; : &quot;201 - Fabbricazione di fertilizzanti e composti azotati&quot;
    }, {
      &quot;value&quot; : &quot;202&quot;,
      &quot;label&quot; : &quot;202 - Fabbricazione di agrofarmaci e di altri prodotti chimici per lagricoltura&quot;
    }, {
      &quot;value&quot; : &quot;203&quot;,
      &quot;label&quot; : &quot;203 - Fabbricazione di pitture. vernici e smalti. inchiostri da stampa e adesivi sintetici (mastici)&quot;
    }, {
      &quot;value&quot; : &quot;204&quot;,
      &quot;label&quot; : &quot;204 - Fabbricazione di profumi e cosmetici&quot;
    }, {
      &quot;value&quot; : &quot;205&quot;,
      &quot;label&quot; : &quot;205 - Fabbricazione di prodotti chimici nca&quot;
    }, {
      &quot;value&quot; : &quot;206&quot;,
      &quot;label&quot; : &quot;206 - Fabbricazione di fibre sintetiche e artificiali&quot;
    }, {
      &quot;value&quot; : &quot;211&quot;,
      &quot;label&quot; : &quot;211 - Fabbricazione di prodotti farmaceutici di base&quot;
    }, {
      &quot;value&quot; : &quot;212&quot;,
      &quot;label&quot; : &quot;212 - Fabbricazione di medicinali e preparati farmaceutici&quot;
    }, {
      &quot;value&quot; : &quot;221&quot;,
      &quot;label&quot; : &quot;221 - FABBRICAZIONE DI ARTICOLI IN GOMMA&quot;
    }, {
      &quot;value&quot; : &quot;222&quot;,
      &quot;label&quot; : &quot;222 - Fabbricazione di altri articoli in materie plastiche&quot;
    }, {
      &quot;value&quot; : &quot;231&quot;,
      &quot;label&quot; : &quot;231 - Fabbricazione di vetro piano&quot;
    }, {
      &quot;value&quot; : &quot;232&quot;,
      &quot;label&quot; : &quot;232 - Fabbricazione di prodotti refrattari&quot;
    }, {
      &quot;value&quot; : &quot;233&quot;,
      &quot;label&quot; : &quot;233 - Fabbricazione di mattoni. tegole ed altri prodotti per ledilizia in terracotta&quot;
    }, {
      &quot;value&quot; : &quot;234&quot;,
      &quot;label&quot; : &quot;234 - Fabbricazione di altri prodotti in ceramica&quot;
    }, {
      &quot;value&quot; : &quot;235&quot;,
      &quot;label&quot; : &quot;235 - Produzione di cemento&quot;
    }, {
      &quot;value&quot; : &quot;236&quot;,
      &quot;label&quot; : &quot;236 - Fabbricazione di prodotti in fibrocemento&quot;
    }, {
      &quot;value&quot; : &quot;237&quot;,
      &quot;label&quot; : &quot;237 - Taglio. modellatura e finitura di pietre&quot;
    }, {
      &quot;value&quot; : &quot;239&quot;,
      &quot;label&quot; : &quot;239 - Produzione di prodotti abrasivi&quot;
    }, {
      &quot;value&quot; : &quot;241&quot;,
      &quot;label&quot; : &quot;241 - SIDERURGIA&quot;
    }, {
      &quot;value&quot; : &quot;242&quot;,
      &quot;label&quot; : &quot;242 - FABBRICAZIONE DI TUBI. CONDOTTI. PROFILATI CAVI E RELATIVI ACCESSORI IN ACCIAIO (ESCLUSI QUELLI IN ACCIAIO COLATO)&quot;
    }, {
      &quot;value&quot; : &quot;243&quot;,
      &quot;label&quot; : &quot;243 - Profilatura mediante formatura o piegatura a freddo&quot;
    }, {
      &quot;value&quot; : &quot;244&quot;,
      &quot;label&quot; : &quot;244 - Produzione di piombo. zinco e stagno e semilavorati&quot;
    }, {
      &quot;value&quot; : &quot;245&quot;,
      &quot;label&quot; : &quot;245 - Fusione di acciaio&quot;
    }, {
      &quot;value&quot; : &quot;251&quot;,
      &quot;label&quot; : &quot;251 - Fabbricazione di strutture metalliche e di parti di strutture&quot;
    }, {
      &quot;value&quot; : &quot;252&quot;,
      &quot;label&quot; : &quot;252 - Fabbricazione di radiatori e contenitori in metallo per caldaie per il riscaldamento centrale&quot;
    }, {
      &quot;value&quot; : &quot;253&quot;,
      &quot;label&quot; : &quot;253 - Fabbricazione di generatori di vapore (esclusi i contenitori in metallo per caldaie per il riscaldamento centrale ad acqua calda)&quot;
    }, {
      &quot;value&quot; : &quot;254&quot;,
      &quot;label&quot; : &quot;254 - FABBRICAZIONE DI ARMI E MUNIZIONI&quot;
    }, {
      &quot;value&quot; : &quot;255&quot;,
      &quot;label&quot; : &quot;255 - Fucinatura. imbutitura. stampaggio e profilatura dei metalli&quot;
    }, {
      &quot;value&quot; : &quot;256&quot;,
      &quot;label&quot; : &quot;256 - Lavori di meccanica generale&quot;
    }, {
      &quot;value&quot; : &quot;257&quot;,
      &quot;label&quot; : &quot;257 - FABBRICAZIONE DI ARTICOLI DI COLTELLERIA. UTENSILI E OGGETTI DI FERRAMENTA&quot;
    }, {
      &quot;value&quot; : &quot;259&quot;,
      &quot;label&quot; : &quot;259 - Fabbricazione di imballaggi leggeri in metallo&quot;
    }, {
      &quot;value&quot; : &quot;261&quot;,
      &quot;label&quot; : &quot;261 - Fabbricazione di schede elettroniche assemblate&quot;
    }, {
      &quot;value&quot; : &quot;262&quot;,
      &quot;label&quot; : &quot;262 - Fabbricazione di computer e unità periferiche&quot;
    }, {
      &quot;value&quot; : &quot;263&quot;,
      &quot;label&quot; : &quot;263 - Fabbricazione di apparecchiature per le telecomunicazioni&quot;
    }, {
      &quot;value&quot; : &quot;264&quot;,
      &quot;label&quot; : &quot;264 - Fabbricazione di prodotti di elettronica di consumo audio e video&quot;
    }, {
      &quot;value&quot; : &quot;265&quot;,
      &quot;label&quot; : &quot;265 - Fabbricazione di strumenti e apparecchi di misurazione. prova e navigazione (esclusi quelli ottici)&quot;
    }, {
      &quot;value&quot; : &quot;266&quot;,
      &quot;label&quot; : &quot;266 - Fabbricazione di strumenti per irradiazione. apparecchiature elettromedicali ed elettroterapeutiche&quot;
    }, {
      &quot;value&quot; : &quot;267&quot;,
      &quot;label&quot; : &quot;267 - Fabbricazione di strumenti ottici e attrezzature fotografiche&quot;
    }, {
      &quot;value&quot; : &quot;268&quot;,
      &quot;label&quot; : &quot;268 - Fabbricazione di supporti magnetici ed ottici&quot;
    }, {
      &quot;value&quot; : &quot;271&quot;,
      &quot;label&quot; : &quot;271 - FABBRICAZIONE DI MOTORI. GENERATORI E TRASFORMATORI ELETTRICI E DI APPARECCHIATURE PER LA DISTRIBUZIONE E IL CONTROLLO DELLELETTRICITÀ&quot;
    }, {
      &quot;value&quot; : &quot;272&quot;,
      &quot;label&quot; : &quot;272 - Fabbricazione di batterie di pile ed accumulatori elettrici&quot;
    }, {
      &quot;value&quot; : &quot;273&quot;,
      &quot;label&quot; : &quot;273 - Fabbricazione di attrezzature per cablaggio&quot;
    }, {
      &quot;value&quot; : &quot;274&quot;,
      &quot;label&quot; : &quot;274 - Fabbricazione di apparecchiature per illuminazione&quot;
    }, {
      &quot;value&quot; : &quot;275&quot;,
      &quot;label&quot; : &quot;275 - FABBRICAZIONE DI APPARECCHI PER USO DOMESTICO&quot;
    }, {
      &quot;value&quot; : &quot;279&quot;,
      &quot;label&quot; : &quot;279 - Fabbricazione di altre apparecchiature elettriche&quot;
    }, {
      &quot;value&quot; : &quot;281&quot;,
      &quot;label&quot; : &quot;281 - Fabbricazione di motori e turbine (esclusi i motori per aeromobili. veicoli e motocicli)&quot;
    }, {
      &quot;value&quot; : &quot;282&quot;,
      &quot;label&quot; : &quot;282 - FABBRICAZIONE DI ALTRE MACCHINE DI IMPIEGO GENERALE&quot;
    }, {
      &quot;value&quot; : &quot;283&quot;,
      &quot;label&quot; : &quot;283 - Fabbricazione di macchine per lagricoltura e la silvicoltura&quot;
    }, {
      &quot;value&quot; : &quot;284&quot;,
      &quot;label&quot; : &quot;284 - Fabbricazione di macchine utensili per la formatura dei metalli&quot;
    }, {
      &quot;value&quot; : &quot;289&quot;,
      &quot;label&quot; : &quot;289 - Fabbricazione di macchine per lindustria alimentare. delle bevande e del tabacco&quot;
    }, {
      &quot;value&quot; : &quot;291&quot;,
      &quot;label&quot; : &quot;291 - Fabbricazione di autoveicoli&quot;
    }, {
      &quot;value&quot; : &quot;292&quot;,
      &quot;label&quot; : &quot;292 - Fabbricazione di carrozzerie per autoveicoli. rimorchi e semirimorchi&quot;
    }, {
      &quot;value&quot; : &quot;293&quot;,
      &quot;label&quot; : &quot;293 - Fabbricazione di apparecchiature elettriche ed elettroniche per autoveicoli e loro motori&quot;
    }, {
      &quot;value&quot; : &quot;301&quot;,
      &quot;label&quot; : &quot;301 - Costruzione di navi e di strutture galleggianti&quot;
    }, {
      &quot;value&quot; : &quot;302&quot;,
      &quot;label&quot; : &quot;302 - COSTRUZIONE DI LOCOMOTIVE E DI MATERIALE ROTABILE FERRO-TRANVIARIO&quot;
    }, {
      &quot;value&quot; : &quot;303&quot;,
      &quot;label&quot; : &quot;303 - Fabbricazione di aeromobili. di veicoli spaziali e dei relativi dispositivi&quot;
    }, {
      &quot;value&quot; : &quot;304&quot;,
      &quot;label&quot; : &quot;304 - Fabbricazione di veicoli militari da combattimento&quot;
    }, {
      &quot;value&quot; : &quot;309&quot;,
      &quot;label&quot; : &quot;309 - FABBRICAZIONE DI MEZZI DI TRASPORTO NCA&quot;
    }, {
      &quot;value&quot; : &quot;310&quot;,
      &quot;label&quot; : &quot;310 - Fabbricazione di materassi&quot;
    }, {
      &quot;value&quot; : &quot;321&quot;,
      &quot;label&quot; : &quot;321 - Coniazione di monete&quot;
    }, {
      &quot;value&quot; : &quot;322&quot;,
      &quot;label&quot; : &quot;322 - Fabbricazione di strumenti musicali&quot;
    }, {
      &quot;value&quot; : &quot;323&quot;,
      &quot;label&quot; : &quot;323 - Fabbricazione di articoli sportivi&quot;
    }, {
      &quot;value&quot; : &quot;324&quot;,
      &quot;label&quot; : &quot;324 - FABBRICAZIONE DI GIOCHI E GIOCATTOLI&quot;
    }, {
      &quot;value&quot; : &quot;325&quot;,
      &quot;label&quot; : &quot;325 - FABBRICAZIONE DI STRUMENTI E FORNITURE MEDICHE E DENTISTICHE&quot;
    }, {
      &quot;value&quot; : &quot;329&quot;,
      &quot;label&quot; : &quot;329 - Altre industrie manifatturiere nca&quot;
    }, {
      &quot;value&quot; : &quot;331&quot;,
      &quot;label&quot; : &quot;331 - Riparazione e manutenzione di locomotive e di materiale rotabile ferro-tranviario (esclusi i loro motori)&quot;
    }, {
      &quot;value&quot; : &quot;332&quot;,
      &quot;label&quot; : &quot;332 - Installazione di macchine ed apparecchiature industriali&quot;
    }, {
      &quot;value&quot; : &quot;351&quot;,
      &quot;label&quot; : &quot;351 - Commercio di energia elettrica&quot;
    }, {
      &quot;value&quot; : &quot;352&quot;,
      &quot;label&quot; : &quot;352 - Distribuzione di combustibili gassosi mediante condotte&quot;
    }, {
      &quot;value&quot; : &quot;353&quot;,
      &quot;label&quot; : &quot;353 - Fornitura di vapore e aria condizionata&quot;
    }, {
      &quot;value&quot; : &quot;360&quot;,
      &quot;label&quot; : &quot;360 - RACCOLTA, TRATTAMENTO E FORNITURA DI ACQUA&quot;
    }, {
      &quot;value&quot; : &quot;370&quot;,
      &quot;label&quot; : &quot;370 - GESTIONE DELLE RETI FOGNARIE&quot;
    }, {
      &quot;value&quot; : &quot;381&quot;,
      &quot;label&quot; : &quot;381 - RACCOLTA DEI RIFIUTI&quot;
    }, {
      &quot;value&quot; : &quot;382&quot;,
      &quot;label&quot; : &quot;382 - Trattamento e smaltimento di rifiuti non pericolosi&quot;
    }, {
      &quot;value&quot; : &quot;383&quot;,
      &quot;label&quot; : &quot;383 - RECUPERO DEI MATERIALI&quot;
    }, {
      &quot;value&quot; : &quot;390&quot;,
      &quot;label&quot; : &quot;390 - ATTIVITÀ DI RISANAMENTO E ALTRI SERVIZI DI GESTIONE DEI RIFIUTI&quot;
    }, {
      &quot;value&quot; : &quot;411&quot;,
      &quot;label&quot; : &quot;411 - SVILUPPO DI PROGETTI IMMOBILIARI&quot;
    }, {
      &quot;value&quot; : &quot;412&quot;,
      &quot;label&quot; : &quot;412 - Costruzione di edifici residenziali e non residenziali&quot;
    }, {
      &quot;value&quot; : &quot;421&quot;,
      &quot;label&quot; : &quot;421 - Costruzione di strade e autostrade&quot;
    }, {
      &quot;value&quot; : &quot;422&quot;,
      &quot;label&quot; : &quot;422 - Costruzione di opere di pubblica utilità per il trasporto di fluidi&quot;
    }, {
      &quot;value&quot; : &quot;429&quot;,
      &quot;label&quot; : &quot;429 - Costruzione di opere idrauliche&quot;
    }, {
      &quot;value&quot; : &quot;431&quot;,
      &quot;label&quot; : &quot;431 - DemOLIZIONE E PREPARAZIONE DEL CANTIERE EDILE&quot;
    }, {
      &quot;value&quot; : &quot;432&quot;,
      &quot;label&quot; : &quot;432 - Installazione di impianti elettrici&quot;
    }, {
      &quot;value&quot; : &quot;433&quot;,
      &quot;label&quot; : &quot;433 - Altri lavori di completamento e di finitura degli edifici&quot;
    }, {
      &quot;value&quot; : &quot;439&quot;,
      &quot;label&quot; : &quot;439 - ALTRI LAVORI SPECIALIZZATI DI COSTRUZIONE&quot;
    }, {
      &quot;value&quot; : &quot;451&quot;,
      &quot;label&quot; : &quot;451 - Commercio di autovetture e di autoveicoli leggeri&quot;
    }, {
      &quot;value&quot; : &quot;452&quot;,
      &quot;label&quot; : &quot;452 - Manutenzione e riparazione di autoveicoli&quot;
    }, {
      &quot;value&quot; : &quot;453&quot;,
      &quot;label&quot; : &quot;453 - COMMERCIO DI PARTI E ACCESSORI DI AUTOVEICOLI&quot;
    }, {
      &quot;value&quot; : &quot;454&quot;,
      &quot;label&quot; : &quot;454 - Commercio. manutenzione e riparazione di motocicli e relative parti ed accessori&quot;
    }, {
      &quot;value&quot; : &quot;461&quot;,
      &quot;label&quot; : &quot;461 - Intermediari del commercio di prodotti tessili. abbigliamento. pellicce. calzature e articoli in pelle&quot;
    }, {
      &quot;value&quot; : &quot;462&quot;,
      &quot;label&quot; : &quot;462 - Commercio allingrosso di cereali. tabacco grezzo. sementi e alimenti per il bestiame (mangimi)&quot;
    }, {
      &quot;value&quot; : &quot;463&quot;,
      &quot;label&quot; : &quot;463 - Commercio allingrosso non specializzato di prodotti alimentari. bevande e tabacco&quot;
    }, {
      &quot;value&quot; : &quot;464&quot;,
      &quot;label&quot; : &quot;464 - Commercio allingrosso di orologi e di gioielleria&quot;
    }, {
      &quot;value&quot; : &quot;465&quot;,
      &quot;label&quot; : &quot;465 - Commercio allingrosso di computer. apparecchiature informatiche periferiche e di software&quot;
    }, {
      &quot;value&quot; : &quot;466&quot;,
      &quot;label&quot; : &quot;466 - Commercio allingrosso di macchinari per lindustria tessile. di macchine per cucire e per maglieria&quot;
    }, {
      &quot;value&quot; : &quot;467&quot;,
      &quot;label&quot; : &quot;467 - Commercio allingrosso di combustibili solidi. liquidi. gassosi e di prodotti derivati&quot;
    }, {
      &quot;value&quot; : &quot;469&quot;,
      &quot;label&quot; : &quot;469 - Commercio allingrosso non specializzato&quot;
    }, {
      &quot;value&quot; : &quot;471&quot;,
      &quot;label&quot; : &quot;471 - Commercio al dettaglio in altri esercizi non specializzati&quot;
    }, {
      &quot;value&quot; : &quot;472&quot;,
      &quot;label&quot; : &quot;472 - Commercio al dettaglio di bevande in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;473&quot;,
      &quot;label&quot; : &quot;473 - Commercio al dettaglio di carburante per autotrazione in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;474&quot;,
      &quot;label&quot; : &quot;474 - COMMERCIO AL DETTAGLIO DI APPARECCHIATURE INFORMATICHE E PER LE TELECOMUNICAZIONI (ICT) IN ESERCIZI SPECIALIZZATI&quot;
    }, {
      &quot;value&quot; : &quot;475&quot;,
      &quot;label&quot; : &quot;475 - Commercio al dettaglio di ferramenta. vernici. vetro piano e materiali da costruzione in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;476&quot;,
      &quot;label&quot; : &quot;476 - Commercio al dettaglio di libri in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;477&quot;,
      &quot;label&quot; : &quot;477 - Commercio al dettaglio di articoli di seconda mano in negozi&quot;
    }, {
      &quot;value&quot; : &quot;478&quot;,
      &quot;label&quot; : &quot;478 - COMMERCIO AL DETTAGLIO AMBULANTE&quot;
    }, {
      &quot;value&quot; : &quot;479&quot;,
      &quot;label&quot; : &quot;479 - Altro commercio al dettaglio al di fuori di negozi. banchi o mercati&quot;
    }, {
      &quot;value&quot; : &quot;491&quot;,
      &quot;label&quot; : &quot;491 - Trasporto ferroviario di passeggeri (interurbano)&quot;
    }, {
      &quot;value&quot; : &quot;492&quot;,
      &quot;label&quot; : &quot;492 - Trasporto ferroviario di merci&quot;
    }, {
      &quot;value&quot; : &quot;493&quot;,
      &quot;label&quot; : &quot;493 - ALTRI TRASPORTI TERRESTRI DI PASSEGGERI&quot;
    }, {
      &quot;value&quot; : &quot;494&quot;,
      &quot;label&quot; : &quot;494 - Trasporto di merci su strada&quot;
    }, {
      &quot;value&quot; : &quot;495&quot;,
      &quot;label&quot; : &quot;495 - Trasporto mediante condotte&quot;
    }, {
      &quot;value&quot; : &quot;501&quot;,
      &quot;label&quot; : &quot;501 - Trasporto marittimo e costiero di passeggeri&quot;
    }, {
      &quot;value&quot; : &quot;502&quot;,
      &quot;label&quot; : &quot;502 - Trasporto marittimo e costiero di merci&quot;
    }, {
      &quot;value&quot; : &quot;503&quot;,
      &quot;label&quot; : &quot;503 - Trasporto di passeggeri per vie dacqua interne&quot;
    }, {
      &quot;value&quot; : &quot;504&quot;,
      &quot;label&quot; : &quot;504 - Trasporto di merci per vie dacqua interne&quot;
    }, {
      &quot;value&quot; : &quot;511&quot;,
      &quot;label&quot; : &quot;511 - Trasporto aereo di passeggeri&quot;
    }, {
      &quot;value&quot; : &quot;512&quot;,
      &quot;label&quot; : &quot;512 - Trasporto spaziale&quot;
    }, {
      &quot;value&quot; : &quot;521&quot;,
      &quot;label&quot; : &quot;521 - Magazzinaggio e custodia&quot;
    }, {
      &quot;value&quot; : &quot;522&quot;,
      &quot;label&quot; : &quot;522 - Attività dei servizi connessi al trasporto aereo&quot;
    }, {
      &quot;value&quot; : &quot;531&quot;,
      &quot;label&quot; : &quot;531 - Attività postali con obbligo di servizio universale&quot;
    }, {
      &quot;value&quot; : &quot;532&quot;,
      &quot;label&quot; : &quot;532 - Altre attività postali e di corriere senza obbligo di servizio universale&quot;
    }, {
      &quot;value&quot; : &quot;551&quot;,
      &quot;label&quot; : &quot;551 - Alberghi e strutture simili&quot;
    }, {
      &quot;value&quot; : &quot;552&quot;,
      &quot;label&quot; : &quot;552 - Alloggi per vacanze e altre strutture per brevi soggiorni&quot;
    }, {
      &quot;value&quot; : &quot;553&quot;,
      &quot;label&quot; : &quot;553 - Aree di campeggio e aree attrezzate per camper e roulotte&quot;
    }, {
      &quot;value&quot; : &quot;559&quot;,
      &quot;label&quot; : &quot;559 - Altri alloggi&quot;
    }, {
      &quot;value&quot; : &quot;561&quot;,
      &quot;label&quot; : &quot;561 - Ristoranti e attività di ristorazione mobile&quot;
    }, {
      &quot;value&quot; : &quot;562&quot;,
      &quot;label&quot; : &quot;562 - Mense e catering continuativo su base contrattuale&quot;
    }, {
      &quot;value&quot; : &quot;563&quot;,
      &quot;label&quot; : &quot;563 - Bar e altri esercizi simili senza cucina&quot;
    }, {
      &quot;value&quot; : &quot;581&quot;,
      &quot;label&quot; : &quot;581 - Edizione di quotidiani&quot;
    }, {
      &quot;value&quot; : &quot;582&quot;,
      &quot;label&quot; : &quot;582 - Edizione di altri software&quot;
    }, {
      &quot;value&quot; : &quot;591&quot;,
      &quot;label&quot; : &quot;591 - Attività di post-produzione cinematografica. di video e di programmi televisivi&quot;
    }, {
      &quot;value&quot; : &quot;592&quot;,
      &quot;label&quot; : &quot;592 - Attività di registrazione sonora e di editoria musicale&quot;
    }, {
      &quot;value&quot; : &quot;601&quot;,
      &quot;label&quot; : &quot;601 - Trasmissioni radiofoniche&quot;
    }, {
      &quot;value&quot; : &quot;602&quot;,
      &quot;label&quot; : &quot;602 - Attività di programmazione e trasmissioni televisive&quot;
    }, {
      &quot;value&quot; : &quot;611&quot;,
      &quot;label&quot; : &quot;611 - TELECOMUNICAZIONI FISSE&quot;
    }, {
      &quot;value&quot; : &quot;612&quot;,
      &quot;label&quot; : &quot;612 - Telecomunicazioni mobili&quot;
    }, {
      &quot;value&quot; : &quot;613&quot;,
      &quot;label&quot; : &quot;613 - Telecomunicazioni satellitari&quot;
    }, {
      &quot;value&quot; : &quot;619&quot;,
      &quot;label&quot; : &quot;619 - Altre attività di telecomunicazione&quot;
    }, {
      &quot;value&quot; : &quot;620&quot;,
      &quot;label&quot; : &quot;620 - Consulenza nel settore delle tecnologie dellinformatica&quot;
    }, {
      &quot;value&quot; : &quot;631&quot;,
      &quot;label&quot; : &quot;631 - ELABORAZIONE DEI DATI. HOSTING E ATTIVITÀ CONNESSE&quot;
    }, {
      &quot;value&quot; : &quot;639&quot;,
      &quot;label&quot; : &quot;639 - Altre attività dei servizi di informazione nca&quot;
    }, {
      &quot;value&quot; : &quot;641&quot;,
      &quot;label&quot; : &quot;641 - Attività delle banche centrali&quot;
    }, {
      &quot;value&quot; : &quot;642&quot;,
      &quot;label&quot; : &quot;642 - ATTIVITÀ DELLE SOCIETÀ DI PARTECIPAZIONE (HOLDING)&quot;
    }, {
      &quot;value&quot; : &quot;643&quot;,
      &quot;label&quot; : &quot;643 - Società fiduciarie. fondi e altre società simili&quot;
    }, {
      &quot;value&quot; : &quot;649&quot;,
      &quot;label&quot; : &quot;649 - Altre attività creditizie&quot;
    }, {
      &quot;value&quot; : &quot;651&quot;,
      &quot;label&quot; : &quot;651 - Assicurazioni diverse da quelle sulla vita&quot;
    }, {
      &quot;value&quot; : &quot;652&quot;,
      &quot;label&quot; : &quot;652 - RIASSICURAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;653&quot;,
      &quot;label&quot; : &quot;653 - Fondi pensione&quot;
    }, {
      &quot;value&quot; : &quot;661&quot;,
      &quot;label&quot; : &quot;661 - Altre attività ausiliarie dei servizi finanziari (escluse le assicurazioni e i fondi pensione)&quot;
    }, {
      &quot;value&quot; : &quot;662&quot;,
      &quot;label&quot; : &quot;662 - ATTIVITÀ AUSILIARIE DELLE ASSICURAZIONI E DEI FONDI PENSIONE&quot;
    }, {
      &quot;value&quot; : &quot;663&quot;,
      &quot;label&quot; : &quot;663 - Attività di gestione dei fondi&quot;
    }, {
      &quot;value&quot; : &quot;681&quot;,
      &quot;label&quot; : &quot;681 - Compravendita di beni immobili effettuata su beni propri&quot;
    }, {
      &quot;value&quot; : &quot;682&quot;,
      &quot;label&quot; : &quot;682 - Affitto e gestione di immobili di proprietà o in leasing&quot;
    }, {
      &quot;value&quot; : &quot;683&quot;,
      &quot;label&quot; : &quot;683 - Gestione di immobili per conto terzi&quot;
    }, {
      &quot;value&quot; : &quot;691&quot;,
      &quot;label&quot; : &quot;691 - Attività degli studi legali e notarili&quot;
    }, {
      &quot;value&quot; : &quot;692&quot;,
      &quot;label&quot; : &quot;692 - Contabilità. controllo e revisione contabile. consulenza in materia fiscale e del lavoro&quot;
    }, {
      &quot;value&quot; : &quot;701&quot;,
      &quot;label&quot; : &quot;701 - Attività delle holding impegnate nelle attività gestionali (holding operative)&quot;
    }, {
      &quot;value&quot; : &quot;702&quot;,
      &quot;label&quot; : &quot;702 - ATTIVITÀ DI CONSULENZA GESTIONALE&quot;
    }, {
      &quot;value&quot; : &quot;711&quot;,
      &quot;label&quot; : &quot;711 - ATTIVITÀ DEGLI STUDI DI ARCHITETTURA. INGEGNERIA ED ALTRI STUDI TECNICI&quot;
    }, {
      &quot;value&quot; : &quot;712&quot;,
      &quot;label&quot; : &quot;712 - Collaudi ed analisi tecniche&quot;
    }, {
      &quot;value&quot; : &quot;721&quot;,
      &quot;label&quot; : &quot;721 - Altre attività di ricerca e sviluppo sperimentale nel campo delle scienze naturali e dellingegneria&quot;
    }, {
      &quot;value&quot; : &quot;722&quot;,
      &quot;label&quot; : &quot;722 - Ricerca e sviluppo sperimentale nel campo delle scienze sociali e umanistiche&quot;
    }, {
      &quot;value&quot; : &quot;731&quot;,
      &quot;label&quot; : &quot;731 - Attività delle concessionarie e degli altri intermediari di servizi pubblicitari&quot;
    }, {
      &quot;value&quot; : &quot;732&quot;,
      &quot;label&quot; : &quot;732 - RICERCHE DI MERCATO E SONDAGGI DI OPINIONE&quot;
    }, {
      &quot;value&quot; : &quot;741&quot;,
      &quot;label&quot; : &quot;741 - Attività di design specializzate&quot;
    }, {
      &quot;value&quot; : &quot;742&quot;,
      &quot;label&quot; : &quot;742 - Attività fotografiche&quot;
    }, {
      &quot;value&quot; : &quot;743&quot;,
      &quot;label&quot; : &quot;743 - Traduzione e interpretariato&quot;
    }, {
      &quot;value&quot; : &quot;749&quot;,
      &quot;label&quot; : &quot;749 - Altre attività professionali. scientifiche e tecniche nca&quot;
    }, {
      &quot;value&quot; : &quot;750&quot;,
      &quot;label&quot; : &quot;750 - SERVIZI VETERINARI&quot;
    }, {
      &quot;value&quot; : &quot;771&quot;,
      &quot;label&quot; : &quot;771 - NOLEGGIO DI AUTOVEICOLI&quot;
    }, {
      &quot;value&quot; : &quot;772&quot;,
      &quot;label&quot; : &quot;772 - Noleggio di altri beni per uso personale e domestico (escluse le attrezzature sportive e ricreative)&quot;
    }, {
      &quot;value&quot; : &quot;773&quot;,
      &quot;label&quot; : &quot;773 - Noleggio di macchine e attrezzature agricole&quot;
    }, {
      &quot;value&quot; : &quot;774&quot;,
      &quot;label&quot; : &quot;774 - Concessione dei diritti di sfruttamento di proprietà intellettuale e prodotti simili (escluse le opere protette dal copyright)&quot;
    }, {
      &quot;value&quot; : &quot;781&quot;,
      &quot;label&quot; : &quot;781 - ATTIVITÀ DI AGENZIE DI COLLOCAMENTO&quot;
    }, {
      &quot;value&quot; : &quot;782&quot;,
      &quot;label&quot; : &quot;782 - Attività delle agenzie di lavoro temporaneo (interinale)&quot;
    }, {
      &quot;value&quot; : &quot;783&quot;,
      &quot;label&quot; : &quot;783 - Altre attività di fornitura e gestione di risorse umane&quot;
    }, {
      &quot;value&quot; : &quot;791&quot;,
      &quot;label&quot; : &quot;791 - ATTIVITÀ DELLE AGENZIE DI VIAGGIO E DEI TOUR OPERATOR&quot;
    }, {
      &quot;value&quot; : &quot;799&quot;,
      &quot;label&quot; : &quot;799 - Altri servizi di prenotazione e altre attività di assistenza turistica non svolte dalle agenzie di viaggio&quot;
    }, {
      &quot;value&quot; : &quot;801&quot;,
      &quot;label&quot; : &quot;801 - Servizi di vigilanza privata&quot;
    }, {
      &quot;value&quot; : &quot;802&quot;,
      &quot;label&quot; : &quot;802 - Servizi connessi ai sistemi di vigilanza&quot;
    }, {
      &quot;value&quot; : &quot;803&quot;,
      &quot;label&quot; : &quot;803 - Servizi investigativi privati&quot;
    }, {
      &quot;value&quot; : &quot;811&quot;,
      &quot;label&quot; : &quot;811 - Servizi integrati di gestione agli edifici&quot;
    }, {
      &quot;value&quot; : &quot;812&quot;,
      &quot;label&quot; : &quot;812 - Pulizia generale (non specializzata) di edifici&quot;
    }, {
      &quot;value&quot; : &quot;813&quot;,
      &quot;label&quot; : &quot;813 - Cura e manutenzione del paesaggio&quot;
    }, {
      &quot;value&quot; : &quot;821&quot;,
      &quot;label&quot; : &quot;821 - Servizi integrati di supporto per le funzioni dufficio&quot;
    }, {
      &quot;value&quot; : &quot;822&quot;,
      &quot;label&quot; : &quot;822 - Attività dei call center&quot;
    }, {
      &quot;value&quot; : &quot;823&quot;,
      &quot;label&quot; : &quot;823 - ORGANIZZAZIONE DI CONVEGNI E FIERE&quot;
    }, {
      &quot;value&quot; : &quot;829&quot;,
      &quot;label&quot; : &quot;829 - Attività di imballaggio e confezionamento per conto terzi&quot;
    }, {
      &quot;value&quot; : &quot;841&quot;,
      &quot;label&quot; : &quot;841 - Regolamentazione delle attività relative alla fornitura di servizi di assistenza sanitaria. dellistruzione. di servizi culturali e ad altri servizi sociali (esclusa lassicurazione sociale obbligatoria)&quot;
    }, {
      &quot;value&quot; : &quot;842&quot;,
      &quot;label&quot; : &quot;842 - Affari esteri&quot;
    }, {
      &quot;value&quot; : &quot;843&quot;,
      &quot;label&quot; : &quot;843 - Assicurazione sociale obbligatoria&quot;
    }, {
      &quot;value&quot; : &quot;851&quot;,
      &quot;label&quot; : &quot;851 - Istruzione prescolastica&quot;
    }, {
      &quot;value&quot; : &quot;852&quot;,
      &quot;label&quot; : &quot;852 - Istruzione primaria&quot;
    }, {
      &quot;value&quot; : &quot;853&quot;,
      &quot;label&quot; : &quot;853 - ISTRUZIONE SECONDARIA&quot;
    }, {
      &quot;value&quot; : &quot;854&quot;,
      &quot;label&quot; : &quot;854 - ISTRUZIONE POST-SECONDARIA UNIVERSITARIA E NON UNIVERSITARIA&quot;
    }, {
      &quot;value&quot; : &quot;855&quot;,
      &quot;label&quot; : &quot;855 - Attività delle scuole guida&quot;
    }, {
      &quot;value&quot; : &quot;856&quot;,
      &quot;label&quot; : &quot;856 - Attività di supporto allistruzione&quot;
    }, {
      &quot;value&quot; : &quot;861&quot;,
      &quot;label&quot; : &quot;861 - Servizi ospedalieri&quot;
    }, {
      &quot;value&quot; : &quot;862&quot;,
      &quot;label&quot; : &quot;862 - Servizi degli studi medici di medicina generale&quot;
    }, {
      &quot;value&quot; : &quot;869&quot;,
      &quot;label&quot; : &quot;869 - Altri servizi di assistenza sanitaria&quot;
    }, {
      &quot;value&quot; : &quot;871&quot;,
      &quot;label&quot; : &quot;871 - Strutture di assistenza infermieristica residenziale&quot;
    }, {
      &quot;value&quot; : &quot;872&quot;,
      &quot;label&quot; : &quot;872 - Strutture di assistenza residenziale per persone affette da ritardi mentali. disturbi mentali o che abusano di sostanze stupefacenti&quot;
    }, {
      &quot;value&quot; : &quot;873&quot;,
      &quot;label&quot; : &quot;873 - Strutture di assistenza residenziale per anziani e disabili&quot;
    }, {
      &quot;value&quot; : &quot;879&quot;,
      &quot;label&quot; : &quot;879 - Altre strutture di assistenza sociale residenziale&quot;
    }, {
      &quot;value&quot; : &quot;881&quot;,
      &quot;label&quot; : &quot;881 - Assistenza sociale non residenziale per anziani e disabili&quot;
    }, {
      &quot;value&quot; : &quot;889&quot;,
      &quot;label&quot; : &quot;889 - ALTRE ATTIVITÀ DI ASSISTENZA SOCIALE NON RESIDENZIALE&quot;
    }, {
      &quot;value&quot; : &quot;900&quot;,
      &quot;label&quot; : &quot;900 - Creazioni artistiche e letterarie&quot;
    }, {
      &quot;value&quot; : &quot;910&quot;,
      &quot;label&quot; : &quot;910 - Attività degli orti botanici. dei giardini zoologici e delle riserve naturali&quot;
    }, {
      &quot;value&quot; : &quot;920&quot;,
      &quot;label&quot; : &quot;920 - ATTIVITÀ RIGUARDANTI LE LOTTERIE, LE SCOMMESSE, LE CASE DA GIOCO&quot;
    }, {
      &quot;value&quot; : &quot;931&quot;,
      &quot;label&quot; : &quot;931 - Altre attività sportive&quot;
    }, {
      &quot;value&quot; : &quot;932&quot;,
      &quot;label&quot; : &quot;932 - Altre attività ricreative e di divertimento&quot;
    }, {
      &quot;value&quot; : &quot;941&quot;,
      &quot;label&quot; : &quot;941 - ATTIVITÀ DI ORGANIZZAZIONI ECONOMICHE. DI DATORI DI LAVORO E PROFESSIONALI&quot;
    }, {
      &quot;value&quot; : &quot;942&quot;,
      &quot;label&quot; : &quot;942 - ATTIVITÀ DEI SINDACATI DI LAVORATORI DIPENDENTI&quot;
    }, {
      &quot;value&quot; : &quot;949&quot;,
      &quot;label&quot; : &quot;949 - Attività di altre organizzazioni associative nca&quot;
    }, {
      &quot;value&quot; : &quot;951&quot;,
      &quot;label&quot; : &quot;951 - RIPARAZIONE DI COMPUTER E DI APPARECCHIATURE PER LE COMUNICAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;952&quot;,
      &quot;label&quot; : &quot;952 - Riparazione di altri beni per uso personale e per la casa&quot;
    }, {
      &quot;value&quot; : &quot;960&quot;,
      &quot;label&quot; : &quot;960 - Servizi di pompe funebri e attività connesse&quot;
    }, {
      &quot;value&quot; : &quot;970&quot;,
      &quot;label&quot; : &quot;970 - ATTIVITÀ DI FAMIGLIE E CONVIVENZE COME DATORI DI LAVORO PER PERSONALE DOMESTICO&quot;
    }, {
      &quot;value&quot; : &quot;981&quot;,
      &quot;label&quot; : &quot;981 - PRODUZIONE DI BENI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;
    }, {
      &quot;value&quot; : &quot;982&quot;,
      &quot;label&quot; : &quot;982 - PRODUZIONE DI SERVIZI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;
    }, {
      &quot;value&quot; : &quot;990&quot;,
      &quot;label&quot; : &quot;990 - ORGANIZZAZIONI ED ORGANISMI EXTRATERRITORIALI&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues11&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetSAECodesIndividual600</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValuesCons&quot; : [ {
      &quot;value&quot; : &quot;600&quot;,
      &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetSAECodesIndividual601</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValuesProd&quot; : [ {
      &quot;value&quot; : &quot;614&quot;,
      &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;
    }, {
      &quot;value&quot; : &quot;615&quot;,
      &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SAEIndividual</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;$Vlocity.TRUE&quot;,
  &quot;isIfElseBlock&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetCurrentActivityLabel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ActivityLabel&quot; : &quot;=FILTER(LIST(ExtractCodActivity:Modifica:ActivityValues), &apos;value == \&quot;&apos; + ExtractCodActivity:AtecoActivityType  + &apos;\&quot;&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;ActivityLabel:label&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:ActivityLabel&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>14.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetCurrentATECOLabel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ActivityLabel&quot; : &quot;=FILTER(LIST(ExtractCodActivity:Modifica:ATECOValues), &apos;value == \&quot;&apos; + ExtractCodActivity:AtecoShortCode + &apos;\&quot;&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;ActivityLabel:label&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:ATECOLabel&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetCurrentRAELabel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;RAELabel&quot; : &quot;=FILTER(LIST(ExtractCodActivity:Modifica:RAEValues), &apos;value == \&quot;&apos; + ExtractCodActivity:AtecoRAE  + &apos;\&quot;&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;RAELabel:label&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:RAELabel&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetCurrentSAELabel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAELabel&quot; : &quot;=FILTER(LIST(ExtractCodActivity:Modifica:SAEValues), &apos;value == \&quot;&apos; + ExtractCodActivity:AtecoSAE  + &apos;\&quot;&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;SAELabel:label&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:SAELabel&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetCurrentSummaryLabel</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SummaryLabel&quot; : &quot;=FILTER(LIST(ExtractCodActivity:Modifica:SummaryValues), &apos;value == \&quot;&apos; + ExtractCodActivity:AtecoShort  + &apos;\&quot;&apos;)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;SummaryLabel:label&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:SummaryLabel&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>16.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetEditLabels</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;RAELabel&quot; : &quot;=ExtractCodActivity:RAELabel&quot;,
    &quot;SAELabel&quot; : &quot;=ExtractCodActivity:SAELabel&quot;,
    &quot;SummaryLabel&quot; : &quot;=ExtractCodActivity:SummaryLabel&quot;,
    &quot;ActivityLabel&quot; : &quot;=ExtractCodActivity:ActivityLabel&quot;,
    &quot;ATECOLabel&quot; : &quot;=ExtractCodActivity:ATECOLabel&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>15.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetOptionsOld</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;SAEValues&quot; : &quot;=%TemporarySAEValues%&quot;,
    &quot;ATECOValues&quot; : &quot;=%TemporaryATECOValues%&quot;,
    &quot;SAEValuesProd&quot; : [ {
      &quot;value&quot; : &quot;614&quot;,
      &quot;label&quot; : &quot;614 - FAMIGLIE PRODUTTRICI ARTIGIANE&quot;
    }, {
      &quot;value&quot; : &quot;615&quot;,
      &quot;label&quot; : &quot;615 - ALTRE FAMIGLIE PRODUTTRICI&quot;
    } ],
    &quot;ATECOValuesProd&quot; : [ {
      &quot;value&quot; : &quot;001&quot;,
      &quot;label&quot; : &quot;001 - ONLUS&quot;
    }, {
      &quot;value&quot; : &quot;002&quot;,
      &quot;label&quot; : &quot;002 - CONDOMINI&quot;
    }, {
      &quot;value&quot; : &quot;003&quot;,
      &quot;label&quot; : &quot;003 - ENTE RELIGIOSO&quot;
    }, {
      &quot;value&quot; : &quot;004&quot;,
      &quot;label&quot; : &quot;004 - ALTRE ASSOCIAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;011&quot;,
      &quot;label&quot; : &quot;011 - Coltivazione di ortaggi e meloni. radici e tuberi&quot;
    }, {
      &quot;value&quot; : &quot;012&quot;,
      &quot;label&quot; : &quot;012 - Coltivazione di altri alberi da frutta. frutti di bosco e in guscio&quot;
    }, {
      &quot;value&quot; : &quot;013&quot;,
      &quot;label&quot; : &quot;013 - Riproduzione delle piante&quot;
    }, {
      &quot;value&quot; : &quot;014&quot;,
      &quot;label&quot; : &quot;014 - Allevamento di altri bovini e di bufalini&quot;
    }, {
      &quot;value&quot; : &quot;015&quot;,
      &quot;label&quot; : &quot;015 - Coltivazioni agricole associate allallevamento di animali: attività mista&quot;
    }, {
      &quot;value&quot; : &quot;016&quot;,
      &quot;label&quot; : &quot;016 - Attività di supporto alla produzione vegetale&quot;
    }, {
      &quot;value&quot; : &quot;017&quot;,
      &quot;label&quot; : &quot;017 - Caccia. cattura di animali e servizi connessi&quot;
    }, {
      &quot;value&quot; : &quot;021&quot;,
      &quot;label&quot; : &quot;021 - Silvicoltura ed altre attività forestali&quot;
    }, {
      &quot;value&quot; : &quot;022&quot;,
      &quot;label&quot; : &quot;022 - Utilizzo di aree forestali&quot;
    }, {
      &quot;value&quot; : &quot;023&quot;,
      &quot;label&quot; : &quot;023 - Raccolta di prodotti selvatici non legnosi&quot;
    }, {
      &quot;value&quot; : &quot;024&quot;,
      &quot;label&quot; : &quot;024 - Servizi di supporto per la silvicoltura&quot;
    }, {
      &quot;value&quot; : &quot;031&quot;,
      &quot;label&quot; : &quot;031 - Pesca marina&quot;
    }, {
      &quot;value&quot; : &quot;032&quot;,
      &quot;label&quot; : &quot;032 - Acquacoltura in acque dolci&quot;
    }, {
      &quot;value&quot; : &quot;051&quot;,
      &quot;label&quot; : &quot;051 - Estrazione di antracite&quot;
    }, {
      &quot;value&quot; : &quot;052&quot;,
      &quot;label&quot; : &quot;052 - Estrazione di lignite&quot;
    }, {
      &quot;value&quot; : &quot;061&quot;,
      &quot;label&quot; : &quot;061 - ESTRAZIONE DI PETROLIO GREGGIO&quot;
    }, {
      &quot;value&quot; : &quot;062&quot;,
      &quot;label&quot; : &quot;062 - Estrazione di gas naturale&quot;
    }, {
      &quot;value&quot; : &quot;071&quot;,
      &quot;label&quot; : &quot;071 - Estrazione di minerali metalliferi ferrosi&quot;
    }, {
      &quot;value&quot; : &quot;072&quot;,
      &quot;label&quot; : &quot;072 - ESTRAZIONE DI MINERALI METALLIFERI NON FERROSI&quot;
    }, {
      &quot;value&quot; : &quot;081&quot;,
      &quot;label&quot; : &quot;081 - Estrazione di pietre ornamentali e da costruzione. calcare. pietra da gesso. creta e ardesia&quot;
    }, {
      &quot;value&quot; : &quot;089&quot;,
      &quot;label&quot; : &quot;089 - ESTRAZIONE DI MINERALI DA CAVE E MINIERE NCA&quot;
    }, {
      &quot;value&quot; : &quot;091&quot;,
      &quot;label&quot; : &quot;091 - Attività di supporto allestrazione di petrolio e di gas naturale&quot;
    }, {
      &quot;value&quot; : &quot;099&quot;,
      &quot;label&quot; : &quot;099 - Attività di supporto per lestrazione da cave e miniere di altri minerali&quot;
    }, {
      &quot;value&quot; : &quot;101&quot;,
      &quot;label&quot; : &quot;101 - Lavorazione e conservazione di carne (escluso volatili)&quot;
    }, {
      &quot;value&quot; : &quot;102&quot;,
      &quot;label&quot; : &quot;102 - Lavorazione e conservazione di pesce. crostacei e molluschi&quot;
    }, {
      &quot;value&quot; : &quot;103&quot;,
      &quot;label&quot; : &quot;103 - Lavorazione e conservazione delle patate&quot;
    }, {
      &quot;value&quot; : &quot;104&quot;,
      &quot;label&quot; : &quot;104 - Produzione di margarina e di grassi commestibili simili&quot;
    }, {
      &quot;value&quot; : &quot;105&quot;,
      &quot;label&quot; : &quot;105 - Industria lattiero-casearia. trattamento igienico. conservazione del latte&quot;
    }, {
      &quot;value&quot; : &quot;106&quot;,
      &quot;label&quot; : &quot;106 - LAVORAZIONE DELLE GRANAGLIE. PRODUZIONE DI AMIDI E DI PRODOTTI AMIDACEI&quot;
    }, {
      &quot;value&quot; : &quot;107&quot;,
      &quot;label&quot; : &quot;107 - Produzione di fette biscottate e di biscotti&quot;
    }, {
      &quot;value&quot; : &quot;108&quot;,
      &quot;label&quot; : &quot;108 - Produzione di cacao. cioccolato. caramelle e confetterie&quot;
    }, {
      &quot;value&quot; : &quot;109&quot;,
      &quot;label&quot; : &quot;109 - PRODUZIONE DI PRODOTTI PER LALIMENTAZIONE DEGLI ANIMALI&quot;
    }, {
      &quot;value&quot; : &quot;110&quot;,
      &quot;label&quot; : &quot;110 - Produzione di vini da uve&quot;
    }, {
      &quot;value&quot; : &quot;120&quot;,
      &quot;label&quot; : &quot;120 - INDUSTRIA DEL TABACCO&quot;
    }, {
      &quot;value&quot; : &quot;131&quot;,
      &quot;label&quot; : &quot;131 - Preparazione e filatura di fibre tessili&quot;
    }, {
      &quot;value&quot; : &quot;132&quot;,
      &quot;label&quot; : &quot;132 - Tessitura&quot;
    }, {
      &quot;value&quot; : &quot;133&quot;,
      &quot;label&quot; : &quot;133 - Finissaggio dei tessili&quot;
    }, {
      &quot;value&quot; : &quot;139&quot;,
      &quot;label&quot; : &quot;139 - Fabbricazione di tessuti a maglia&quot;
    }, {
      &quot;value&quot; : &quot;141&quot;,
      &quot;label&quot; : &quot;141 - CONFEZIONE DI ARTICOLI DI ABBIGLIAMENTO (ESCLUSO ABBIGLIAMENTO IN PELLICCIA)&quot;
    }, {
      &quot;value&quot; : &quot;142&quot;,
      &quot;label&quot; : &quot;142 - Confezione di articoli in pelliccia&quot;
    }, {
      &quot;value&quot; : &quot;143&quot;,
      &quot;label&quot; : &quot;143 - Fabbricazione di altri articoli di maglieria&quot;
    }, {
      &quot;value&quot; : &quot;151&quot;,
      &quot;label&quot; : &quot;151 - Preparazione e concia del cuoio&quot;
    }, {
      &quot;value&quot; : &quot;152&quot;,
      &quot;label&quot; : &quot;152 - Fabbricazione di calzature&quot;
    }, {
      &quot;value&quot; : &quot;161&quot;,
      &quot;label&quot; : &quot;161 - Taglio e piallatura del legno&quot;
    }, {
      &quot;value&quot; : &quot;162&quot;,
      &quot;label&quot; : &quot;162 - Fabbricazione di altri prodotti in legno. sughero. paglia e materiali da intreccio&quot;
    }, {
      &quot;value&quot; : &quot;171&quot;,
      &quot;label&quot; : &quot;171 - FABBRICAZIONE DI PASTA-CARTA. CARTA E CARTONE&quot;
    }, {
      &quot;value&quot; : &quot;172&quot;,
      &quot;label&quot; : &quot;172 - FABBRICAZIONE DI ARTICOLI DI CARTA E CARTONE&quot;
    }, {
      &quot;value&quot; : &quot;181&quot;,
      &quot;label&quot; : &quot;181 - Stampa di giornali&quot;
    }, {
      &quot;value&quot; : &quot;182&quot;,
      &quot;label&quot; : &quot;182 - Riproduzione di supporti registrati&quot;
    }, {
      &quot;value&quot; : &quot;191&quot;,
      &quot;label&quot; : &quot;191 - Fabbricazione di prodotti di cokeria&quot;
    }, {
      &quot;value&quot; : &quot;192&quot;,
      &quot;label&quot; : &quot;192 - Fabbricazione di prodotti derivanti dalla raffinazione del petrolio&quot;
    }, {
      &quot;value&quot; : &quot;201&quot;,
      &quot;label&quot; : &quot;201 - Fabbricazione di fertilizzanti e composti azotati&quot;
    }, {
      &quot;value&quot; : &quot;202&quot;,
      &quot;label&quot; : &quot;202 - Fabbricazione di agrofarmaci e di altri prodotti chimici per lagricoltura&quot;
    }, {
      &quot;value&quot; : &quot;203&quot;,
      &quot;label&quot; : &quot;203 - Fabbricazione di pitture. vernici e smalti. inchiostri da stampa e adesivi sintetici (mastici)&quot;
    }, {
      &quot;value&quot; : &quot;204&quot;,
      &quot;label&quot; : &quot;204 - Fabbricazione di profumi e cosmetici&quot;
    }, {
      &quot;value&quot; : &quot;205&quot;,
      &quot;label&quot; : &quot;205 - Fabbricazione di prodotti chimici nca&quot;
    }, {
      &quot;value&quot; : &quot;206&quot;,
      &quot;label&quot; : &quot;206 - Fabbricazione di fibre sintetiche e artificiali&quot;
    }, {
      &quot;value&quot; : &quot;211&quot;,
      &quot;label&quot; : &quot;211 - Fabbricazione di prodotti farmaceutici di base&quot;
    }, {
      &quot;value&quot; : &quot;212&quot;,
      &quot;label&quot; : &quot;212 - Fabbricazione di medicinali e preparati farmaceutici&quot;
    }, {
      &quot;value&quot; : &quot;221&quot;,
      &quot;label&quot; : &quot;221 - FABBRICAZIONE DI ARTICOLI IN GOMMA&quot;
    }, {
      &quot;value&quot; : &quot;222&quot;,
      &quot;label&quot; : &quot;222 - Fabbricazione di altri articoli in materie plastiche&quot;
    }, {
      &quot;value&quot; : &quot;231&quot;,
      &quot;label&quot; : &quot;231 - Fabbricazione di vetro piano&quot;
    }, {
      &quot;value&quot; : &quot;232&quot;,
      &quot;label&quot; : &quot;232 - Fabbricazione di prodotti refrattari&quot;
    }, {
      &quot;value&quot; : &quot;233&quot;,
      &quot;label&quot; : &quot;233 - Fabbricazione di mattoni. tegole ed altri prodotti per ledilizia in terracotta&quot;
    }, {
      &quot;value&quot; : &quot;234&quot;,
      &quot;label&quot; : &quot;234 - Fabbricazione di altri prodotti in ceramica&quot;
    }, {
      &quot;value&quot; : &quot;235&quot;,
      &quot;label&quot; : &quot;235 - Produzione di cemento&quot;
    }, {
      &quot;value&quot; : &quot;236&quot;,
      &quot;label&quot; : &quot;236 - Fabbricazione di prodotti in fibrocemento&quot;
    }, {
      &quot;value&quot; : &quot;237&quot;,
      &quot;label&quot; : &quot;237 - Taglio. modellatura e finitura di pietre&quot;
    }, {
      &quot;value&quot; : &quot;239&quot;,
      &quot;label&quot; : &quot;239 - Produzione di prodotti abrasivi&quot;
    }, {
      &quot;value&quot; : &quot;241&quot;,
      &quot;label&quot; : &quot;241 - SIDERURGIA&quot;
    }, {
      &quot;value&quot; : &quot;242&quot;,
      &quot;label&quot; : &quot;242 - FABBRICAZIONE DI TUBI. CONDOTTI. PROFILATI CAVI E RELATIVI ACCESSORI IN ACCIAIO (ESCLUSI QUELLI IN ACCIAIO COLATO)&quot;
    }, {
      &quot;value&quot; : &quot;243&quot;,
      &quot;label&quot; : &quot;243 - Profilatura mediante formatura o piegatura a freddo&quot;
    }, {
      &quot;value&quot; : &quot;244&quot;,
      &quot;label&quot; : &quot;244 - Produzione di piombo. zinco e stagno e semilavorati&quot;
    }, {
      &quot;value&quot; : &quot;245&quot;,
      &quot;label&quot; : &quot;245 - Fusione di acciaio&quot;
    }, {
      &quot;value&quot; : &quot;251&quot;,
      &quot;label&quot; : &quot;251 - Fabbricazione di strutture metalliche e di parti di strutture&quot;
    }, {
      &quot;value&quot; : &quot;252&quot;,
      &quot;label&quot; : &quot;252 - Fabbricazione di radiatori e contenitori in metallo per caldaie per il riscaldamento centrale&quot;
    }, {
      &quot;value&quot; : &quot;253&quot;,
      &quot;label&quot; : &quot;253 - Fabbricazione di generatori di vapore (esclusi i contenitori in metallo per caldaie per il riscaldamento centrale ad acqua calda)&quot;
    }, {
      &quot;value&quot; : &quot;254&quot;,
      &quot;label&quot; : &quot;254 - FABBRICAZIONE DI ARMI E MUNIZIONI&quot;
    }, {
      &quot;value&quot; : &quot;255&quot;,
      &quot;label&quot; : &quot;255 - Fucinatura. imbutitura. stampaggio e profilatura dei metalli&quot;
    }, {
      &quot;value&quot; : &quot;256&quot;,
      &quot;label&quot; : &quot;256 - Lavori di meccanica generale&quot;
    }, {
      &quot;value&quot; : &quot;257&quot;,
      &quot;label&quot; : &quot;257 - FABBRICAZIONE DI ARTICOLI DI COLTELLERIA. UTENSILI E OGGETTI DI FERRAMENTA&quot;
    }, {
      &quot;value&quot; : &quot;259&quot;,
      &quot;label&quot; : &quot;259 - Fabbricazione di imballaggi leggeri in metallo&quot;
    }, {
      &quot;value&quot; : &quot;261&quot;,
      &quot;label&quot; : &quot;261 - Fabbricazione di schede elettroniche assemblate&quot;
    }, {
      &quot;value&quot; : &quot;262&quot;,
      &quot;label&quot; : &quot;262 - Fabbricazione di computer e unità periferiche&quot;
    }, {
      &quot;value&quot; : &quot;263&quot;,
      &quot;label&quot; : &quot;263 - Fabbricazione di apparecchiature per le telecomunicazioni&quot;
    }, {
      &quot;value&quot; : &quot;264&quot;,
      &quot;label&quot; : &quot;264 - Fabbricazione di prodotti di elettronica di consumo audio e video&quot;
    }, {
      &quot;value&quot; : &quot;265&quot;,
      &quot;label&quot; : &quot;265 - Fabbricazione di strumenti e apparecchi di misurazione. prova e navigazione (esclusi quelli ottici)&quot;
    }, {
      &quot;value&quot; : &quot;266&quot;,
      &quot;label&quot; : &quot;266 - Fabbricazione di strumenti per irradiazione. apparecchiature elettromedicali ed elettroterapeutiche&quot;
    }, {
      &quot;value&quot; : &quot;267&quot;,
      &quot;label&quot; : &quot;267 - Fabbricazione di strumenti ottici e attrezzature fotografiche&quot;
    }, {
      &quot;value&quot; : &quot;268&quot;,
      &quot;label&quot; : &quot;268 - Fabbricazione di supporti magnetici ed ottici&quot;
    }, {
      &quot;value&quot; : &quot;271&quot;,
      &quot;label&quot; : &quot;271 - FABBRICAZIONE DI MOTORI. GENERATORI E TRASFORMATORI ELETTRICI E DI APPARECCHIATURE PER LA DISTRIBUZIONE E IL CONTROLLO DELLELETTRICITÀ&quot;
    }, {
      &quot;value&quot; : &quot;272&quot;,
      &quot;label&quot; : &quot;272 - Fabbricazione di batterie di pile ed accumulatori elettrici&quot;
    }, {
      &quot;value&quot; : &quot;273&quot;,
      &quot;label&quot; : &quot;273 - Fabbricazione di attrezzature per cablaggio&quot;
    }, {
      &quot;value&quot; : &quot;274&quot;,
      &quot;label&quot; : &quot;274 - Fabbricazione di apparecchiature per illuminazione&quot;
    }, {
      &quot;value&quot; : &quot;275&quot;,
      &quot;label&quot; : &quot;275 - FABBRICAZIONE DI APPARECCHI PER USO DOMESTICO&quot;
    }, {
      &quot;value&quot; : &quot;279&quot;,
      &quot;label&quot; : &quot;279 - Fabbricazione di altre apparecchiature elettriche&quot;
    }, {
      &quot;value&quot; : &quot;281&quot;,
      &quot;label&quot; : &quot;281 - Fabbricazione di motori e turbine (esclusi i motori per aeromobili. veicoli e motocicli)&quot;
    }, {
      &quot;value&quot; : &quot;282&quot;,
      &quot;label&quot; : &quot;282 - FABBRICAZIONE DI ALTRE MACCHINE DI IMPIEGO GENERALE&quot;
    }, {
      &quot;value&quot; : &quot;283&quot;,
      &quot;label&quot; : &quot;283 - Fabbricazione di macchine per lagricoltura e la silvicoltura&quot;
    }, {
      &quot;value&quot; : &quot;284&quot;,
      &quot;label&quot; : &quot;284 - Fabbricazione di macchine utensili per la formatura dei metalli&quot;
    }, {
      &quot;value&quot; : &quot;289&quot;,
      &quot;label&quot; : &quot;289 - Fabbricazione di macchine per lindustria alimentare. delle bevande e del tabacco&quot;
    }, {
      &quot;value&quot; : &quot;291&quot;,
      &quot;label&quot; : &quot;291 - Fabbricazione di autoveicoli&quot;
    }, {
      &quot;value&quot; : &quot;292&quot;,
      &quot;label&quot; : &quot;292 - Fabbricazione di carrozzerie per autoveicoli. rimorchi e semirimorchi&quot;
    }, {
      &quot;value&quot; : &quot;293&quot;,
      &quot;label&quot; : &quot;293 - Fabbricazione di apparecchiature elettriche ed elettroniche per autoveicoli e loro motori&quot;
    }, {
      &quot;value&quot; : &quot;301&quot;,
      &quot;label&quot; : &quot;301 - Costruzione di navi e di strutture galleggianti&quot;
    }, {
      &quot;value&quot; : &quot;302&quot;,
      &quot;label&quot; : &quot;302 - COSTRUZIONE DI LOCOMOTIVE E DI MATERIALE ROTABILE FERRO-TRANVIARIO&quot;
    }, {
      &quot;value&quot; : &quot;303&quot;,
      &quot;label&quot; : &quot;303 - Fabbricazione di aeromobili. di veicoli spaziali e dei relativi dispositivi&quot;
    }, {
      &quot;value&quot; : &quot;304&quot;,
      &quot;label&quot; : &quot;304 - Fabbricazione di veicoli militari da combattimento&quot;
    }, {
      &quot;value&quot; : &quot;309&quot;,
      &quot;label&quot; : &quot;309 - FABBRICAZIONE DI MEZZI DI TRASPORTO NCA&quot;
    }, {
      &quot;value&quot; : &quot;310&quot;,
      &quot;label&quot; : &quot;310 - Fabbricazione di materassi&quot;
    }, {
      &quot;value&quot; : &quot;321&quot;,
      &quot;label&quot; : &quot;321 - Coniazione di monete&quot;
    }, {
      &quot;value&quot; : &quot;322&quot;,
      &quot;label&quot; : &quot;322 - Fabbricazione di strumenti musicali&quot;
    }, {
      &quot;value&quot; : &quot;323&quot;,
      &quot;label&quot; : &quot;323 - Fabbricazione di articoli sportivi&quot;
    }, {
      &quot;value&quot; : &quot;324&quot;,
      &quot;label&quot; : &quot;324 - FABBRICAZIONE DI GIOCHI E GIOCATTOLI&quot;
    }, {
      &quot;value&quot; : &quot;325&quot;,
      &quot;label&quot; : &quot;325 - FABBRICAZIONE DI STRUMENTI E FORNITURE MEDICHE E DENTISTICHE&quot;
    }, {
      &quot;value&quot; : &quot;329&quot;,
      &quot;label&quot; : &quot;329 - Altre industrie manifatturiere nca&quot;
    }, {
      &quot;value&quot; : &quot;331&quot;,
      &quot;label&quot; : &quot;331 - Riparazione e manutenzione di locomotive e di materiale rotabile ferro-tranviario (esclusi i loro motori)&quot;
    }, {
      &quot;value&quot; : &quot;332&quot;,
      &quot;label&quot; : &quot;332 - Installazione di macchine ed apparecchiature industriali&quot;
    }, {
      &quot;value&quot; : &quot;351&quot;,
      &quot;label&quot; : &quot;351 - Commercio di energia elettrica&quot;
    }, {
      &quot;value&quot; : &quot;352&quot;,
      &quot;label&quot; : &quot;352 - Distribuzione di combustibili gassosi mediante condotte&quot;
    }, {
      &quot;value&quot; : &quot;353&quot;,
      &quot;label&quot; : &quot;353 - Fornitura di vapore e aria condizionata&quot;
    }, {
      &quot;value&quot; : &quot;360&quot;,
      &quot;label&quot; : &quot;360 - RACCOLTA, TRATTAMENTO E FORNITURA DI ACQUA&quot;
    }, {
      &quot;value&quot; : &quot;370&quot;,
      &quot;label&quot; : &quot;370 - GESTIONE DELLE RETI FOGNARIE&quot;
    }, {
      &quot;value&quot; : &quot;381&quot;,
      &quot;label&quot; : &quot;381 - RACCOLTA DEI RIFIUTI&quot;
    }, {
      &quot;value&quot; : &quot;382&quot;,
      &quot;label&quot; : &quot;382 - Trattamento e smaltimento di rifiuti non pericolosi&quot;
    }, {
      &quot;value&quot; : &quot;383&quot;,
      &quot;label&quot; : &quot;383 - RECUPERO DEI MATERIALI&quot;
    }, {
      &quot;value&quot; : &quot;390&quot;,
      &quot;label&quot; : &quot;390 - ATTIVITÀ DI RISANAMENTO E ALTRI SERVIZI DI GESTIONE DEI RIFIUTI&quot;
    }, {
      &quot;value&quot; : &quot;411&quot;,
      &quot;label&quot; : &quot;411 - SVILUPPO DI PROGETTI IMMOBILIARI&quot;
    }, {
      &quot;value&quot; : &quot;412&quot;,
      &quot;label&quot; : &quot;412 - Costruzione di edifici residenziali e non residenziali&quot;
    }, {
      &quot;value&quot; : &quot;421&quot;,
      &quot;label&quot; : &quot;421 - Costruzione di strade e autostrade&quot;
    }, {
      &quot;value&quot; : &quot;422&quot;,
      &quot;label&quot; : &quot;422 - Costruzione di opere di pubblica utilità per il trasporto di fluidi&quot;
    }, {
      &quot;value&quot; : &quot;429&quot;,
      &quot;label&quot; : &quot;429 - Costruzione di opere idrauliche&quot;
    }, {
      &quot;value&quot; : &quot;431&quot;,
      &quot;label&quot; : &quot;431 - DemOLIZIONE E PREPARAZIONE DEL CANTIERE EDILE&quot;
    }, {
      &quot;value&quot; : &quot;432&quot;,
      &quot;label&quot; : &quot;432 - Installazione di impianti elettrici&quot;
    }, {
      &quot;value&quot; : &quot;433&quot;,
      &quot;label&quot; : &quot;433 - Altri lavori di completamento e di finitura degli edifici&quot;
    }, {
      &quot;value&quot; : &quot;439&quot;,
      &quot;label&quot; : &quot;439 - ALTRI LAVORI SPECIALIZZATI DI COSTRUZIONE&quot;
    }, {
      &quot;value&quot; : &quot;451&quot;,
      &quot;label&quot; : &quot;451 - Commercio di autovetture e di autoveicoli leggeri&quot;
    }, {
      &quot;value&quot; : &quot;452&quot;,
      &quot;label&quot; : &quot;452 - Manutenzione e riparazione di autoveicoli&quot;
    }, {
      &quot;value&quot; : &quot;453&quot;,
      &quot;label&quot; : &quot;453 - COMMERCIO DI PARTI E ACCESSORI DI AUTOVEICOLI&quot;
    }, {
      &quot;value&quot; : &quot;454&quot;,
      &quot;label&quot; : &quot;454 - Commercio. manutenzione e riparazione di motocicli e relative parti ed accessori&quot;
    }, {
      &quot;value&quot; : &quot;461&quot;,
      &quot;label&quot; : &quot;461 - Intermediari del commercio di prodotti tessili. abbigliamento. pellicce. calzature e articoli in pelle&quot;
    }, {
      &quot;value&quot; : &quot;462&quot;,
      &quot;label&quot; : &quot;462 - Commercio allingrosso di cereali. tabacco grezzo. sementi e alimenti per il bestiame (mangimi)&quot;
    }, {
      &quot;value&quot; : &quot;463&quot;,
      &quot;label&quot; : &quot;463 - Commercio allingrosso non specializzato di prodotti alimentari. bevande e tabacco&quot;
    }, {
      &quot;value&quot; : &quot;464&quot;,
      &quot;label&quot; : &quot;464 - Commercio allingrosso di orologi e di gioielleria&quot;
    }, {
      &quot;value&quot; : &quot;465&quot;,
      &quot;label&quot; : &quot;465 - Commercio allingrosso di computer. apparecchiature informatiche periferiche e di software&quot;
    }, {
      &quot;value&quot; : &quot;466&quot;,
      &quot;label&quot; : &quot;466 - Commercio allingrosso di macchinari per lindustria tessile. di macchine per cucire e per maglieria&quot;
    }, {
      &quot;value&quot; : &quot;467&quot;,
      &quot;label&quot; : &quot;467 - Commercio allingrosso di combustibili solidi. liquidi. gassosi e di prodotti derivati&quot;
    }, {
      &quot;value&quot; : &quot;469&quot;,
      &quot;label&quot; : &quot;469 - Commercio allingrosso non specializzato&quot;
    }, {
      &quot;value&quot; : &quot;471&quot;,
      &quot;label&quot; : &quot;471 - Commercio al dettaglio in altri esercizi non specializzati&quot;
    }, {
      &quot;value&quot; : &quot;472&quot;,
      &quot;label&quot; : &quot;472 - Commercio al dettaglio di bevande in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;473&quot;,
      &quot;label&quot; : &quot;473 - Commercio al dettaglio di carburante per autotrazione in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;474&quot;,
      &quot;label&quot; : &quot;474 - COMMERCIO AL DETTAGLIO DI APPARECCHIATURE INFORMATICHE E PER LE TELECOMUNICAZIONI (ICT) IN ESERCIZI SPECIALIZZATI&quot;
    }, {
      &quot;value&quot; : &quot;475&quot;,
      &quot;label&quot; : &quot;475 - Commercio al dettaglio di ferramenta. vernici. vetro piano e materiali da costruzione in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;476&quot;,
      &quot;label&quot; : &quot;476 - Commercio al dettaglio di libri in esercizi specializzati&quot;
    }, {
      &quot;value&quot; : &quot;477&quot;,
      &quot;label&quot; : &quot;477 - Commercio al dettaglio di articoli di seconda mano in negozi&quot;
    }, {
      &quot;value&quot; : &quot;478&quot;,
      &quot;label&quot; : &quot;478 - COMMERCIO AL DETTAGLIO AMBULANTE&quot;
    }, {
      &quot;value&quot; : &quot;479&quot;,
      &quot;label&quot; : &quot;479 - Altro commercio al dettaglio al di fuori di negozi. banchi o mercati&quot;
    }, {
      &quot;value&quot; : &quot;491&quot;,
      &quot;label&quot; : &quot;491 - Trasporto ferroviario di passeggeri (interurbano)&quot;
    }, {
      &quot;value&quot; : &quot;492&quot;,
      &quot;label&quot; : &quot;492 - Trasporto ferroviario di merci&quot;
    }, {
      &quot;value&quot; : &quot;493&quot;,
      &quot;label&quot; : &quot;493 - ALTRI TRASPORTI TERRESTRI DI PASSEGGERI&quot;
    }, {
      &quot;value&quot; : &quot;494&quot;,
      &quot;label&quot; : &quot;494 - Trasporto di merci su strada&quot;
    }, {
      &quot;value&quot; : &quot;495&quot;,
      &quot;label&quot; : &quot;495 - Trasporto mediante condotte&quot;
    }, {
      &quot;value&quot; : &quot;501&quot;,
      &quot;label&quot; : &quot;501 - Trasporto marittimo e costiero di passeggeri&quot;
    }, {
      &quot;value&quot; : &quot;502&quot;,
      &quot;label&quot; : &quot;502 - Trasporto marittimo e costiero di merci&quot;
    }, {
      &quot;value&quot; : &quot;503&quot;,
      &quot;label&quot; : &quot;503 - Trasporto di passeggeri per vie dacqua interne&quot;
    }, {
      &quot;value&quot; : &quot;504&quot;,
      &quot;label&quot; : &quot;504 - Trasporto di merci per vie dacqua interne&quot;
    }, {
      &quot;value&quot; : &quot;511&quot;,
      &quot;label&quot; : &quot;511 - Trasporto aereo di passeggeri&quot;
    }, {
      &quot;value&quot; : &quot;512&quot;,
      &quot;label&quot; : &quot;512 - Trasporto spaziale&quot;
    }, {
      &quot;value&quot; : &quot;521&quot;,
      &quot;label&quot; : &quot;521 - Magazzinaggio e custodia&quot;
    }, {
      &quot;value&quot; : &quot;522&quot;,
      &quot;label&quot; : &quot;522 - Attività dei servizi connessi al trasporto aereo&quot;
    }, {
      &quot;value&quot; : &quot;531&quot;,
      &quot;label&quot; : &quot;531 - Attività postali con obbligo di servizio universale&quot;
    }, {
      &quot;value&quot; : &quot;532&quot;,
      &quot;label&quot; : &quot;532 - Altre attività postali e di corriere senza obbligo di servizio universale&quot;
    }, {
      &quot;value&quot; : &quot;551&quot;,
      &quot;label&quot; : &quot;551 - Alberghi e strutture simili&quot;
    }, {
      &quot;value&quot; : &quot;552&quot;,
      &quot;label&quot; : &quot;552 - Alloggi per vacanze e altre strutture per brevi soggiorni&quot;
    }, {
      &quot;value&quot; : &quot;553&quot;,
      &quot;label&quot; : &quot;553 - Aree di campeggio e aree attrezzate per camper e roulotte&quot;
    }, {
      &quot;value&quot; : &quot;559&quot;,
      &quot;label&quot; : &quot;559 - Altri alloggi&quot;
    }, {
      &quot;value&quot; : &quot;561&quot;,
      &quot;label&quot; : &quot;561 - Ristoranti e attività di ristorazione mobile&quot;
    }, {
      &quot;value&quot; : &quot;562&quot;,
      &quot;label&quot; : &quot;562 - Mense e catering continuativo su base contrattuale&quot;
    }, {
      &quot;value&quot; : &quot;563&quot;,
      &quot;label&quot; : &quot;563 - Bar e altri esercizi simili senza cucina&quot;
    }, {
      &quot;value&quot; : &quot;581&quot;,
      &quot;label&quot; : &quot;581 - Edizione di quotidiani&quot;
    }, {
      &quot;value&quot; : &quot;582&quot;,
      &quot;label&quot; : &quot;582 - Edizione di altri software&quot;
    }, {
      &quot;value&quot; : &quot;591&quot;,
      &quot;label&quot; : &quot;591 - Attività di post-produzione cinematografica. di video e di programmi televisivi&quot;
    }, {
      &quot;value&quot; : &quot;592&quot;,
      &quot;label&quot; : &quot;592 - Attività di registrazione sonora e di editoria musicale&quot;
    }, {
      &quot;value&quot; : &quot;601&quot;,
      &quot;label&quot; : &quot;601 - Trasmissioni radiofoniche&quot;
    }, {
      &quot;value&quot; : &quot;602&quot;,
      &quot;label&quot; : &quot;602 - Attività di programmazione e trasmissioni televisive&quot;
    }, {
      &quot;value&quot; : &quot;611&quot;,
      &quot;label&quot; : &quot;611 - TELECOMUNICAZIONI FISSE&quot;
    }, {
      &quot;value&quot; : &quot;612&quot;,
      &quot;label&quot; : &quot;612 - Telecomunicazioni mobili&quot;
    }, {
      &quot;value&quot; : &quot;613&quot;,
      &quot;label&quot; : &quot;613 - Telecomunicazioni satellitari&quot;
    }, {
      &quot;value&quot; : &quot;619&quot;,
      &quot;label&quot; : &quot;619 - Altre attività di telecomunicazione&quot;
    }, {
      &quot;value&quot; : &quot;620&quot;,
      &quot;label&quot; : &quot;620 - Consulenza nel settore delle tecnologie dellinformatica&quot;
    }, {
      &quot;value&quot; : &quot;631&quot;,
      &quot;label&quot; : &quot;631 - ELABORAZIONE DEI DATI. HOSTING E ATTIVITÀ CONNESSE&quot;
    }, {
      &quot;value&quot; : &quot;639&quot;,
      &quot;label&quot; : &quot;639 - Altre attività dei servizi di informazione nca&quot;
    }, {
      &quot;value&quot; : &quot;641&quot;,
      &quot;label&quot; : &quot;641 - Attività delle banche centrali&quot;
    }, {
      &quot;value&quot; : &quot;642&quot;,
      &quot;label&quot; : &quot;642 - ATTIVITÀ DELLE SOCIETÀ DI PARTECIPAZIONE (HOLDING)&quot;
    }, {
      &quot;value&quot; : &quot;643&quot;,
      &quot;label&quot; : &quot;643 - Società fiduciarie. fondi e altre società simili&quot;
    }, {
      &quot;value&quot; : &quot;649&quot;,
      &quot;label&quot; : &quot;649 - Altre attività creditizie&quot;
    }, {
      &quot;value&quot; : &quot;651&quot;,
      &quot;label&quot; : &quot;651 - Assicurazioni diverse da quelle sulla vita&quot;
    }, {
      &quot;value&quot; : &quot;652&quot;,
      &quot;label&quot; : &quot;652 - RIASSICURAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;653&quot;,
      &quot;label&quot; : &quot;653 - Fondi pensione&quot;
    }, {
      &quot;value&quot; : &quot;661&quot;,
      &quot;label&quot; : &quot;661 - Altre attività ausiliarie dei servizi finanziari (escluse le assicurazioni e i fondi pensione)&quot;
    }, {
      &quot;value&quot; : &quot;662&quot;,
      &quot;label&quot; : &quot;662 - ATTIVITÀ AUSILIARIE DELLE ASSICURAZIONI E DEI FONDI PENSIONE&quot;
    }, {
      &quot;value&quot; : &quot;663&quot;,
      &quot;label&quot; : &quot;663 - Attività di gestione dei fondi&quot;
    }, {
      &quot;value&quot; : &quot;681&quot;,
      &quot;label&quot; : &quot;681 - Compravendita di beni immobili effettuata su beni propri&quot;
    }, {
      &quot;value&quot; : &quot;682&quot;,
      &quot;label&quot; : &quot;682 - Affitto e gestione di immobili di proprietà o in leasing&quot;
    }, {
      &quot;value&quot; : &quot;683&quot;,
      &quot;label&quot; : &quot;683 - Gestione di immobili per conto terzi&quot;
    }, {
      &quot;value&quot; : &quot;691&quot;,
      &quot;label&quot; : &quot;691 - Attività degli studi legali e notarili&quot;
    }, {
      &quot;value&quot; : &quot;692&quot;,
      &quot;label&quot; : &quot;692 - Contabilità. controllo e revisione contabile. consulenza in materia fiscale e del lavoro&quot;
    }, {
      &quot;value&quot; : &quot;701&quot;,
      &quot;label&quot; : &quot;701 - Attività delle holding impegnate nelle attività gestionali (holding operative)&quot;
    }, {
      &quot;value&quot; : &quot;702&quot;,
      &quot;label&quot; : &quot;702 - ATTIVITÀ DI CONSULENZA GESTIONALE&quot;
    }, {
      &quot;value&quot; : &quot;711&quot;,
      &quot;label&quot; : &quot;711 - ATTIVITÀ DEGLI STUDI DI ARCHITETTURA. INGEGNERIA ED ALTRI STUDI TECNICI&quot;
    }, {
      &quot;value&quot; : &quot;712&quot;,
      &quot;label&quot; : &quot;712 - Collaudi ed analisi tecniche&quot;
    }, {
      &quot;value&quot; : &quot;721&quot;,
      &quot;label&quot; : &quot;721 - Altre attività di ricerca e sviluppo sperimentale nel campo delle scienze naturali e dellingegneria&quot;
    }, {
      &quot;value&quot; : &quot;722&quot;,
      &quot;label&quot; : &quot;722 - Ricerca e sviluppo sperimentale nel campo delle scienze sociali e umanistiche&quot;
    }, {
      &quot;value&quot; : &quot;731&quot;,
      &quot;label&quot; : &quot;731 - Attività delle concessionarie e degli altri intermediari di servizi pubblicitari&quot;
    }, {
      &quot;value&quot; : &quot;732&quot;,
      &quot;label&quot; : &quot;732 - RICERCHE DI MERCATO E SONDAGGI DI OPINIONE&quot;
    }, {
      &quot;value&quot; : &quot;741&quot;,
      &quot;label&quot; : &quot;741 - Attività di design specializzate&quot;
    }, {
      &quot;value&quot; : &quot;742&quot;,
      &quot;label&quot; : &quot;742 - Attività fotografiche&quot;
    }, {
      &quot;value&quot; : &quot;743&quot;,
      &quot;label&quot; : &quot;743 - Traduzione e interpretariato&quot;
    }, {
      &quot;value&quot; : &quot;749&quot;,
      &quot;label&quot; : &quot;749 - Altre attività professionali. scientifiche e tecniche nca&quot;
    }, {
      &quot;value&quot; : &quot;750&quot;,
      &quot;label&quot; : &quot;750 - SERVIZI VETERINARI&quot;
    }, {
      &quot;value&quot; : &quot;771&quot;,
      &quot;label&quot; : &quot;771 - NOLEGGIO DI AUTOVEICOLI&quot;
    }, {
      &quot;value&quot; : &quot;772&quot;,
      &quot;label&quot; : &quot;772 - Noleggio di altri beni per uso personale e domestico (escluse le attrezzature sportive e ricreative)&quot;
    }, {
      &quot;value&quot; : &quot;773&quot;,
      &quot;label&quot; : &quot;773 - Noleggio di macchine e attrezzature agricole&quot;
    }, {
      &quot;value&quot; : &quot;774&quot;,
      &quot;label&quot; : &quot;774 - Concessione dei diritti di sfruttamento di proprietà intellettuale e prodotti simili (escluse le opere protette dal copyright)&quot;
    }, {
      &quot;value&quot; : &quot;781&quot;,
      &quot;label&quot; : &quot;781 - ATTIVITÀ DI AGENZIE DI COLLOCAMENTO&quot;
    }, {
      &quot;value&quot; : &quot;782&quot;,
      &quot;label&quot; : &quot;782 - Attività delle agenzie di lavoro temporaneo (interinale)&quot;
    }, {
      &quot;value&quot; : &quot;783&quot;,
      &quot;label&quot; : &quot;783 - Altre attività di fornitura e gestione di risorse umane&quot;
    }, {
      &quot;value&quot; : &quot;791&quot;,
      &quot;label&quot; : &quot;791 - ATTIVITÀ DELLE AGENZIE DI VIAGGIO E DEI TOUR OPERATOR&quot;
    }, {
      &quot;value&quot; : &quot;799&quot;,
      &quot;label&quot; : &quot;799 - Altri servizi di prenotazione e altre attività di assistenza turistica non svolte dalle agenzie di viaggio&quot;
    }, {
      &quot;value&quot; : &quot;801&quot;,
      &quot;label&quot; : &quot;801 - Servizi di vigilanza privata&quot;
    }, {
      &quot;value&quot; : &quot;802&quot;,
      &quot;label&quot; : &quot;802 - Servizi connessi ai sistemi di vigilanza&quot;
    }, {
      &quot;value&quot; : &quot;803&quot;,
      &quot;label&quot; : &quot;803 - Servizi investigativi privati&quot;
    }, {
      &quot;value&quot; : &quot;811&quot;,
      &quot;label&quot; : &quot;811 - Servizi integrati di gestione agli edifici&quot;
    }, {
      &quot;value&quot; : &quot;812&quot;,
      &quot;label&quot; : &quot;812 - Pulizia generale (non specializzata) di edifici&quot;
    }, {
      &quot;value&quot; : &quot;813&quot;,
      &quot;label&quot; : &quot;813 - Cura e manutenzione del paesaggio&quot;
    }, {
      &quot;value&quot; : &quot;821&quot;,
      &quot;label&quot; : &quot;821 - Servizi integrati di supporto per le funzioni dufficio&quot;
    }, {
      &quot;value&quot; : &quot;822&quot;,
      &quot;label&quot; : &quot;822 - Attività dei call center&quot;
    }, {
      &quot;value&quot; : &quot;823&quot;,
      &quot;label&quot; : &quot;823 - ORGANIZZAZIONE DI CONVEGNI E FIERE&quot;
    }, {
      &quot;value&quot; : &quot;829&quot;,
      &quot;label&quot; : &quot;829 - Attività di imballaggio e confezionamento per conto terzi&quot;
    }, {
      &quot;value&quot; : &quot;841&quot;,
      &quot;label&quot; : &quot;841 - Regolamentazione delle attività relative alla fornitura di servizi di assistenza sanitaria. dellistruzione. di servizi culturali e ad altri servizi sociali (esclusa lassicurazione sociale obbligatoria)&quot;
    }, {
      &quot;value&quot; : &quot;842&quot;,
      &quot;label&quot; : &quot;842 - Affari esteri&quot;
    }, {
      &quot;value&quot; : &quot;843&quot;,
      &quot;label&quot; : &quot;843 - Assicurazione sociale obbligatoria&quot;
    }, {
      &quot;value&quot; : &quot;851&quot;,
      &quot;label&quot; : &quot;851 - Istruzione prescolastica&quot;
    }, {
      &quot;value&quot; : &quot;852&quot;,
      &quot;label&quot; : &quot;852 - Istruzione primaria&quot;
    }, {
      &quot;value&quot; : &quot;853&quot;,
      &quot;label&quot; : &quot;853 - ISTRUZIONE SECONDARIA&quot;
    }, {
      &quot;value&quot; : &quot;854&quot;,
      &quot;label&quot; : &quot;854 - ISTRUZIONE POST-SECONDARIA UNIVERSITARIA E NON UNIVERSITARIA&quot;
    }, {
      &quot;value&quot; : &quot;855&quot;,
      &quot;label&quot; : &quot;855 - Attività delle scuole guida&quot;
    }, {
      &quot;value&quot; : &quot;856&quot;,
      &quot;label&quot; : &quot;856 - Attività di supporto allistruzione&quot;
    }, {
      &quot;value&quot; : &quot;861&quot;,
      &quot;label&quot; : &quot;861 - Servizi ospedalieri&quot;
    }, {
      &quot;value&quot; : &quot;862&quot;,
      &quot;label&quot; : &quot;862 - Servizi degli studi medici di medicina generale&quot;
    }, {
      &quot;value&quot; : &quot;869&quot;,
      &quot;label&quot; : &quot;869 - Altri servizi di assistenza sanitaria&quot;
    }, {
      &quot;value&quot; : &quot;871&quot;,
      &quot;label&quot; : &quot;871 - Strutture di assistenza infermieristica residenziale&quot;
    }, {
      &quot;value&quot; : &quot;872&quot;,
      &quot;label&quot; : &quot;872 - Strutture di assistenza residenziale per persone affette da ritardi mentali. disturbi mentali o che abusano di sostanze stupefacenti&quot;
    }, {
      &quot;value&quot; : &quot;873&quot;,
      &quot;label&quot; : &quot;873 - Strutture di assistenza residenziale per anziani e disabili&quot;
    }, {
      &quot;value&quot; : &quot;879&quot;,
      &quot;label&quot; : &quot;879 - Altre strutture di assistenza sociale residenziale&quot;
    }, {
      &quot;value&quot; : &quot;881&quot;,
      &quot;label&quot; : &quot;881 - Assistenza sociale non residenziale per anziani e disabili&quot;
    }, {
      &quot;value&quot; : &quot;889&quot;,
      &quot;label&quot; : &quot;889 - ALTRE ATTIVITÀ DI ASSISTENZA SOCIALE NON RESIDENZIALE&quot;
    }, {
      &quot;value&quot; : &quot;900&quot;,
      &quot;label&quot; : &quot;900 - Creazioni artistiche e letterarie&quot;
    }, {
      &quot;value&quot; : &quot;910&quot;,
      &quot;label&quot; : &quot;910 - Attività degli orti botanici. dei giardini zoologici e delle riserve naturali&quot;
    }, {
      &quot;value&quot; : &quot;920&quot;,
      &quot;label&quot; : &quot;920 - ATTIVITÀ RIGUARDANTI LE LOTTERIE, LE SCOMMESSE, LE CASE DA GIOCO&quot;
    }, {
      &quot;value&quot; : &quot;931&quot;,
      &quot;label&quot; : &quot;931 - Altre attività sportive&quot;
    }, {
      &quot;value&quot; : &quot;932&quot;,
      &quot;label&quot; : &quot;932 - Altre attività ricreative e di divertimento&quot;
    }, {
      &quot;value&quot; : &quot;941&quot;,
      &quot;label&quot; : &quot;941 - ATTIVITÀ DI ORGANIZZAZIONI ECONOMICHE. DI DATORI DI LAVORO E PROFESSIONALI&quot;
    }, {
      &quot;value&quot; : &quot;942&quot;,
      &quot;label&quot; : &quot;942 - ATTIVITÀ DEI SINDACATI DI LAVORATORI DIPENDENTI&quot;
    }, {
      &quot;value&quot; : &quot;949&quot;,
      &quot;label&quot; : &quot;949 - Attività di altre organizzazioni associative nca&quot;
    }, {
      &quot;value&quot; : &quot;951&quot;,
      &quot;label&quot; : &quot;951 - RIPARAZIONE DI COMPUTER E DI APPARECCHIATURE PER LE COMUNICAZIONI&quot;
    }, {
      &quot;value&quot; : &quot;952&quot;,
      &quot;label&quot; : &quot;952 - Riparazione di altri beni per uso personale e per la casa&quot;
    }, {
      &quot;value&quot; : &quot;960&quot;,
      &quot;label&quot; : &quot;960 - Servizi di pompe funebri e attività connesse&quot;
    }, {
      &quot;value&quot; : &quot;970&quot;,
      &quot;label&quot; : &quot;970 - ATTIVITÀ DI FAMIGLIE E CONVIVENZE COME DATORI DI LAVORO PER PERSONALE DOMESTICO&quot;
    }, {
      &quot;value&quot; : &quot;981&quot;,
      &quot;label&quot; : &quot;981 - PRODUZIONE DI BENI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;
    }, {
      &quot;value&quot; : &quot;982&quot;,
      &quot;label&quot; : &quot;982 - PRODUZIONE DI SERVIZI INDIFFERENZIATI PER USO PROPRIO DA PARTE DI FAMIGLIE E CONVIVENZE&quot;
    }, {
      &quot;value&quot; : &quot;990&quot;,
      &quot;label&quot; : &quot;990 - ORGANIZZAZIONI ED ORGANISMI EXTRATERRITORIALI&quot;
    } ],
    &quot;SAEValuesCons&quot; : [ {
      &quot;value&quot; : &quot;600&quot;,
      &quot;label&quot; : &quot;600 - FAMIGLIE CONSUMATRICI&quot;
    } ],
    &quot;ATECOValuesCons&quot; : [ {
      &quot;value&quot; : &quot;000&quot;,
      &quot;label&quot; : &quot;000 - SCONOSCIUTO&quot;
    } ]
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformActivityCodes</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica:ActivityValues&quot;,
  &quot;sendJSONPath&quot; : &quot;ActivityCodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformSummaryCodes</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;ExtractCodActivity:Modifica:SummaryValues&quot;,
  &quot;sendJSONPath&quot; : &quot;SummaryCodes&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DACodeToValues&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessKey>CodiceAttivita_GetData</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>GetData</subType>
    <type>CodiceAttivita</type>
    <uniqueName>CodiceAttivita_GetData_Procedure_6</uniqueName>
    <versionNumber>6.0</versionNumber>
    <webComponentKey>5397cccd-4981-fc8c-b6ae-0d41fe8c1fec</webComponentKey>
</OmniIntegrationProcedure>
