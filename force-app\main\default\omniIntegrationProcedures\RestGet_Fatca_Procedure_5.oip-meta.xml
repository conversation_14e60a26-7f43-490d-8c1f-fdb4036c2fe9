<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{
    &quot;RecordId&quot;: &quot;a1i9V000006bdVBQAY&quot;,
    &quot;ActionType&quot;: &quot;GetLocalita&quot;,
    &quot;SiglaProvincia&quot;: &quot;RC&quot;,
    &quot;CodiceBelfioreComune&quot;: &quot;H501&quot;,
    &quot;Type&quot;: &quot;PG&quot;,
    &quot;UserId&quot;: &quot;0059X00000Jr08cQAB&quot;
}</customJavaScript>
    <description>Lorenzo Scrufari: Refactoring chiamate HTTP in corso, scrivetemi se da modificare DONE: PreLoadInserimento PreLoad (PF+PG) PreLoadInserimentoPg, Comuni, Localita</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>Rest Get Fatca</name>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseChangePreVal</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActualCittadinanzeString% LIKE \&quot;536\&quot; || %PoteriFirmaUSA% == true&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;ResidenzaFiscaleUSA&quot; : true,
    &quot;DisableResidenzaUSA&quot; : false,
    &quot;ErrorType&quot; : 0,
    &quot;Status&quot; : true
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ChangeResidenza</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;ChangeResidenza\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>12.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseChangePreEst</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActualCittadinanzeString% LIKE \&quot;536\&quot; || %PoteriFirmaEstero% == true&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;ResidenzaFiscaleEstero&quot; : true,
    &quot;DisableResidenzaEstero&quot; : false,
    &quot;ErrorType&quot; : 0,
    &quot;Status&quot; : true
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ChangeResidenzaEstera</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;ChangeResidenzaEstera\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>13.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCheckCittKo</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%CittadinanzaUSA% = false &amp;&amp; ((%ActualCittadinanzeString% LIKE \&quot;536\&quot; || %ResidenzaFiscaleUSA% = true) || (%PaeseDiNascitaUSA% = true || %CodiceFiscale% LIKE \&quot;Z404\&quot;))&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : false,
    &quot;ErrorType&quot; : 1,
    &quot;Message&quot; : &quot;=\&quot;Cittadinanza negli U.S.A. / possesso Green Card : dati non congruenti con informazioni anagrafiche\&quot;&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCheckCittOk</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : true,
    &quot;ErrorType&quot; : 0
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckCittadinanza</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;CheckCittadinanza\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>11.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCheckResKo</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActualCittadinanzeString% LIKE \&quot;536\&quot; &amp;&amp; ResidenzaFiscaleUSA = false&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : false,
    &quot;ErrorType&quot; : 2,
    &quot;Message&quot; : &quot;=\&quot;Residenza Fiscale U.S.A. : dati non congruenti con informazioni anagrafiche\&quot;&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCheckResKo2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;CittadinanzaUSA = false &amp;&amp; ResidenzaFiscaleUSA = true&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : false,
    &quot;ErrorType&quot; : 1,
    &quot;Message&quot; : &quot;=\&quot;Cittadinanza negli U.S.A. / possesso Green Card: dati non congruenti con informazioni anagrafiche&apos;\&quot;&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseCheckResOk</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;Status&quot; : true,
    &quot;ErrorType&quot; : 0
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckResidenza</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;CheckResidenza\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>10.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetOptionsComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionsComuni&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetComuni&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;codProv&quot;,
    &quot;element&quot; : &quot;SiglaProvincia&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPOptionsComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPOptionsComuniInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;Resp&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;limiteOccorrenze&quot; : 150,
      &quot;siglaProvincia&quot; : &quot;%SiglaProvincia%&quot;,
      &quot;soloAttivi&quot; : true
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction3</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;OptionsComuni&quot; : &quot;=%OptionsComuni%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransComuni</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TransformOptionsComuni&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Comuni</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;GetOptions\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetUserInfo</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;RecordId&quot; : &quot;=%RecordId%&quot;,
    &quot;UserId&quot; : &quot;=%UserId%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UtilGetUserInfo&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction15&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetLocalita2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;GetLocalita\&quot;&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(%SetLocalitaCalloutParams%)&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;response&quot; : &quot;=DESERIALIZE(%GetLocalita2:result%)&quot;,
    &quot;StatusCode&quot; : &quot;=%GetLocalita2:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Integration Procedure Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPOptionsLocalita</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPOptionsLocalita:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;Resp&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/localita&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfioreComune&quot; : &quot;%CodiceBelfioreComune%&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;limiteOccorrenze&quot; : 99,
      &quot;siglaProvincia&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseAction4</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;TransLocalita&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetLocalitaCalloutParams</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;QueryParam&quot; : &quot;=%SetLocalitaQueryParams%&quot;,
    &quot;HeaderParams&quot; : &quot;=%SetHeader%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SetLocalitaCalloutParams:Params&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>SetLocalitaQueryParams</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;codiceBelfioreComune&quot; : &quot;%CodiceBelfioreComune%&quot;,
    &quot;descrizione&quot; : &quot;&quot;,
    &quot;limiteOccorrenze&quot; : &quot;=99&quot;,
    &quot;siglaProvincia&quot; : &quot;&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransLocalita</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Resp&quot; : &quot;=%GetLocalita2:response%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TransformOptionLocalita&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Localita</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;GetLocalita\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPNorm</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPNormInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/indirizziNormalizzati&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfioreComune&quot; : &quot;=%CodiceBelfioreComune%&quot;,
      &quot;indirizzo&quot; : &quot;=%IndirizzoCompleto%&quot;,
      &quot;siglaProvincia&quot; : &quot;=%SiglaProvincia%&quot;,
      &quot;civico&quot; : &quot;=%NumeroCivico%&quot;,
      &quot;comune&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <isActive>false</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Normalizzazione</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;GetNormal\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>AddErrorTypeAndMessage</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;ErrorType&quot; : &quot;=%GetFatcaService:response:error:errorPayload:response:error:errorPayload:response:codice%&quot;,
    &quot;ErrorMessage&quot; : &quot;=%GetFatcaService:response:error:errorPayload:response:error:errorPayload:response:messaggio%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;GetFatcaService:response&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Set Values</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetFatcaService</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;getDatiFatca\&quot;&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(%SetFatcaCalloutParams%)&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;response&quot; : &quot;=DESERIALIZE(%GetFatcaService:result%)&quot;,
    &quot;response:StatusCode&quot; : &quot;=%GetFatcaService:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction1&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : {
    &quot;PROVA&quot; : &quot;=\&quot;PROVA\&quot;&quot;
  }
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Integration Procedure Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetFatcaCalloutParams</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;HeaderParams&quot; : &quot;=%SetHeader%&quot;,
    &quot;PathParam&quot; : &quot;=%SetFatcaPathParams%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SetFatcaCalloutParams:Params&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>SetFatcaPathParams</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;compagnia&quot; : &quot;=IF(ISBLANK(%RetriveAccountDetailData:Societa%),\&quot;\&quot;,%RetriveAccountDetailData:Societa%)&quot;,
    &quot;ciu&quot; : &quot;=IF(ISBLANK(%RetriveAccountDetailData:SourceSystemIdentifier%),\&quot;\&quot;,%RetriveAccountDetailData:SourceSystemIdentifier%)&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ChiamataFatcaLoad</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;true&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock14&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetAllStati</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Active&quot; : &quot;=true&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;GetAllStati&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTurboAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>11.0</sequenceNumber>
            <type>DataRaptor Turbo Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetAllStatiLoad</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionsStati&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>12.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>HTTPAction</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;%HTTPActionInfo:statusCode%&quot;,
    &quot;ErrorType&quot; : &quot;%HTTPAction:error:errorPayload:response:error:errorPayload:response:codice%&quot;,
    &quot;ErrorMessage&quot; : &quot;%HTTPAction:error:errorPayload:response:error:errorPayload:response:messaggio%&quot;
  },
  &quot;failureResponse&quot; : {
    &quot;&quot; : &quot;&quot;
  },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/compagnie/%RetriveAccountDetailData:Societa%/anagrafiche/%RetriveAccountDetailData:SourceSystemIdentifier%/fatca&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : { },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : false,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;%RetriveNamedCred:DeveloperName%&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;ERROR LOG&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStati</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;GetStatiByCode&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;Code&quot;,
    &quot;element&quot; : &quot;HTTPAction:residenzeFiscaliEstere:residenzaFiscaleEstera&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetStatiLoad</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;Ue&quot; : &quot;=QUERY( \&quot;SELECT isUE__c FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:residenzeFiscaliEstere:residenzaFiscaleEstera%  )&quot;,
    &quot;Attivo&quot; : &quot;=true&quot;,
    &quot;CodiceBelfiore&quot; : &quot;=QUERY( \&quot;SELECT Codice_belfiore__c FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:residenzeFiscaliEstere:residenzaFiscaleEstera%  )&quot;,
    &quot;Descrizione&quot; : &quot;=QUERY( \&quot;SELECT Name FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:residenzeFiscaliEstere:residenzaFiscaleEstera%  )&quot;,
    &quot;CodiceABI&quot; : &quot;=QUERY( \&quot;SELECT Codice_ABI__c FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:residenzeFiscaliEstere:residenzaFiscaleEstera%  )&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>Set Values</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Loop</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;loopList&quot; : &quot;GetFatcaService:response:residenzeFiscaliEstere&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;loopOutput&quot; : {
    &quot;ResidenzaFiscaleEsteraCode&quot; : &quot;=%GetFatcaService:response:residenzeFiscaliEstere:residenzaFiscaleEstera%&quot;,
    &quot;ResidenzaFiscale&quot; : &quot;=%GetStatiLoad%&quot;,
    &quot;NumeroIdentificazioneFiscale&quot; : &quot;=%GetFatcaService:response:residenzeFiscaliEstere:numeroIdentificazioneFiscale%&quot;,
    &quot;FlagIdentificazione&quot; : &quot;=IF(%GetFatcaService:response:residenzeFiscaliEstere:numeroIdentificazioneFiscale%, true, false)&quot;
  },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;LoopBlock2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>13.0</sequenceNumber>
            <type>Loop Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ResponseAction</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;success&quot; : &quot;=true&quot;,
    &quot;insert&quot; : false
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;FinalCheck&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>Response Action</type>
            </childElements>
            <childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>FinalCheck</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;Response&quot; : &quot;=DESERIALIZE(%FinalCheck:response%)&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;TransformResponse&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;CheckDatiFatca&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;checkModal&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>3.0</sequenceNumber>
                    <type>Remote Action</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>TransformResponse</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Loop&quot; : &quot;=%Loop%&quot;,
    &quot;DettagliModifica&quot; : &quot;=%RetriveAccountDetailData%&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;OptionsCittadinanze&quot; : &quot;=%OptionsCittadinanze%&quot;,
    &quot;OptionsStati&quot; : &quot;=%OptionsStati%&quot;,
    &quot;Estero:OptionsStati&quot; : &quot;=%OptionsStati%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;GetFatcaService:response&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DatiFatcaTrasformResponse&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>2.0</sequenceNumber>
                    <type>DataRaptor Transform Action</type>
                </childElements>
                <childElements>
                    <isActive>false</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>TransformResponseOLD</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Loop&quot; : &quot;=%Loop%&quot;,
    &quot;DettagliModifica&quot; : &quot;=%RetriveAccountDetailData%&quot;,
    &quot;Stati&quot; : &quot;=%GetAllStati%&quot;,
    &quot;AllCittadinanze&quot; : &quot;=%HTTPCittadinanze%&quot;,
    &quot;AllStati&quot; : &quot;=%GetAllStati%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;DatiFatcaTrasformResponse&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>1.0</sequenceNumber>
                    <type>DataRaptor Transform Action</type>
                </childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TryCatchBlock2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;failureResponse&quot; : {
    &quot;success&quot; : &quot;=false&quot;
  },
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnBlockError&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;TryCatchBlock2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Try Catch Block</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>PersonaFisica</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%Type% == \&quot;PF\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>14.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>ResponseAction2</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;success&quot; : &quot;=true&quot;,
    &quot;insert&quot; : false
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;FinalCheckPg&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>8.0</sequenceNumber>
                <type>Response Action</type>
            </childElements>
            <childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>FinalCheckPg</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;Response&quot; : &quot;=DESERIALIZE(%FinalCheckPg:response%)&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;TransformeResponsePG&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;CheckDatiFatca&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;checkModalPG&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;RemoteAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>8.0</sequenceNumber>
                    <type>Remote Action</type>
                </childElements>
                <childElements>
                    <isActive>false</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>GetComuni</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction:indirizzo:codiceBelfioreStato% == \&quot;Z000\&quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%GetComuniInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfiore&quot; : &quot;%HTTPAction:indirizzo:codiceBelfioreComune%&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;limiteOccorrenze&quot; : 121,
      &quot;siglaProvincia&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>1.0</sequenceNumber>
                    <type>Rest Action</type>
                </childElements>
                <childElements>
                    <isActive>false</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>GetProvince</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%HTTPAction:indirizzo:codiceBelfioreStato% == \&quot;Z000\&quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%GetProvinceInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceISTATRegione&quot; : &quot;%GetComuni:codiceISTATRegione%&quot;,
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;%GetComuni:siglaProvincia%&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>2.0</sequenceNumber>
                    <type>Rest Action</type>
                </childElements>
                <childElements>
                    <isActive>false</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>GetStato</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;GetStatiByCode&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;Code&quot;,
    &quot;element&quot; : &quot;HTTPAction:indirizzo:codiceBelfioreStato&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>9.0</sequenceNumber>
                    <type>DataRaptor Extract Action</type>
                </childElements>
                <childElements>
                    <childElements>
                        <isActive>false</isActive>
                        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                        <level>4.0</level>
                        <name>GetLocalita</name>
                        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%GetLocalitaInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/localita&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfioreComune&quot; : &quot;%GetFatcaService:response:indirizzo:codiceBelfioreComune%&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;limiteOccorrenze&quot; : 99,
      &quot;siglaProvincia&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                        <sequenceNumber>1.0</sequenceNumber>
                        <type>Rest Action</type>
                    </childElements>
                    <childElements>
                        <isActive>true</isActive>
                        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                        <level>4.0</level>
                        <name>GetLocalitaLoad</name>
                        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;integrationId&quot; : &quot;=\&quot;GetLocalita\&quot;&quot;,
    &quot;params&quot; : &quot;=SERIALIZE(%SetLocalitaPGCalloutParams%)&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;response&quot; : &quot;=DESERIALIZE(%GetLocalitaLoad:result%)&quot;,
    &quot;StatusCode&quot; : &quot;=%GetLocalitaLoad:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;IntegrationService_IntegrationService&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;disableChainable&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;IntegrationProcedureAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                        <sequenceNumber>4.0</sequenceNumber>
                        <type>Integration Procedure Action</type>
                    </childElements>
                    <childElements>
                        <isActive>true</isActive>
                        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                        <level>4.0</level>
                        <name>SetLocalitaPGCalloutParams</name>
                        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;QueryParam&quot; : &quot;=%SetLocalitaPGQueryParams%&quot;,
    &quot;HeaderParams&quot; : &quot;=%SetHeader%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;SetLocalitaPGCalloutParams:Params&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                        <sequenceNumber>3.0</sequenceNumber>
                        <type>Set Values</type>
                    </childElements>
                    <childElements>
                        <isActive>true</isActive>
                        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                        <level>4.0</level>
                        <name>SetLocalitaPGQueryParams</name>
                        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;codiceBelfioreComune&quot; : &quot;=%GetFatcaService:response:indirizzo:codiceBelfioreComune%&quot;,
    &quot;descrizione&quot; : &quot;&quot;,
    &quot;limiteOccorrenze&quot; : &quot;=99&quot;,
    &quot;siglaProvincia&quot; : &quot;&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues7&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                        <sequenceNumber>2.0</sequenceNumber>
                        <type>Set Values</type>
                    </childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>ifGetLocalita</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%GetFatcaService:response:indirizzo:codiceBelfioreStato% == \&quot;Z000\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock13&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>3.0</sequenceNumber>
                    <type>Conditional Block</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>TransformeResponsePG</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;DettagliModifica&quot; : &quot;=%RetriveAccountDetailData%&quot;,
    &quot;Loop&quot; : &quot;=%Loop%&quot;,
    &quot;AllLocalita&quot; : &quot;=%GetLocalitaLoad:response%&quot;,
    &quot;Comune:descrizione&quot; : &quot;=QUERY( \&quot;SELECT Name FROM Unita_Territoriali__c WHERE Codice_catasto__c\t= &apos;{0}&apos; and Codice_catasto__c\t!= null and Recordtype.Name = &apos;Comune&apos; limit 1\&quot;, %GetFatcaService:response:indirizzo:codiceBelfioreComune%  )&quot;,
    &quot;Comune:descrizioneProvincia&quot; : &quot;=QUERY( \&quot;SELECT Name FROM Unita_Territoriali__c WHERE Sigla_automobilistica__c = &apos;{0}&apos; and Sigla_automobilistica__c != null and Recordtype.Name = &apos;Provincia&apos; limit 1\&quot;, %GetFatcaService:response:indirizzo:abbreviazioneProvincia%  )&quot;,
    &quot;Provincia:sigla&quot; : &quot;=QUERY( \&quot;SELECT Name FROM Unita_Territoriali__c WHERE Sigla_automobilistica__c = &apos;{0}&apos; and Sigla_automobilistica__c != null and Recordtype.Name = &apos;Provincia&apos; limit 1\&quot;, %GetFatcaService:response:indirizzo:abbreviazioneProvincia%  )&quot;,
    &quot;Stato:CodiceBelfiore&quot; : &quot;=QUERY( \&quot;SELECT Codice_belfiore__c FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:indirizzo:codiceBelfioreStato%  )&quot;,
    &quot;Stato:Descrizione&quot; : &quot;=QUERY( \&quot;SELECT Name FROM Unita_Territoriali__c WHERE Codice_belfiore__c = &apos;{0}&apos; and Codice_belfiore__c != null and Recordtype.Name = &apos;Nazione&apos; limit 1\&quot;, %GetFatcaService:response:indirizzo:codiceBelfioreStato%  )&quot;
  },
  &quot;additionalOutput&quot; : {
    &quot;Options&quot; : &quot;=%OptionsStati%&quot;,
    &quot;Estero:OptionsStati&quot; : &quot;=%OptionsStati%&quot;,
    &quot;OptionsStati&quot; : &quot;=%OptionsStati%&quot;,
    &quot;OptionProvince&quot; : &quot;=%OptionProvince%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;GetFatcaService:response&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TrasformeResponsePG&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>7.0</sequenceNumber>
                    <type>DataRaptor Transform Action</type>
                </childElements>
                <childElements>
                    <isActive>false</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>TransformeResponsePGOLD</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Comune&quot; : &quot;=%GetComuni%&quot;,
    &quot;DettagliModifica&quot; : &quot;=%RetriveAccountDetailData%&quot;,
    &quot;Provincia&quot; : &quot;=%GetProvince%&quot;,
    &quot;Stato&quot; : &quot;=%GetStato%&quot;,
    &quot;Loop&quot; : &quot;=%Loop%&quot;,
    &quot;Stati&quot; : &quot;=%GetAllStati%&quot;,
    &quot;Allprovince&quot; : &quot;=%HTTPGetProvince%&quot;,
    &quot;AllComuni&quot; : &quot;=%HTTPGetComuni%&quot;,
    &quot;AllLocalita&quot; : &quot;=%GetLocalita%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;HTTPAction&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TrasformeResponsePG&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>10.0</sequenceNumber>
                    <type>DataRaptor Transform Action</type>
                </childElements>
                <childElements>
                    <isActive>true</isActive>
                    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                    <level>3.0</level>
                    <name>ValuesCodici</name>
                    <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                    <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;codBelfioreComune&quot; : &quot;%GetFatcaService:response:indirizzo:codiceBelfioreComune%&quot;,
    &quot;codBelfioreStato&quot; : &quot;%GetFatcaService:response:indirizzo:codiceBelfioreStato%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                    <sequenceNumber>4.0</sequenceNumber>
                    <type>Set Values</type>
                </childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>TryCatchBlock3</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;failureResponse&quot; : {
    &quot;success&quot; : &quot;=false&quot;
  },
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnBlockError&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;TryCatchBlock3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Try Catch Block</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>PersonaGiuridica</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%Type% == \&quot;PG\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>15.0</sequenceNumber>
            <type>Conditional Block</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseErrorKO</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%GetFatcaService:response:StatusCode% &gt; 300 &amp;&amp; %GetFatcaService:response:ErrorType% != \&quot;BUSINESS_ERROR\&quot; &quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;success&quot; : false
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponseErrorNoRecordFound</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%GetFatcaService:response:StatusCode% &gt; 300 &amp;&amp; %GetFatcaService:response:ErrorType% = \&quot;BUSINESS_ERROR\&quot; &quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;success&quot; : true,
    &quot;insert&quot; : true
  },
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction11&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>9.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>RetriveAccountDetailData</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetriveAccountDetailById&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;RecordId&quot;,
    &quot;element&quot; : &quot;RecordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetCittadinanzeLoad</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionsCittadinanze&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetCittadinanze&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>2.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>true</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>GetProvinceLoad</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionProvince&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction14&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>4.0</sequenceNumber>
                <type>DataRaptor Extract Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPCittadinanze</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPCittadinanze:StatusCode% &gt; 300&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPCittadinanzeInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/cittadinanze&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : { },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;Errore Cittadinanze&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>1.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPGetComuni</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPGetComuni:StatusCode% &gt; 300&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPGetComuniInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/comuni&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;limiteOccorrenze&quot; : 150,
      &quot;siglaProvincia&quot; : &quot;%HTTPAction:indirizzo:abbreviazioneProvincia%&quot;,
      &quot;soloAttivi&quot; : true
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>5.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPGetProvince</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPCittadinanze:StatusCode% &gt; 300&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPGetProvinceInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceISTATRegione&quot; : &quot;&quot;,
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>3.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <childElements>
                <isActive>false</isActive>
                <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
                <level>2.0</level>
                <name>HTTPStati</name>
                <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
                <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPGetComuni:StatusCode% &gt; 300&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPGetComuniInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;filtroGeografico&quot; : &quot;DISATTIVO&quot;,
      &quot;soloAttivi&quot; : true
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
                <sequenceNumber>6.0</sequenceNumber>
                <type>Rest Action</type>
            </childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TryCatchBlock1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;failureResponse&quot; : {
    &quot;success&quot; : &quot;=false&quot;
  },
  &quot;remoteClass&quot; : &quot;&quot;,
  &quot;remoteMethod&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnBlockError&quot; : true,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;TryCatchBlock1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>10.0</sequenceNumber>
            <type>Try Catch Block</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>PreLoad</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;Preload\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetAllStati2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Active&quot; : &quot;=true&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;GetAllStati&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTurboAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Turbo Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetCittadinanze</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;%HTTPCittadinanze:StatusCode% &gt; 300&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%HTTPCittadinanzeInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/cittadinanze&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : { },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;Errore Cittadinanze&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction10&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetCittadinanzePreLoad</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionsCittadinanze&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetCittadinanze&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction8&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetStatiPreLoad</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionsStati&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsePreLoadInserimento</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;OptionsStati&quot; : &quot;=%OptionsStati%&quot;,
    &quot;OptionsCittadinanze&quot; : &quot;=%OptionsCittadinanze%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;TransformInserimento&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction12&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>8.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>RetriveAccDetailData</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetriveAccountDetailById&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;RecordId&quot;,
    &quot;element&quot; : &quot;RecordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformInserimento</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Cittadinanze&quot; : &quot;%GetCittadinanze%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;RetriveAccDetailData&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TransformFatcaInsert&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction5&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>PreLoadInserimento</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;PreLoadInserimento\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock9&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetProvince2</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%GetProvinceInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/province&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceISTATRegione&quot; : &quot;%GetComuni:codiceISTATRegione%&quot;,
      &quot;codiceUnipolRegione&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;sigla&quot; : &quot;%GetComuni:siglaProvincia%&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>4.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetProvincePreLoadPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;OptionProvince&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetProvince&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction12&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>5.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetStatiPreLoadPG</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;options&quot;,
  &quot;responseJSONNode&quot; : &quot;Options&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;UnipolGetStati&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction1&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>3.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>false</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>GetStatiService</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : {
    &quot;StatusCode&quot; : &quot;=%GetProvinceInfo:statusCode%&quot;
  },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;%endpoint%&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;restPath&quot; : &quot;/api/v1/stati&quot;,
  &quot;restMethod&quot; : &quot;GET&quot;,
  &quot;retryCount&quot; : 0,
  &quot;restOptions&quot; : {
    &quot;headers&quot; : { },
    &quot;params&quot; : {
      &quot;codiceBelfiore&quot; : &quot;&quot;,
      &quot;descrizione&quot; : &quot;&quot;,
      &quot;soloAttivi&quot; : true,
      &quot;codiceABI&quot; : &quot;&quot;,
      &quot;filtroGeografico&quot; : &quot;&quot;
    },
    &quot;timeout&quot; : 0,
    &quot;sendBody&quot; : true,
    &quot;xmlEscapeResponse&quot; : false,
    &quot;clientCertificateName&quot; : &quot;&quot;,
    &quot;isCompressed&quot; : false
  },
  &quot;namedCredential&quot; : &quot;ExperienceFSCAPI&quot;,
  &quot;type&quot; : &quot;Integration&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;HTTPAction3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>Rest Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>ResponsePreLoadInserimentoPg</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;additionalOutput&quot; : {
    &quot;OptionProvince&quot; : &quot;=%OptionProvince%&quot;,
    &quot;Options&quot; : &quot;=%Options%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnFullDataJSON&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;TransformInserimentoPG1&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;responseDefaultData&quot; : { },
  &quot;vlcResponseHeaders&quot; : { },
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ResponseAction15&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>7.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>RetriveAccDetailDataPg</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetriveAccountDetailById&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ {
    &quot;inputParam&quot; : &quot;RecordId&quot;,
    &quot;element&quot; : &quot;RecordId&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction2&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>DataRaptor Extract Action</type>
        </childElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>TransformInserimentoPG1</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Data&quot; : &quot;%RetriveAccDetailDataPg%&quot;
  },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;TransformInserimentoPG&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperTransformAction6&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
            <sequenceNumber>6.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>PreLoadInserimentoPg</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;%ActionType% = \&quot;PreLoadInserimentoPg\&quot;&quot;,
  &quot;isIfElseBlock&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;ConditionalBlock12&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>9.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RetriveNamedCred</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;bundle&quot; : &quot;RetriveNamedCredential&quot;,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;DataMapperExtractAction4&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>SetHeader</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;elementValueMap&quot; : {
    &quot;X-CHANNEL-SUBJECT&quot; : &quot;=%GetUserInfo:userId%&quot;,
    &quot;X-CHANNEL-SUBJECT-NAME&quot; : &quot;=%GetUserInfo:username%&quot;
  },
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;show&quot; : null,
  &quot;label&quot; : &quot;SetValues3&quot;,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>RestGet_Fatca</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { }
}</propertySetConfig>
    <subType>Fatca</subType>
    <type>RestGet</type>
    <uniqueName>RestGet_Fatca_Procedure_5</uniqueName>
    <versionNumber>5.0</versionNumber>
    <webComponentKey>636d15b3-8174-39d3-524f-89b607d39fa8</webComponentKey>
</OmniIntegrationProcedure>
