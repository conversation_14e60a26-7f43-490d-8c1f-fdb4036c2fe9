<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;policies&quot;:[{&quot;agency&quot;:&quot;0019X00000sfj5iQAA&quot;,&quot;rtDeveloperName&quot;:&quot;PU_POSITION&quot;},{&quot;agency&quot;:&quot;0019X00000sfj5iQAA&quot;,&quot;rtDeveloperName&quot;:&quot;PASS_UNISALUTE_PER_TE&quot;}],&quot;size&quot;:&quot;2&quot;}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>English</language>
    <name>UniDSGetBadgeProdotti</name>
    <omniProcessElements>
        <description>List Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>List</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;preventIntraListMerge&quot; : false,
  &quot;isActive&quot; : true,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;mergeListsOrder&quot; : [ &quot;Loop Block:Transform&quot;, &quot;Loop Block:Transform&quot; ],
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;filterListFormula&quot; : &quot;&quot;,
  &quot;primaryListKey&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;advancedMerge&quot; : false,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;sortInDescendingOrder&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;hasPrimary&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;postActionLogging&quot; : &quot;&quot;,
  &quot;preActionLogging&quot; : &quot;&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;dynamicOutputFields&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;allowMergeNulls&quot; : false,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;mergeFields&quot; : [ &quot;textDescription&quot;, &quot;resourceName&quot; ]
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>List Merge Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <childElements>
            <description>Decision Matrix Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Decision Matrix</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;remoteOptions&quot; : {
    &quot;matrixName&quot; : &quot;BadgeProdotti&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;matrix Input Parameters&quot; : [ {
    &quot;value&quot; : &quot;RecordType&quot;,
    &quot;name&quot; : &quot;Set List Policies:policiesList:rtDeveloperName&quot;
  } ],
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Matrix Action</type>
        </childElements>
        <childElements>
            <description>Data Mapper Transform Action</description>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Transform</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : &quot;&quot;,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;%Decision Matrix:result%&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UniDSTransformBadge&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>2.0</sequenceNumber>
            <type>DataRaptor Transform Action</type>
        </childElements>
        <description>Loop Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Loop Block</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;loopList&quot; : &quot;Set List Policies:policiesList&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;loopOutput&quot; : {
    &quot;Transform&quot; : &quot;%Transform%&quot;
  }
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>Loop Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Response Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Response</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;returnFullDataJSON&quot; : false,
  &quot;additionalOutput&quot; : {
    &quot;resp&quot; : &quot;=%List%&quot;
  },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;responseFormat&quot; : &quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Set Values Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Set Index</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;CountIndex&quot;,
  &quot;responseJSONNode&quot; : &quot;index&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;CountIndex&quot; : 0.0
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessElements>
        <description>Set Values Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Set List Policies</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;elementValueMap&quot; : {
    &quot;policiesList&quot; : &quot;=LIST(policies)&quot;
  },
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;chainOnStep&quot; : false
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Set Values</type>
    </omniProcessElements>
    <omniProcessKey>UniDS_BadgeProdotti</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;deactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>BadgeProdotti</subType>
    <type>UniDS</type>
    <uniqueName>UniDS_BadgeProdotti_English_1</uniqueName>
    <versionNumber>1.0</versionNumber>
</OmniIntegrationProcedure>
