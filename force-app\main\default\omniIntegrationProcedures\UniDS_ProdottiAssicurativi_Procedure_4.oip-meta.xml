<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{&quot;AccountId&quot;:&quot;0019X00001Ev2MIQAZ&quot;,&quot;showOtherAgency&quot;:&quot;FALSE&quot;}</customJavaScript>
    <description>Stefano &amp; <PERSON>e Refactoring</description>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>GetProdottiAssicurativi</name>
    <omniProcessElements>
        <description>Integration Procedure Action</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>BadgeProdotti</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;disableChainable&quot; : false,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;integrationProcedureKey&quot; : &quot;UniDS_BadgeProdotti&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;size&quot; : &quot;=LISTSIZE(GetAccountInsurancePolicies:policies)&quot;,
    &quot;policies&quot; : &quot;=LIST(GetAccountInsurancePolicies:policies)&quot;
  },
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Integration Procedure Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckCustomPermissions</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;checkCustomPermissions&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;checkCp&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;customPermissions&quot; : [ &quot;X169_506000000&quot;, &quot;X172_201030200&quot;, &quot;X172_201030300&quot;, &quot;X172_201030400&quot;, &quot;X172_201030500&quot;, &quot;X172_201030600&quot;, &quot;X172_201030700&quot;, &quot;X172_201030800&quot;, &quot;X172_201030900&quot;, &quot;X172_201031000&quot;, &quot;X172_202020000&quot;, &quot;X172_202020100&quot;, &quot;X172_202020101&quot;, &quot;X172_202020102&quot;, &quot;X172_202020103&quot;, &quot;X172_202020104&quot;, &quot;X172_202020105&quot;, &quot;X172_202020500&quot;, &quot;X172_202020501&quot;, &quot;X172_202020502&quot;, &quot;X172_202020503&quot;, &quot;X172_202020504&quot;, &quot;X172_202150504&quot;, &quot;X172_202150600&quot;, &quot;X172_202020600&quot;, &quot;X172_202150000&quot;, &quot;X172_202150101&quot;, &quot;X172_202150102&quot;, &quot;X172_202150100&quot;, &quot;X172_202150103&quot;, &quot;X172_202150104&quot;, &quot;X172_202150105&quot;, &quot;X172_202150106&quot;, &quot;X172_202150107&quot;, &quot;X172_202150200&quot;, &quot;X172_202150300&quot;, &quot;X172_202150301&quot;, &quot;X172_202150302&quot;, &quot;X172_202150303&quot;, &quot;X172_202150304&quot;, &quot;X172_202150305&quot;, &quot;X172_202150400&quot;, &quot;X172_202150500&quot;, &quot;X172_202150501&quot;, &quot;X172_202150502&quot;, &quot;X172_202150503&quot;, &quot;X174_201000000&quot;, &quot;X174_202000000&quot;, &quot;X182_201010000&quot;, &quot;X182_201020000&quot;, &quot;X182_202030000&quot;, &quot;X182_202120000&quot;, &quot;X182_202010000&quot;, &quot;X182_202140000&quot;, &quot;X182_202200100&quot;, &quot;X182_202200200&quot;, &quot;X182_202210100&quot;, &quot;X182_202210200&quot;, &quot;X182_206110100&quot;, &quot;X182_206110200&quot; ]
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>CheckIsAbbinato</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;checkAbbinato&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;isAbbinato&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;OmnistudioUtils&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;accountId&quot; : &quot;%AccountId%&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GenericLog</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;remoteOptions&quot; : { },
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;remoteMethod&quot; : &quot;call&quot;,
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;UniLogIntegrationProcedureSubmit&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;additionalChainableResponse&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;Message&quot; : &quot;ProdottiAssicurativi&quot;,
    &quot;ClassName&quot; : &quot;PAInsurance&quot;,
    &quot;Payload&quot; : &quot;%GetAccountInsurancePolicies%&quot;,
    &quot;MethodName&quot; : &quot;PAExtract&quot;
  },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>7.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetAccountInsurancePolicies</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;GetUserAgencyId&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;showOtherAgency&quot; : &quot;=%showOtherAgency%&quot;,
    &quot;AccountId&quot; : &quot;=%AccountId%&quot;,
    &quot;Folder&quot; : &quot;=&apos;PU_FOLDER&apos;&quot;,
    &quot;Date&quot; : &quot;=$Vlocity.TODAY&quot;
  },
  &quot;bundle&quot; : &quot;UniGetProductsAgency&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>DataRaptor Extract Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetUserAgencyId</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : true,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;userId&quot; : &quot;=$Vlocity.UserId&quot;
  },
  &quot;bundle&quot; : &quot;UniGetUserAgencyId&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Turbo Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>ReturnResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;badgeProdottiSize&quot; : &quot;= IF(ISNOTBLANK(%BadgeProdotti:resp%), LISTSIZE(%BadgeProdotti:resp%), 0)&quot;,
    &quot;badgeProdotti&quot; : &quot;=LIST(%BadgeProdotti:resp%)&quot;
  },
  &quot;sendJSONPath&quot; : &quot;TransformCurrencies_withPolicies&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>8.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformCurrencies_withPolicies</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;GetAccountInsurancePolicies&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : {
    &quot;isAbbinato&quot; : &quot;%isAbbinato:isAbbinato%&quot;,
    &quot;checkCp&quot; : &quot;%checkCp%&quot;
  },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;UniCountAndSumProductsAgency&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>6.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessKey>UniDS_ProdottiAssicurativi</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;transientValues&quot; : {
    &quot;deactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>ProdottiAssicurativi</subType>
    <type>UniDS</type>
    <uniqueName>UniDS_ProdottiAssicurativi_Procedure_4</uniqueName>
    <versionNumber>4.0</versionNumber>
</OmniIntegrationProcedure>
