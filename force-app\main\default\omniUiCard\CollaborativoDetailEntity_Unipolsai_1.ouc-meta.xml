<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Collaborativo_Operation&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;RecordId&quot;:&quot;{recordId}&quot;,&quot;ActionType&quot;:&quot;LoadEntity&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;a1j9O000034CBWvQAO&quot;,&quot;id&quot;:1}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CollaborativoDetailEntity</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;}},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3E%3Cspan%20style=%22font-size:%2012pt;%22%3EEntit&amp;agrave;%20Associate%20(%7BTotale%7D)%3C/span%3E%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;}},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-1&quot;},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;CollaborativoEntityTable&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.List}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-2&quot;}]}},&quot;childCards&quot;:[&quot;CollaborativoEntityTable&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Collaborativo_Operation&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;RecordId&quot;:&quot;{recordId}&quot;,&quot;ActionType&quot;:&quot;LoadEntity&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;a1j9O000034CBWvQAO&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;CollaborativoDetailEntity&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4KICAgICAgICAgICAgICAgIDx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj4KICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:56,&quot;masterLabel&quot;:&quot;CollaborativoDetailEntity&quot;,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}]}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;rowclick&quot;,&quot;channelname&quot;:&quot;CollaborativoDetailEntity&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744717452904-8xta922xr&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744717503150&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Record&quot;:{&quot;targetName&quot;:&quot;Opportunity&quot;,&quot;targetId&quot;:&quot;{action.result.OppId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;rowclick&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}],&quot;isRepeatable&quot;:true}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;Totale&quot;:&quot;55&quot;,&quot;List&quot;:[{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LbomdUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc0kTUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc10bUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc237UAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc2j3UAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc3QbUAJ&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lc7h7UAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LcAblUAF&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lg656UAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lge1ZUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lge1aUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lge1bUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lge1cUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlIVFUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlIVGUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlIVHUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlIVIUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlWOXUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlWOYUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlWOZUA3&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlWOaUAN&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlYdFUAV&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlYdGUAV&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlYdHUAV&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlYdIUAV&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlYdJUAV&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlexBUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlexCUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlexDUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlexEUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlihRUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlihSUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlihTUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000LlihUUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lm0xhUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lm0xiUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lm0xjUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000Lm0xkUAB&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000M1gyDUAR&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000M4B9vUAF&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000M4B9wUAF&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000M4B9xUAF&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting collaborativo&quot;,&quot;Id&quot;:&quot;00U9O00000MVWtNUAX&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting collaborativo&quot;,&quot;Id&quot;:&quot;00U9O00000MVWtOUAX&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting collaborativo&quot;,&quot;Id&quot;:&quot;00U9O00000MVWtPUAX&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting collaborativo&quot;,&quot;Id&quot;:&quot;00U9O00000MVWtQUAX&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000MfxKPUAZ&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000MfxKQUAZ&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000MfxKRUAZ&quot;},{&quot;Name&quot;:&quot;Inserisci oggetto del meeting&quot;,&quot;Id&quot;:&quot;00U9O00000MfxKSUAZ&quot;},{&quot;Name&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;00U9O00000MxtS1UAJ&quot;},{&quot;Name&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;00U9O00000N1xqPUAR&quot;},{&quot;Name&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;00U9O00000N1xqQUAR&quot;},{&quot;Name&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;00U9O00000N1xqRUAR&quot;},{&quot;Name&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;00U9O00000N1xqSUAR&quot;}]}</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
