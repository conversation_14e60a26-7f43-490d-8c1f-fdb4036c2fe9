<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>CollaborativoCreaGruppo/Unipolsai/1.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n    {\n      \&quot;Id\&quot;: \&quot;0059O00000Sh77tQAB\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;<PERSON>\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000IIEdQQAX\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Confman Confman\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000IRxVJQA1\&quot;,\n      \&quot;isSelected\&quot;: true,\n      \&quot;Nome\&quot;: \&quot;Luca Parisi\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000JqlPnQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Davide Imperia\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000JqwMrQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Mario Bianchi\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000Jr08cQAB\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Maria Calvino\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000KXvs1QAD\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Anna Valente\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000PAeXHQA1\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Guy De Roquefeuil\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000PysBiQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Michela Pompei\&quot;\n    }\n  ]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:1}]},&quot;event-0_0&quot;:{&quot;type&quot;:&quot;ApexRemote&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;response\&quot;]&quot;,&quot;remoteClass&quot;:&quot;CollaborativoController&quot;,&quot;remoteMethod&quot;:&quot;checkUser&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;UserId&quot;:&quot; {User.userId}&quot;,&quot;IdRecord&quot;:&quot;{action.result.Id}&quot;,&quot;Records&quot;:&quot;{records}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;action.result.Id\&quot;:\&quot;{action.result.Id}\&quot;,\&quot;records\&quot;:\&quot;{records}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;action.result.Id&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:6},{&quot;name&quot;:&quot;records&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:8}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CollaborativoModificaGruppo</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small&quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_3_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cstrong%3EUtenti%20di%20agenzia%20selezionabili%3C/strong%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;parentElementKey&quot;:&quot;element_block_3_0&quot;,&quot;elementLabel&quot;:&quot;Block-2-Text-0&quot;}],&quot;elementLabel&quot;:&quot;Block-0&quot;},{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;rowLevelEdit&quot;:true,&quot;userSelectableRow&quot;:true,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;Id&quot;,&quot;label&quot;:&quot;Id&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;,&quot;visible&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;isSelected&quot;,&quot;label&quot;:&quot;isSelected&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;checkbox&quot;,&quot;editable&quot;:&quot;true&quot;,&quot;userSelectable&quot;:&quot;true&quot;,&quot;visible&quot;:&quot;true&quot;},{&quot;fieldName&quot;:&quot;Nome&quot;,&quot;label&quot;:&quot;Nome&quot;,&quot;searchable&quot;:false,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;,&quot;editable&quot;:&quot;false&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Datatable-1&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;[\n    {\n      \&quot;Id\&quot;: \&quot;0059O00000Sh77tQAB\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Alessandro Pupi\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000IIEdQQAX\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Confman Confman\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000IRxVJQA1\&quot;,\n      \&quot;isSelected\&quot;: true,\n      \&quot;Nome\&quot;: \&quot;Luca Parisi\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000JqlPnQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Davide Imperia\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000JqwMrQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Mario Bianchi\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000Jr08cQAB\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Maria Calvino\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000KXvs1QAD\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Anna Valente\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000PAeXHQA1\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Guy De Roquefeuil\&quot;\n    },\n    {\n      \&quot;Id\&quot;: \&quot;0059X00000PysBiQAJ\&quot;,\n      \&quot;isSelected\&quot;: false,\n      \&quot;Nome\&quot;: \&quot;Michela Pompei\&quot;\n    }\n  ]&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;CollaborativoModificaGruppo&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;CollaborativoModificaGruppo&quot;,&quot;apiVersion&quot;:62,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;]},&quot;isExplicitImport&quot;:false},&quot;isRepeatable&quot;:false,&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;tablet_l&quot;},&quot;globalCSS&quot;:false,&quot;sessionVars&quot;:[],&quot;events&quot;:[{&quot;eventname&quot;:&quot;selectrow&quot;,&quot;channelname&quot;:&quot;CollaborativoModificaGruppo&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744028804087-4iul5iy6m&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744029048148&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;action.result.isSelected&quot;,&quot;fieldValue&quot;:&quot;{action.result.isSelected}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;selectrow&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>[{&quot;Id&quot;:&quot;0059O00000Sh77tQAB&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Alessandro Pupi&quot;},{&quot;Id&quot;:&quot;0059X00000IIEdQQAX&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Confman Confman&quot;},{&quot;Id&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;isSelected&quot;:true,&quot;Nome&quot;:&quot;Luca Parisi&quot;},{&quot;Id&quot;:&quot;0059X00000JqlPnQAJ&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Davide Imperia&quot;},{&quot;Id&quot;:&quot;0059X00000JqwMrQAJ&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Mario Bianchi&quot;},{&quot;Id&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Maria Calvino&quot;},{&quot;Id&quot;:&quot;0059X00000KXvs1QAD&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Anna Valente&quot;},{&quot;Id&quot;:&quot;0059X00000PAeXHQA1&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Guy De Roquefeuil&quot;},{&quot;Id&quot;:&quot;0059X00000PysBiQAJ&quot;,&quot;isSelected&quot;:false,&quot;Nome&quot;:&quot;Michela Pompei&quot;}]</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;&quot;}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
