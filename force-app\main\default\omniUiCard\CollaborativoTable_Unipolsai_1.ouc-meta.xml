<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CollaborativoTable</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:true,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:false,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;rowDelete&quot;:true,&quot;confirmdeleterow&quot;:true,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;NomeGruppo&quot;,&quot;label&quot;:&quot;Nome Gruppo&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;Descrizione&quot;,&quot;label&quot;:&quot;Descrizione&quot;,&quot;searchable&quot;:&quot;true&quot;,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;NumeroMembri&quot;,&quot;label&quot;:&quot;Numero Membri&quot;,&quot;searchable&quot;:&quot;false&quot;,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;},{&quot;fieldName&quot;:&quot;DataCreazione&quot;,&quot;label&quot;:&quot;Data Creazione&quot;,&quot;searchable&quot;:&quot;false&quot;,&quot;sortable&quot;:true,&quot;editable&quot;:&quot;false&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Datatable-0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:null,&quot;value&quot;:{},&quot;orderBy&quot;:{},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;CollaborativoTable&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;events&quot;:[{&quot;eventname&quot;:&quot;rowclick&quot;,&quot;channelname&quot;:&quot;CollaborativoTable&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743781490273-d2ohvzspl&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1743781532493&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Record&quot;:{&quot;targetId&quot;:&quot;{action.result.Id}&quot;,&quot;targetName&quot;:&quot;Group__c&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;rowclick&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;},{&quot;eventname&quot;:&quot;delete&quot;,&quot;channelname&quot;:&quot;CollaborativoTable&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744296017410-72w9c0us1&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744296183296&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;callActionDelete&quot;,&quot;message&quot;:&quot;callActionDelete&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;PublicGroupId&quot;:&quot;{action.result.PublicGroupId}&quot;,&quot;GroupId&quot;:&quot;{action.result.Id}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-1&quot;,&quot;displayLabel&quot;:&quot;delete&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;}],&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;CollaborativoTable&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;isRepeatable&quot;:false,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfCollaborativoTable_2_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003FBHZSA4&quot;,&quot;MasterLabel&quot;:&quot;cfCollaborativoTable_2_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;}}</propertySetConfig>
    <versionNumber>1</versionNumber>
</OmniUiCard>
