<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\t\t\t\&quot;AgendaCollaborativo\&quot;: \&quot;Non Attiva\&quot;,\n\t\t\t\&quot;DataCreazione\&quot;: \&quot;2025-05-22\&quot;,\n\t\t\t\&quot;Descrizione\&quot;: \&quot;Test 2205\&quot;,\n\t\t\t\&quot;Id\&quot;: \&quot;a1j9O00002eQVT7QAO\&quot;,\n\t\t\t\&quot;isCollaborativoAgenda\&quot;: false,\n\t\t\t\&quot;NomeGruppo\&quot;: \&quot;Test 2205\&quot;,\n\t\t\t\&quot;NumeroMembri\&quot;: 17,\n\t\t\t\&quot;PublicGroupId\&quot;: \&quot;00G9O00000CruYDUAZ\&quot;\n\t\t}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>CollaborativoTable</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Datatable&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:true,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:false,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;rowDelete&quot;:true,&quot;confirmdeleterow&quot;:false,&quot;fireeventOnDeleteconfirm&quot;:true,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;NomeGruppo&quot;,&quot;label&quot;:&quot;Nome Gruppo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;Descrizione&quot;,&quot;label&quot;:&quot;Descrizione&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;NumeroMembri&quot;,&quot;label&quot;:&quot;Numero Membri&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;DataCreazione&quot;,&quot;label&quot;:&quot;Data Creazione&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;AgendaCollaborativo&quot;,&quot;label&quot;:&quot;Agenda Collaborativo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Datatable-0&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\t\t\t\&quot;AgendaCollaborativo\&quot;: \&quot;Non Attiva\&quot;,\n\t\t\t\&quot;DataCreazione\&quot;: \&quot;2025-05-22\&quot;,\n\t\t\t\&quot;Descrizione\&quot;: \&quot;Test 2205\&quot;,\n\t\t\t\&quot;Id\&quot;: \&quot;a1j9O00002eQVT7QAO\&quot;,\n\t\t\t\&quot;isCollaborativoAgenda\&quot;: false,\n\t\t\t\&quot;NomeGruppo\&quot;: \&quot;Test 2205\&quot;,\n\t\t\t\&quot;NumeroMembri\&quot;: 17,\n\t\t\t\&quot;PublicGroupId\&quot;: \&quot;00G9O00000CruYDUAZ\&quot;\n\t\t}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;CollaborativoTable&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;events&quot;:[{&quot;eventname&quot;:&quot;rowclick&quot;,&quot;channelname&quot;:&quot;CollaborativoTable&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1743781490273-d2ohvzspl&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1743781532493&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Record&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Record&quot;:{&quot;targetId&quot;:&quot;{action.result.Id}&quot;,&quot;targetName&quot;:&quot;Group__c&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;rowclick&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;},{&quot;eventname&quot;:&quot;delete&quot;,&quot;channelname&quot;:&quot;CollaborativoTable&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;event&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748954701876-h7fublc9o&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748960204101&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;customLwc&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;close_modal&quot;,&quot;flyoutLwc&quot;:&quot;confirmDialogEliminaGruppoCollaborativo&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;__recordId&quot;:&quot;{action.result.Id}&quot;,&quot;GroupId&quot;:&quot;{action.result.Id}&quot;,&quot;PublicGroupId&quot;:&quot;{action.result.PublicGroupId}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;key&quot;:&quot;event-1&quot;,&quot;displayLabel&quot;:&quot;delete&quot;,&quot;eventLabel&quot;:&quot;custom event&quot;},{&quot;eventname&quot;:&quot;data&quot;,&quot;channelname&quot;:&quot;close_modal&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748955306880-9kze5vxkw&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748955795037&quot;,&quot;type&quot;:&quot;Event&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;subType&quot;:&quot;PubSub&quot;,&quot;eventName&quot;:&quot;close_modal&quot;,&quot;message&quot;:&quot;close&quot;,&quot;hasExtraParams&quot;:true,&quot;extraParams&quot;:{&quot;recordId&quot;:&quot;{action.result.Id}&quot;}},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-2&quot;,&quot;displayLabel&quot;:&quot;close_modal:data&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;},{&quot;eventname&quot;:&quot;data&quot;,&quot;channelname&quot;:&quot;omniscript_action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748966053155-7ozv6vqwp&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748966053336&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-3&quot;,&quot;displayLabel&quot;:&quot;omniscript_action:data&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;CollaborativoTable&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;isRepeatable&quot;:false,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfCollaborativoTable_2_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003FBHZSA4&quot;,&quot;MasterLabel&quot;:&quot;cfCollaborativoTable_2_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-22&quot;,&quot;Descrizione&quot;:&quot;Test 2205&quot;,&quot;Id&quot;:&quot;a1j9O00002eQVT7QAO&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test 2205&quot;,&quot;NumeroMembri&quot;:17,&quot;PublicGroupId&quot;:&quot;00G9O00000CruYDUAZ&quot;}</sampleDataSourceResponse>
    <versionNumber>2</versionNumber>
</OmniUiCard>
