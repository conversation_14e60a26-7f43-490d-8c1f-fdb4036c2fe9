<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Collaborativo_Operation&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;LoadData&quot;,&quot;UserId&quot;:&quot;{User.userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:2}]},&quot;event-0_0&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Collaborativo_Operation&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;DeleteGroup&quot;,&quot;PublicGroupId&quot;:&quot;{action.PublicGroupId}&quot;,&quot;GroupId&quot;:&quot;{action.GroupId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;,\&quot;action.PublicGroupId\&quot;:\&quot;{action.PublicGroupId}\&quot;,\&quot;action.GroupId\&quot;:\&quot;{action.GroupId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:2},{&quot;name&quot;:&quot;action.PublicGroupId&quot;,&quot;val&quot;:&quot;00G9O00000BSxYHUA1&quot;,&quot;id&quot;:4},{&quot;name&quot;:&quot;action.GroupId&quot;,&quot;val&quot;:&quot;a1j9O00002OvVBVQA3&quot;,&quot;id&quot;:6}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>FlexMainCollaborativo</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;children&quot;:[{&quot;key&quot;:&quot;element_element_block_0_0_outputField_0_0&quot;,&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%3E%3Cspan%20style=%22font-size:%2014pt;%22%3EGruppi%3C/span%3E%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;}},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Text-0&quot;},{&quot;key&quot;:&quot;element_element_block_0_0_action_1_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Nuovo Gruppo&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744367297458&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;Collaborativo/Add/Italian&quot;,&quot;flyoutLwc&quot;:&quot;collaborativo-add-italian&quot;},&quot;key&quot;:&quot;1744300594574-wawerkt9r&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;reRenderFlyout&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;outline-brand&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}},&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;&quot;,&quot;reRenderFlyout&quot;:true},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-0-Action-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_6-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;6&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;right&quot;}}},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;around:x-small&quot;}],&quot;class&quot;:&quot;slds-p-around_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#F3F3F3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#F3F3F3;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;name&quot;:&quot;FlexCard&quot;,&quot;element&quot;:&quot;childCardPreview&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;cardName&quot;:&quot;CollaborativoTable&quot;,&quot;recordId&quot;:&quot;{recordId}&quot;,&quot;cardNode&quot;:&quot;{record.response}&quot;,&quot;selectedState&quot;:&quot;Active&quot;,&quot;isChildCardTrackingEnabled&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;FlexCard-1&quot;}]}},&quot;childCards&quot;:[&quot;CollaborativoTable&quot;],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;Collaborativo_Operation&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;ActionType&quot;:&quot;LoadData&quot;,&quot;UserId&quot;:&quot;{User.userId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;User.userId\&quot;:\&quot;{User.userId}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;User.userId&quot;,&quot;val&quot;:&quot;0059X00000IRxVJQA1&quot;,&quot;id&quot;:2}]},&quot;title&quot;:&quot;FlexMainCollaborativo&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;PHRhcmdldENvbmZpZyB0YXJnZXRzPSJsaWdodG5pbmdfX0FwcFBhZ2UiPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICAgICAgICAgICAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICAgICAgICAgICAgICA8L3RhcmdldENvbmZpZz4KICAgICAgICAgICAgICAgIDx0YXJnZXRDb25maWcgdGFyZ2V0cz0ibGlnaHRuaW5nX19SZWNvcmRQYWdlIj4KICAgICAgICAgICAgICAgICAgICA8cHJvcGVydHkgbmFtZT0iZGVidWciIHR5cGU9IkJvb2xlYW4iLz4KICAgICAgICAgICAgICAgIDwvdGFyZ2V0Q29uZmlnPg==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;apiVersion&quot;:56,&quot;masterLabel&quot;:&quot;FlexMainCollaborativo&quot;,&quot;description&quot;:&quot;&quot;,&quot;runtimeNamespace&quot;:&quot;&quot;,&quot;isExposed&quot;:true},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}]}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;callActionDelete&quot;,&quot;channelname&quot;:&quot;callActionDelete&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1744299029727-sczroa3pn&quot;,&quot;label&quot;:&quot;IpDeletegroup&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744299927669&quot;,&quot;type&quot;:&quot;DataAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;message&quot;:&quot;{\&quot;type\&quot;:\&quot;IntegrationProcedures\&quot;,\&quot;value\&quot;:{\&quot;dsDelay\&quot;:\&quot;\&quot;,\&quot;ipMethod\&quot;:\&quot;Collaborativo_Operation\&quot;,\&quot;vlocityAsync\&quot;:false,\&quot;inputMap\&quot;:{\&quot;ActionType\&quot;:\&quot;DeleteGroup\&quot;,\&quot;PublicGroupId\&quot;:\&quot;{action.PublicGroupId}\&quot;,\&quot;GroupId\&quot;:\&quot;{action.GroupId}\&quot;},\&quot;jsonMap\&quot;:\&quot;{\\\&quot;User.userId\\\&quot;:\\\&quot;{User.userId}\\\&quot;,\\\&quot;action.PublicGroupId\\\&quot;:\\\&quot;{action.PublicGroupId}\\\&quot;,\\\&quot;action.GroupId\\\&quot;:\\\&quot;{action.GroupId}\\\&quot;}\&quot;,\&quot;resultVar\&quot;:\&quot;\&quot;},\&quot;orderBy\&quot;:{\&quot;name\&quot;:\&quot;\&quot;,\&quot;isReverse\&quot;:\&quot;\&quot;},\&quot;contextVariables\&quot;:[{\&quot;name\&quot;:\&quot;User.userId\&quot;,\&quot;val\&quot;:\&quot;0059X00000IRxVJQA1\&quot;,\&quot;id\&quot;:2},{\&quot;name\&quot;:\&quot;action.PublicGroupId\&quot;,\&quot;val\&quot;:\&quot;00G9O00000BSxYHUA1\&quot;,\&quot;id\&quot;:4},{\&quot;name\&quot;:\&quot;action.GroupId\&quot;,\&quot;val\&quot;:\&quot;a1j9O00002OvVBVQA3\&quot;,\&quot;id\&quot;:6}]}&quot;},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1744299403729-ltbg16v05&quot;,&quot;label&quot;:&quot;OpenModalDetail&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744299979157&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;childCard&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;cardName&quot;:&quot;CollaborativoDeleteModal&quot;,&quot;flyoutLwc&quot;:&quot;CollaborativoDeleteModal&quot;,&quot;cardNode&quot;:&quot;&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;message&quot;:&quot;{message}&quot;,&quot;groupId&quot;:&quot;{groupId}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1744367035389-a9hjaktme&quot;,&quot;label&quot;:&quot;Reload&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1744367047108&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;success&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;true&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;true&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;callActionDelete:callActionDelete&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;},{&quot;eventname&quot;:&quot;data&quot;,&quot;channelname&quot;:&quot;omniscript_action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1748436908027-cstwvyq1r&quot;,&quot;label&quot;:&quot;SetValue&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748437782019&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;success&quot;,&quot;fieldValue&quot;:&quot;{action.success}&quot;},{&quot;fieldName&quot;:&quot;message&quot;,&quot;fieldValue&quot;:&quot;{action.message}&quot;}]},&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1744636848015-n8zx7j3bo&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1748441113035&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;reload&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;message&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:1}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-1&quot;,&quot;displayLabel&quot;:&quot;omniscript_action:data&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}],&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfCollaborativoDetailsUsers&quot;,&quot;Id&quot;:&quot;0Rb9O000003FOYBSA4&quot;,&quot;MasterLabel&quot;:&quot;cfCollaborativoDetailsUsers&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;}}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;response&quot;:[{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-22&quot;,&quot;Descrizione&quot;:&quot;Test 2205&quot;,&quot;Id&quot;:&quot;a1j9O00002eQVT7QAO&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test 2205&quot;,&quot;NumeroMembri&quot;:17,&quot;PublicGroupId&quot;:&quot;00G9O00000CruYDUAZ&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:&quot;Test Col 1 Desc&quot;,&quot;Id&quot;:&quot;a1j9O00002ejm8TQAQ&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test Col 1&quot;,&quot;NumeroMembri&quot;:2,&quot;PublicGroupId&quot;:&quot;00G9O00000D3fdRUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:&quot;Test RG&quot;,&quot;Id&quot;:&quot;a1j9O00002ejmqMQAQ&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test RG 78&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3mtRUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:&quot;Test RG&quot;,&quot;Id&quot;:&quot;a1j9O00002ejzgnQAA&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test RG&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3mobUAB&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:&quot;-&quot;,&quot;Id&quot;:&quot;a1j9O00002ejznFQAQ&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippo&quot;,&quot;NumeroMembri&quot;:15,&quot;PublicGroupId&quot;:&quot;00G9O00000D3oLlUAJ&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002el3FJQAY&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippoSuccess&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3tbZUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002el3TpQAI&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippo2&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3u9RUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002el5dhQAA&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippo29&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3unlUAB&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002el6OTQAY&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippo11&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3q1zUAB&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-28&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002el6Q5QAI&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;pippo23&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000D3x7JUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-30&quot;,&quot;Descrizione&quot;:&quot;Defect&quot;,&quot;Id&quot;:&quot;a1j9O00002f4g3FQAQ&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Defect&quot;,&quot;NumeroMembri&quot;:0,&quot;PublicGroupId&quot;:&quot;-1&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-05-30&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O00002f4hc1QAA&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;Test Calv&quot;,&quot;NumeroMembri&quot;:0,&quot;PublicGroupId&quot;:&quot;-&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-05&quot;,&quot;Descrizione&quot;:&quot;test&quot;,&quot;Id&quot;:&quot;a1j9O000033pzvpQAA&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;test gruppo&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000FicNYUAZ&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-06&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O000034CBWvQAO&quot;,&quot;isCollaborativoAgenda&quot;:true,&quot;NomeGruppo&quot;:&quot;Test collaborativo&quot;,&quot;NumeroMembri&quot;:5,&quot;PublicGroupId&quot;:&quot;00G9O00000FkHV4UAN&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-08&quot;,&quot;Descrizione&quot;:&quot;Gruppo per testare scenario - gruppo senza utente incluso&quot;,&quot;Id&quot;:&quot;a1j9O000035RwRhQAK&quot;,&quot;isCollaborativoAgenda&quot;:true,&quot;NomeGruppo&quot;:&quot;Test Collaborativo senza Maria Calvino&quot;,&quot;NumeroMembri&quot;:2,&quot;PublicGroupId&quot;:&quot;00G9O00000FpekMUAR&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Non Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-14&quot;,&quot;Descrizione&quot;:null,&quot;Id&quot;:&quot;a1j9O000037w5g5QAA&quot;,&quot;isCollaborativoAgenda&quot;:false,&quot;NomeGruppo&quot;:&quot;test per eliminazione&quot;,&quot;NumeroMembri&quot;:4,&quot;PublicGroupId&quot;:&quot;00G9O00000G2Vo5UAF&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-28&quot;,&quot;Descrizione&quot;:&quot;Gruppo per unit test agenda condivisa&quot;,&quot;Id&quot;:&quot;a1j9O00003EIe8fQAD&quot;,&quot;isCollaborativoAgenda&quot;:true,&quot;NomeGruppo&quot;:&quot;Test Collaborativo 28/08&quot;,&quot;NumeroMembri&quot;:4,&quot;PublicGroupId&quot;:&quot;00G9O00000GVepZUAT&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-28&quot;,&quot;Descrizione&quot;:&quot;descrizione collaborativo&quot;,&quot;Id&quot;:&quot;a1j9O00003EPnWzQAL&quot;,&quot;isCollaborativoAgenda&quot;:true,&quot;NomeGruppo&quot;:&quot;test domenico&quot;,&quot;NumeroMembri&quot;:2,&quot;PublicGroupId&quot;:&quot;00G9O00000GWhlNUAT&quot;},{&quot;AgendaCollaborativo&quot;:&quot;Attiva&quot;,&quot;DataCreazione&quot;:&quot;2025-08-29&quot;,&quot;Descrizione&quot;:&quot;-&quot;,&quot;Id&quot;:&quot;a1j9O00003EsylZQAR&quot;,&quot;isCollaborativoAgenda&quot;:true,&quot;NomeGruppo&quot;:&quot;Domenico coll&quot;,&quot;NumeroMembri&quot;:1,&quot;PublicGroupId&quot;:&quot;00G9O00000GZ0NKUA1&quot;}],&quot;success&quot;:&quot;&quot;,&quot;message&quot;:&quot;&quot;,&quot;groupId&quot;:&quot;&quot;}</sampleDataSourceResponse>
    <versionNumber>1</versionNumber>
</OmniUiCard>
