<?xml version="1.0" encoding="UTF-8"?>
<Report xmlns="http://soap.sforce.com/2006/04/metadata">
    <aggregates>
        <calculatedFormula>(Case.CCVendite__c:SUM / Case.ActualCalls__c:SUM) * 100</calculatedFormula>
        <datatype>percent</datatype>
        <developerName>FORMULA1</developerName>
        <downGroupingContext>Case$Owner</downGroupingContext>
        <isActive>true</isActive>
        <isCrossBlock>false</isCrossBlock>
        <masterLabel>Conversion rate per vendita</masterLabel>
        <scale>2</scale>
    </aggregates>
    <aggregates>
        <calculatedFormula>(VoiceCall.CallEndDateTime:UNIQUE - Case.CC_Data_Inserimento_Esito__c:UNIQUE )</calculatedFormula>
        <datatype>number</datatype>
        <developerName>FORMULA2</developerName>
        <downGroupingContext>Case$Owner</downGroupingContext>
        <isActive>true</isActive>
        <isCrossBlock>false</isCrossBlock>
        <masterLabel>Tempo medio esitazione finale</masterLabel>
        <scale>2</scale>
    </aggregates>
    <aggregates>
        <calculatedFormula>SUM(
  IF(TEXT(Case.Esito__c:UNIQUE) = &quot;VENDITA&quot;, 1, 0)
) / RowCount * 100</calculatedFormula>
        <datatype>percent</datatype>
        <developerName>FORMULA3</developerName>
        <downGroupingContext>Case$Owner</downGroupingContext>
        <isActive>true</isActive>
        <isCrossBlock>false</isCrossBlock>
        <masterLabel>CR Posizione assicurativa</masterLabel>
        <scale>2</scale>
    </aggregates>
    <aggregates>
        <calculatedFormula>(SUM(IF(TEXT(Case.Esito__c:UNIQUE) = &quot;fuori SLA&quot;, 1, 0)) / RowCount) * 100</calculatedFormula>
        <datatype>percent</datatype>
        <developerName>FORMULA4</developerName>
        <downGroupingContext>Case$Owner</downGroupingContext>
        <isActive>true</isActive>
        <isCrossBlock>false</isCrossBlock>
        <masterLabel>% Fuori SLA</masterLabel>
        <scale>2</scale>
    </aggregates>
    <aggregates>
        <calculatedFormula>(Case.CCVendite__c:SUM / Case.CC_Ore_Lavorate__c:UNIQUE)</calculatedFormula>
        <datatype>number</datatype>
        <developerName>FORMULA5</developerName>
        <downGroupingContext>Case$Owner</downGroupingContext>
        <isActive>true</isActive>
        <isCrossBlock>false</isCrossBlock>
        <masterLabel>Vendite su ore lavorate</masterLabel>
        <scale>2</scale>
    </aggregates>
    <columns>
        <aggregateTypes>Average</aggregateTypes>
        <field>Case$ActualCalls__c</field>
    </columns>
    <columns>
        <aggregateTypes>Average</aggregateTypes>
        <field>Case.Voice_Calls__r$CallDurationInSeconds</field>
    </columns>
    <columns>
        <aggregateTypes>Average</aggregateTypes>
        <field>Case$CC_Tempo_medio_presa_in_carico__c</field>
    </columns>
    <format>Summary</format>
    <groupingsDown>
        <dateGranularity>Day</dateGranularity>
        <field>Case$Owner</field>
        <sortOrder>Asc</sortOrder>
    </groupingsDown>
    <name>CC Tabella Performance Operatore</name>
    <params>
        <name>co</name>
        <value>0</value>
    </params>
    <reportType>CC_Performance__c</reportType>
    <scope>organization</scope>
    <showDetails>true</showDetails>
    <showGrandTotal>true</showGrandTotal>
    <showSubTotals>true</showSubTotals>
    <timeFrameFilter>
        <dateColumn>Case$StartDate__c</dateColumn>
        <interval>INTERVAL_CURFY</interval>
    </timeFrameFilter>
</Report>
