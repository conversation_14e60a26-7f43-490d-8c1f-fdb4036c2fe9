trigger CaseTrigger on Case(before insert, after insert, after update, before update) {
    CaseTriggerHandler handler = new CaseTriggerHandler();
    CaseNotificationHandler handlerNotify = new CaseNotificationHandler();
    
    if (SkipTrigger__c.getInstance(UserInfo.getUserId()).SkipCase__c) {
        return;
    }

    if (Trigger.isBefore && Trigger.isInsert) {
        /*Spostato in handler
        User u = [SELECT Id,Name FROM User WHERE (Name = :System.Label.OwnerId OR Name = :System.Label.OwnerId2) LIMIT 1];
        for (Case obj : Trigger.new) {
            obj.OwnerId = u.Id;
            System.debug('>>>> CaseTrigger.before.insert: ' + obj.OwnerId);
        }*/
        handler.onBeforeInsert(Trigger.new);
    }

    if (Trigger.isAfter && (Trigger.isInsert || Trigger.isUpdate)) {
        if(Trigger.isInsert){
            handler.onAfterInsert(Trigger.new);
        }
        handler.onAfterInsOrUpd(Trigger.new, Trigger.newMap, Trigger.oldMap);

        List<Case> filteredList = handler.filterCasesByRecordType(Trigger.new);
        if (!filteredList.isEmpty()) {
            if(Trigger.isInsert){
                handlerNotify.notificationManagement(filteredList, Trigger.newMap, Trigger.oldMap, 'Creation');
            }else if(Trigger.isUpdate){
                handlerNotify.notificationManagement(filteredList, Trigger.newMap, Trigger.oldMap, 'Reassignment');
            }
        }  

    }

    if (Trigger.isBefore && (Trigger.isInsert || Trigger.isUpdate)) {
        handler.onBeforeInsOrUpd(Trigger.new);
    }

    if (Trigger.isBefore && Trigger.isUpdate) {
        handler.onBeforeUpdate(Trigger.new, Trigger.oldMap);
    }
}