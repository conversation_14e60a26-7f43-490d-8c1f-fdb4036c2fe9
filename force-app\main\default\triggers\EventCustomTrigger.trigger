trigger EventCustomTrigger on Event (after insert, before update, after update, before delete, after delete) {

    EventTriggerHandler handler = new EventTriggerHandler();
        
    if (Trigger.isAfter && Trigger.isInsert) {
        handler.notificationManagement(Trigger.new,Trigger.newMap, Trigger.oldMap, 'Creation');
        handler.collaborativeEventHandler(Trigger.new,Trigger.newMap, Trigger.oldMap);
    }
    if (Trigger.isBefore && Trigger.isUpdate) {
        Boolean isUpdateBlocked = false;
        for (Event e: Trigger.new) {
        	isUpdateBlocked = handler.shouldBlockUpdate(e, Trigger.oldMap.get(e.id));
            if (isUpdateBlocked) {
                if (e.Group__c != null ) {
                    e.addError(Label.EventCollaborativoUpdateCustomError);
                } else {
                	e.addError(Label.EventUpdateCustomError);
                }
                return;
            }
        }
    }
    
    if (Trigger.isBefore && Trigger.isDelete) {
        Boolean isDeleteBlocked = false;
        for (Event e: Trigger.old) {
        	isDeleteBlocked = handler.shouldBlockDelete(e);
            if (isDeleteBlocked) {
                if (e.Group__c != null ) {
                	e.addError(Label.EventCollaborativoDeleteCustomError);
                } else {   
                	e.addError(Label.EventDeleteCustomError);
               	}
                return;
            }
        }
    }
	 
   	if (Trigger.isAfter && Trigger.isUpdate) {
        handler.notificationManagement(Trigger.new,Trigger.newMap, Trigger.oldMap, 'Edit');
        handler.collaborativeEventHandler(Trigger.new,Trigger.newMap, Trigger.oldMap);
        handler.updateLinkGoogleMeetForCollaborativo(Trigger.new,Trigger.newMap, Trigger.oldMap);
        handler.deleteEventCollabolativoAfterPresaInCarico (Trigger.new,Trigger.newMap, Trigger.oldMap);
    }
   	if (Trigger.isAfter && Trigger.isDelete) {
    	handler.notificationManagement(Trigger.new,Trigger.newMap, Trigger.oldMap, 'Delete');    
  	}
}