<?xml version="1.0" encoding="UTF-8"?>
<Dashboard xmlns="http://soap.sforce.com/2006/04/metadata">
    <backgroundEndColor>#FFFFFF</backgroundEndColor>
    <backgroundFadeDirection>Diagonal</backgroundFadeDirection>
    <backgroundStartColor>#FFFFFF</backgroundStartColor>
    <chartTheme>light</chartTheme>
    <colorPalette>unity</colorPalette>
    <dashboardChartTheme>light</dashboardChartTheme>
    <dashboardColorPalette>unity</dashboardColorPalette>
    <dashboardFilters>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Art. 94</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Assicurazione</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Auto in preassegnazione</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Auto sostitutiva</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Circolazione all&apos;estero</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Contratto</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Fatturazione</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Gestione interventi</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Gestione sinistri</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Logistica veicolo</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Richiesta info per acquisto del mezzo</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Servizi amministrativi</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Soccorso e recupero</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Supporto Area Riservata</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Variazioni contrattuali</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Chiusura Contratto</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Consegna Veicolo Nuovo</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Consegna Veicolo Preassegnazione</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Consegna Veicolo Sostitutiva</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Manutenzione Veicolo</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Pneumatici</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Sinistro - carrozzeria</values>
        </dashboardFilterOptions>
        <dashboardFilterOptions>
            <operator>equals</operator>
            <values>Reclamo Soccorso Stradale</values>
        </dashboardFilterOptions>
        <name>Categoria</name>
    </dashboardFilters>
    <dashboardGridLayout>
        <dashboardGridComponents>
            <colSpan>9</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>true</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <componentType>Bar</componentType>
                <dashboardFilterColumns>
                    <column>Case.Categoria__c</column>
                </dashboardFilterColumns>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                    <groupingSorts>
                        <groupingLevel>g2</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Totalità dei Case Unipol Rental</header>
                <legendPosition>Right</legendPosition>
                <report>urcs_ReportFolderResponsabili/Totalit_dei_Case_Unipol_Rental_W4M</report>
                <showPercentage>false</showPercentage>
                <showPicturesOnCharts>false</showPicturesOnCharts>
                <showValues>false</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <sortLegendValues>true</sortLegendValues>
                <useReportChart>true</useReportChart>
            </dashboardComponent>
            <rowIndex>0</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <dashboardGridComponents>
            <colSpan>9</colSpan>
            <columnIndex>0</columnIndex>
            <dashboardComponent>
                <autoselectColumnsFromReport>false</autoselectColumnsFromReport>
                <chartAxisRange>Auto</chartAxisRange>
                <chartSummary>
                    <axisBinding>y</axisBinding>
                    <column>RowCount</column>
                </chartSummary>
                <componentType>BarStacked</componentType>
                <dashboardFilterColumns>
                    <column>Case.Categoria__c</column>
                </dashboardFilterColumns>
                <decimalPrecision>-1</decimalPrecision>
                <displayUnits>Auto</displayUnits>
                <drillEnabled>false</drillEnabled>
                <drillToDetailEnabled>false</drillToDetailEnabled>
                <enableHover>false</enableHover>
                <expandOthers>false</expandOthers>
                <groupingColumn>STATUS</groupingColumn>
                <groupingColumn>ORIGIN</groupingColumn>
                <groupingSortProperties>
                    <groupingSorts>
                        <groupingLevel>g1</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                    <groupingSorts>
                        <groupingLevel>g2</groupingLevel>
                        <sortOrder>a</sortOrder>
                    </groupingSorts>
                </groupingSortProperties>
                <header>Totalità dei Case Chiusi Unipol Rental</header>
                <legendPosition>Right</legendPosition>
                <report>urcs_ReportFolderResponsabili/Totalit_dei_Case_Chiusi_Unipol_Rental_dtb</report>
                <showPercentage>false</showPercentage>
                <showValues>false</showValues>
                <sortBy>RowLabelAscending</sortBy>
                <sortLegendValues>true</sortLegendValues>
                <useReportChart>false</useReportChart>
            </dashboardComponent>
            <rowIndex>8</rowIndex>
            <rowSpan>8</rowSpan>
        </dashboardGridComponents>
        <numberOfColumns>9</numberOfColumns>
        <rowHeight>36</rowHeight>
    </dashboardGridLayout>
    <dashboardType>LoggedInUser</dashboardType>
    <isGridLayout>true</isGridLayout>
    <textColor>#000000</textColor>
    <title>Service Dashboard</title>
    <titleColor>#000000</titleColor>
    <titleSize>12</titleSize>
</Dashboard>
