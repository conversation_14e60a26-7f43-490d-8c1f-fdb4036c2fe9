<?xml version="1.0" encoding="UTF-8"?>
<FlexiPage xmlns="http://soap.sforce.com/2006/04/metadata">
    <flexiPageRegions>
        <name>Facet-dc83a1a3-9a20-47a1-8086-8472d3d025e1</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>body</name>
                    <value>Facet-dc83a1a3-9a20-47a1-8086-8472d3d025e1</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>colPadding</name>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>colWidth</name>
                    <value>12</value>
                </componentInstanceProperties>
                <componentName>flexipage:gridColumn</componentName>
                <identifier>flexipage_gridColumn3</identifier>
            </componentInstance>
        </itemInstances>
        <name>Facet-3c5323ac-cee0-4f7f-9bca-ef1edacdedd0</name>
        <type>Facet</type>
    </flexiPageRegions>
    <flexiPageRegions>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>decorate</name>
                    <value>true</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>richTextValue</name>
                    <value>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;﻿&lt;/span&gt;&lt;strong style=&quot;font-size: 16px;&quot;&gt;Stiamo preparando per te una nuova &lt;em&gt;pagina iniziale&lt;/em&gt; con dashboard personalizzate. Disponibile da novembre.&lt;/strong&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Oggi puoi già lavorare su tutte le funzionalità del CRM: cerca con la Global Search o esplora le schede (Account, Contratti, Asset, Attività operative).&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;font-size: 16px;&quot;&gt;Buon lavoro!&lt;/span&gt;&lt;/p&gt;</value>
                </componentInstanceProperties>
                <componentName>flexipage:richText</componentName>
                <identifier>flexipage_richText</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.urcs_DashboardResponsabiliCC}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>false</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>dashboardName</name>
                    <value>zEbfBYgzXMNRhSeCWAvMTJrSOgjrvs</value>
                </componentInstanceProperties>
                <componentInstanceProperties>
                    <name>hideOnError</name>
                    <value>false</value>
                </componentInstanceProperties>
                <componentName>desktopDashboards:embeddedDashboard</componentName>
                <identifier>desktopDashboards_embeddedDashboard</identifier>
                <visibilityRule>
                    <criteria>
                        <leftValue>{!$Permission.CustomPermission.urcs_DashboardResponsabiliCC}</leftValue>
                        <operator>EQUAL</operator>
                        <rightValue>true</rightValue>
                    </criteria>
                </visibilityRule>
            </componentInstance>
        </itemInstances>
        <itemInstances>
            <componentInstance>
                <componentInstanceProperties>
                    <name>columns</name>
                    <value>Facet-3c5323ac-cee0-4f7f-9bca-ef1edacdedd0</value>
                </componentInstanceProperties>
                <componentName>flexipage:grid</componentName>
                <identifier>flexipage_grid2</identifier>
            </componentInstance>
        </itemInstances>
        <name>main</name>
        <type>Region</type>
    </flexiPageRegions>
    <masterLabel>urcs_CustomerServiceHomePage</masterLabel>
    <template>
        <name>industries_common:homeTemplateOneRegion</name>
    </template>
    <type>HomePage</type>
</FlexiPage>
