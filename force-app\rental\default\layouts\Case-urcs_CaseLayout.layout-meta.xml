<?xml version="1.0" encoding="UTF-8"?>
<Layout xmlns="http://soap.sforce.com/2006/04/metadata">
    <emailDefault>false</emailDefault>
    <excludeButtons>AddToActionableList</excludeButtons>
    <excludeButtons>AssignRecordLabel</excludeButtons>
    <excludeButtons>CaseHierarchy</excludeButtons>
    <excludeButtons>ChangeOwnerOne</excludeButtons>
    <excludeButtons>ChangeRecordType</excludeButtons>
    <excludeButtons>Clone</excludeButtons>
    <excludeButtons>CloseCase</excludeButtons>
    <excludeButtons>CreateGroupAction</excludeButtons>
    <excludeButtons>Delete</excludeButtons>
    <excludeButtons>Edit</excludeButtons>
    <excludeButtons>MassAccept</excludeButtons>
    <excludeButtons>MergeCase</excludeButtons>
    <excludeButtons>OpenSlackRecordChannel</excludeButtons>
    <excludeButtons>PrintableView</excludeButtons>
    <excludeButtons>RecordShareHierarchy</excludeButtons>
    <excludeButtons>SendSurveyInvitation</excludeButtons>
    <excludeButtons>Share</excludeButtons>
    <excludeButtons>StartOutboundConversation</excludeButtons>
    <excludeButtons>Submit</excludeButtons>
    <feedLayout>
        <autocollapsePublisher>true</autocollapsePublisher>
        <compactFeed>true</compactFeed>
        <feedFilterPosition>LeftFixed</feedFilterPosition>
        <feedFilters>
            <feedFilterType>AllUpdates</feedFilterType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>EmailMessageEvent</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>CallLogPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>TextPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ChangeStatusPost</feedItemType>
        </feedFilters>
        <feedFilters>
            <feedFilterType>FeedItemType</feedFilterType>
            <feedItemType>ActivityEvent</feedItemType>
        </feedFilters>
        <fullWidthFeed>true</fullWidthFeed>
        <hideSidebar>true</hideSidebar>
        <highlightExternalFeedItems>true</highlightExternalFeedItems>
        <rightComponents>
            <componentType>HelpAndToolLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Following</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Followers</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>Topics</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomLinks</componentType>
        </rightComponents>
        <rightComponents>
            <componentType>CustomButtons</componentType>
        </rightComponents>
        <useInlineFiltersInConsole>true</useInlineFiltersInConsole>
    </feedLayout>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>true</detailHeading>
        <editHeading>true</editHeading>
        <label>Additional Information</label>
        <layoutColumns>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Subject</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Status</field>
            </layoutItems>
            <layoutItems>
                <behavior>Required</behavior>
                <field>Origin</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Priority</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>Description</field>
            </layoutItems>
        </layoutColumns>
        <layoutColumns>
            <layoutItems>
                <behavior>Readonly</behavior>
                <field>SuppliedEmail</field>
            </layoutItems>
            <layoutItems>
                <behavior>Edit</behavior>
                <field>ContactId</field>
            </layoutItems>
        </layoutColumns>
        <style>TwoColumnsLeftToRight</style>
    </layoutSections>
    <layoutSections>
        <customLabel>false</customLabel>
        <detailHeading>false</detailHeading>
        <editHeading>false</editHeading>
        <label>Custom Links</label>
        <layoutColumns/>
        <layoutColumns/>
        <layoutColumns/>
        <style>CustomLinks</style>
    </layoutSections>
    <platformActionList>
        <actionListContext>Record</actionListContext>
        <platformActionListItems>
            <actionName>Case.urcs_LogACall</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>0</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.urcs_SendEmail</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>1</sortOrder>
        </platformActionListItems>
        <platformActionListItems>
            <actionName>Case.urcs_NuovaOperazione</actionName>
            <actionType>QuickAction</actionType>
            <sortOrder>2</sortOrder>
        </platformActionListItems>
    </platformActionList>
    <quickActionList/>
    <relatedContent>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>ContactId</field>
            </layoutItem>
        </relatedContentItems>
        <relatedContentItems>
            <layoutItem>
                <behavior>Readonly</behavior>
                <field>AccountId</field>
            </layoutItem>
        </relatedContentItems>
    </relatedContent>
    <relatedLists>
        <relatedList>RelatedEntityHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>TASK.STATUS</fields>
        <fields>TASK.PRIORITY</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <relatedList>RelatedActivityList</relatedList>
    </relatedLists>
    <relatedLists>
        <excludeButtons>MailMerge</excludeButtons>
        <excludeButtons>ViewAll</excludeButtons>
        <fields>TASK.SUBJECT</fields>
        <fields>TASK.WHO_NAME</fields>
        <fields>ACTIVITY.TASK</fields>
        <fields>TASK.DUE_DATE</fields>
        <fields>CORE.USERS.FULL_NAME</fields>
        <fields>TASK.LAST_UPDATE</fields>
        <relatedList>RelatedHistoryList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedContentNoteList</relatedList>
    </relatedLists>
    <relatedLists>
        <relatedList>RelatedFileList</relatedList>
    </relatedLists>
    <relatedObjects>ContactId</relatedObjects>
    <runAssignmentRulesDefault>false</runAssignmentRulesDefault>
    <showEmailCheckbox>true</showEmailCheckbox>
    <showHighlightsPanel>true</showHighlightsPanel>
    <showInteractionLogPanel>false</showInteractionLogPanel>
    <showKnowledgeComponent>false</showKnowledgeComponent>
    <showRunAssignmentRulesCheckbox>true</showRunAssignmentRulesCheckbox>
    <showSubmitAndAttachButton>true</showSubmitAndAttachButton>
    <summaryLayout>
        <masterLabel>00h9O000009WOTT</masterLabel>
        <sizeX>3</sizeX>
        <sizeY>4</sizeY>
        <summaryLayoutItems>
            <field>ContactId</field>
            <posX>0</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CaseNumber</field>
            <posX>1</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>CreatedDate</field>
            <posX>1</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Subject</field>
            <posX>1</posX>
            <posY>2</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Description</field>
            <posX>1</posX>
            <posY>3</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Status</field>
            <posX>2</posX>
            <posY>0</posY>
        </summaryLayoutItems>
        <summaryLayoutItems>
            <field>Priority</field>
            <posX>2</posX>
            <posY>1</posY>
        </summaryLayoutItems>
        <summaryLayoutStyle>CaseInteraction</summaryLayoutStyle>
    </summaryLayout>
</Layout>
