------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
The purpose of this manual procedure is to Add FLV For_CorrelationId Field In Entity InsurancePolicyNPI
For Profiles "Unipol Admin Light", "INT - Mulesoft", "INT - Mulesoft Batch"
and in Permission set "System Admin PS"
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------

1. Login to Salesforce
2. Click on the settings icon at the top right and go to the setup item
3. In Quick Find write "Profiles" and click on it
4. From the list select letter "U" and click on the name of Profile "Unipol Admin Light", 
5. Then go on section "Custom Field-Level Security"  and click in the view link for the object "Insurance Policy Non Public Information"
6. Then click Edit button and check the edit checkbox for the field CorrelationId
7. Then click on Save button
8. Repeat steps 3-7 for Profile "INT - Mulesoft" and "INT - Mulesoft Batch"
9. Then in Quick Find write "Permission Sets" and click on it
10. From the list select letter "S" and click on the name of PermissionSet "System Admin PS"
11. Then click on link "Object Settings"  and click in the name-link of the object "Insurance Policy Non Public Information"
12. Then click Edit button and check the edit checkbox for the field "CorrelationId"
13. Then click on Save button